# AI代码分析大盘 API 接口文档

## 📊 概述

AI代码分析大盘提供全面的AI代码使用情况统计和可视化数据，支持多维度筛选和趋势分析。

## 🔗 接口信息

### 基础信息
- **接口名称**: AI代码分析大盘
- **请求方法**: GET
- **接口地址**: `https://faas-common.inf.test.sankuai.com/api/function/node/{FUNCTION_ID}`
- **Content-Type**: `application/json`
- **支持CORS**: ✅

### 云函数配置
- **函数名**: `AICodingDashboard`
- **环境**: test
- **超时时间**: 30秒

## 📝 请求参数

### Query Parameters

| 参数名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| `timeRange` | string | 否 | `30d` | 时间范围 | `7d`, `30d`, `90d`, `custom` |
| `startDate` | string | 否 | - | 自定义开始日期 (当timeRange=custom时) | `2025-05-20` |
| `endDate` | string | 否 | - | 自定义结束日期 (当timeRange=custom时) | `2025-05-26` |
| `repository` | string | 否 | - | 仓库名称筛选 (支持模糊匹配) | `ai-coding` |
| `developer` | string | 否 | - | 开发者筛选 (支持模糊匹配) | `yaoyan` |
| `fileType` | string | 否 | - | 文件类型筛选 (支持模糊匹配) | `tsx` |

### 时间范围说明
- `7d`: 最近7天
- `30d`: 最近30天  
- `90d`: 最近90天
- `custom`: 自定义时间范围 (需要同时提供startDate和endDate)

## 📤 响应格式

### 成功响应 (200)

```json
{
  "success": true,
  "message": "大盘数据获取成功",
  "data": {
    "overview": {
      "totalPRs": 1234,
      "avgAIRatio": 68.2,
      "activeRepos": 45,
      "activeDevelopers": 89,
      "trends": {
        "prGrowth": 12.5,
        "aiRatioGrowth": 5.3
      }
    },
    "trends": {
      "daily": [
        {
          "date": "2025-05-25",
          "aiRatio": 68.2,
          "prCount": 52
        }
      ]
    },
    "rankings": {
      "repositories": [
        {
          "name": "ai-coding-admin",
          "fullName": "*******************/ai-coding-admin.git",
          "aiRatio": 85.2,
          "prCount": 123,
          "totalAILines": 15234
        }
      ],
      "developers": [
        {
          "name": "yaoyan03",
          "aiLines": 15234,
          "totalLines": 18567,
          "aiRatio": 82.1,
          "prCount": 45
        }
      ],
      "fileTypes": [
        {
          "type": "tsx",
          "aiRatio": 85.2,
          "fileCount": 1234,
          "totalAILines": 8765
        }
      ]
    },
    "distributions": {
      "aiRatioRanges": [
        {
          "range": "80-100%",
          "count": 567
        }
      ],
      "detectionMethods": [
        {
          "method": "行级匹配",
          "count": 567,
          "accuracy": 92.3
        }
      ],
      "aiSources": [
        {
          "source": "copilot",
          "count": 1234,
          "avgConfidence": 95.2
        }
      ]
    },
    "recentActivities": [
      {
        "timestamp": "2025-05-26T10:30:00Z",
        "type": "analysis_completed",
        "prId": "13073644",
        "prTitle": "feat: 新增AI代码分析功能",
        "aiRatio": 68.2,
        "developer": "yaoyan03",
        "repository": "ai-coding-admin"
      }
    ],
    "metadata": {
      "generatedAt": "2025-05-26T10:30:00.000Z",
      "timeRange": "30d",
      "filters": {
        "repository": null,
        "developer": null,
        "fileType": null
      }
    }
  }
}
```

### 错误响应 (500)

```json
{
  "success": false,
  "message": "获取大盘数据失败",
  "error": "数据库连接失败"
}
```

## 📊 数据字段说明

### Overview (总览数据)
- `totalPRs`: 总PR数量
- `avgAIRatio`: 平均AI代码占比 (百分比)
- `activeRepos`: 活跃仓库数量
- `activeDevelopers`: 活跃开发者数量
- `trends.prGrowth`: PR数量增长率 (相比上期)
- `trends.aiRatioGrowth`: AI占比增长率 (相比上期)

### Trends (趋势数据)
- `daily`: 按日期统计的趋势数据数组
  - `date`: 日期 (YYYY-MM-DD 或 YYYY-WW 格式)
  - `aiRatio`: 当日平均AI占比
  - `prCount`: 当日PR数量

### Rankings (排行榜数据)
- `repositories`: 仓库排行榜 (按AI占比排序，Top 10)
- `developers`: 开发者排行榜 (按AI代码行数排序，Top 10)  
- `fileTypes`: 文件类型排行榜 (按AI占比排序，Top 10)

### Distributions (分布数据)
- `aiRatioRanges`: AI占比区间分布
- `detectionMethods`: 检测方法分布和准确率
- `aiSources`: AI来源分布 (来自历史记录表)

### Recent Activities (最近活动)
- 最近10条分析完成的活动记录
- 按分析时间倒序排列

## 🌐 请求示例

### 1. 获取基础大盘数据
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID"
```

### 2. 获取最近7天数据
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?timeRange=7d"
```

### 3. 自定义时间范围
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?timeRange=custom&startDate=2025-05-20&endDate=2025-05-26"
```

### 4. 按仓库筛选
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?repository=ai-coding&timeRange=30d"
```

### 5. 按开发者筛选
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?developer=yaoyan&timeRange=30d"
```

### 6. 按文件类型筛选
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?fileType=tsx&timeRange=30d"
```

### 7. 组合筛选条件
```bash
curl "https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?timeRange=30d&repository=ai-coding&developer=yaoyan&fileType=tsx"
```

## 🔧 JavaScript 调用示例

### 使用 fetch
```javascript
async function getDashboardData(params = {}) {
  const queryString = new URLSearchParams(params).toString();
  const url = `https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID?${queryString}`;
  
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.success) {
      console.log('大盘数据:', data.data);
      return data.data;
    } else {
      console.error('获取失败:', data.message);
    }
  } catch (error) {
    console.error('请求异常:', error);
  }
}

// 使用示例
getDashboardData({ timeRange: '7d', repository: 'ai-coding' });
```

### 使用 axios
```javascript
import axios from 'axios';

const dashboardAPI = axios.create({
  baseURL: 'https://faas-common.inf.test.sankuai.com/api/function/node/YOUR_FUNCTION_ID',
  timeout: 30000
});

async function getDashboardData(params = {}) {
  try {
    const response = await dashboardAPI.get('/', { params });
    return response.data.data;
  } catch (error) {
    console.error('获取大盘数据失败:', error);
    throw error;
  }
}
```

## 📈 数据库架构

### 主数据库 (swift)
- `ai_code_analysis_reports`: AI代码分析报告主表
- `ai_code_analysis_file_details`: AI代码分析文件详情表

### 历史数据库 (GrowthDistributionCore)  
- `ai_code_gen_records`: AI代码历史记录表

## ⚠️ 注意事项

1. **性能优化**: 大盘查询涉及多表联查，建议合理设置时间范围
2. **数据实时性**: 数据基于已存储的分析报告，实时性取决于报告提交频率
3. **筛选条件**: 支持模糊匹配，可以使用部分关键词进行筛选
4. **时区处理**: 所有时间均为UTC时间，前端需要根据需要转换为本地时间
5. **错误处理**: 建议在前端实现适当的错误处理和重试机制

## 🚀 部署信息

- **测试环境**: `https://faas-common.inf.test.sankuai.com`
- **函数名**: `AICodingDashboard`
- **部署命令**: `nest deploy -e test`
- **日志查看**: SSH到服务器查看 `/opt/logs/com.sankuai.dzufebiz.manage/` 目录下的日志文件
