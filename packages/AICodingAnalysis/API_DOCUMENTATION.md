# AI代码分析结果管理 API 文档

## 概述

`AICodingAnalysisResult` 函数提供了AI代码分析结果的存储和查询功能，支持GET（分页查询）和POST（存储数据）两种操作。

## 接口地址

- **函数名**: `AICodingAnalysisResult`
- **支持方法**: GET, POST, OPTIONS
- **基础URL**: `https://your-domain.com/AICodingAnalysisResult`
- **跨域支持**: ✅ 支持 CORS，允许所有域名访问

## 数据库表结构

### 主表：ai_code_analysis_reports
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID，自增 |
| pr_global_id | varchar(50) | PR全局ID，唯一 |
| pr_title | varchar(500) | PR标题 |
| git_repository | varchar(500) | Git仓库地址 |
| git_branch | varchar(200) | Git分支名 |
| mis_number | varchar(50) | MIS号/作者 |
| detection_method | varchar(50) | 检测方法 |
| analysis_timestamp | datetime | 分析时间戳 |
| total_files | int | 总文件数 |
| total_lines | int | 总行数 |
| changed_lines | int | 变更行数 |
| ai_generated_lines | int | AI生成行数 |
| ai_code_ratio | decimal(10,8) | AI代码占比 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 详情表：ai_code_analysis_file_details
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID，自增 |
| report_id | bigint | 报告ID，关联主表 |
| pr_global_id | varchar(50) | PR全局ID，冗余字段 |
| file_name | varchar(1000) | 文件名/路径 |
| file_type | varchar(20) | 文件类型 |
| total_lines | int | 文件总行数 |
| changed_lines | int | 文件变更行数 |
| ai_matched_lines | int | AI匹配行数 |
| ai_code_ratio | decimal(10,8) | 文件AI代码占比 |
| detection_method | varchar(50) | 文件检测方法 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

---

## 1. GET 请求 - 分页查询分析结果

### 1.1 接口说明
- **方法**: GET
- **功能**: 分页查询AI代码分析结果，支持多条件搜索
- **URL**: `GET /AICodingAnalysisResult`

### 1.2 请求示例

#### 基础查询
```bash
GET /AICodingAnalysisResult?page=1&pageSize=20
```

#### 按MIS号搜索
```bash
GET /AICodingAnalysisResult?page=1&pageSize=10&misNumber=zhangsan
```

#### 按时间范围搜索
```bash
GET /AICodingAnalysisResult?page=1&pageSize=10&startTime=2024-01-01T00:00:00.000Z&endTime=2024-12-31T23:59:59.999Z
```

#### 按仓库和分支搜索
```bash
GET /AICodingAnalysisResult?page=1&pageSize=10&gitRepository=project&gitBranch=main
```

#### 按PR ID搜索
```bash
GET /AICodingAnalysisResult?page=1&pageSize=10&prId=PR-2024
```

#### 组合搜索
```bash
GET /AICodingAnalysisResult?page=1&pageSize=10&misNumber=zhangsan&gitRepository=project&startTime=2024-01-01T00:00:00.000Z&endTime=2024-12-31T23:59:59.999Z
```

### 1.3 查询参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| page | number | 否 | 1 | 页码，从1开始 | `1` |
| pageSize | number | 否 | 20 | 每页数量，最大100 | `20` |
| analysisTime | string | 否 | - | 精确分析时间 (ISO 8601格式) | `2024-01-15T10:30:00.000Z` |
| startTime | string | 否 | - | 开始时间 (ISO 8601格式) | `2024-01-01T00:00:00.000Z` |
| endTime | string | 否 | - | 结束时间 (ISO 8601格式) | `2024-12-31T23:59:59.999Z` |
| misNumber | string | 否 | - | MIS号，支持模糊搜索 | `zhangsan` |
| prId | string | 否 | - | PR ID，支持模糊搜索 | `PR-2024` |
| gitRepository | string | 否 | - | 仓库地址，支持模糊搜索 | `project.git` |
| gitBranch | string | 否 | - | 仓库分支，支持模糊搜索 | `main` |

### 1.4 成功返回示例

#### HTTP 状态码: 200

```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "pr_global_id": "PR-2024-001",
        "pr_title": "添加新功能模块",
        "git_repository": "https://git.example.com/frontend-project.git",
        "git_branch": "feature/new-feature",
        "mis_number": "zhangsan",
        "detection_method": "AI_DETECTION",
        "analysis_timestamp": "2024-01-15T10:30:00.000Z",
        "total_files": 5,
        "total_lines": 1000,
        "changed_lines": 200,
        "ai_generated_lines": 50,
        "ai_code_ratio": 0.25000000,
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-15T10:30:00.000Z",
        "fileDetails": [
          {
            "id": 1,
            "report_id": 1,
            "pr_global_id": "PR-2024-001",
            "file_name": "src/components/Button.js",
            "file_type": "js",
            "total_lines": 100,
            "changed_lines": 20,
            "ai_matched_lines": 5,
            "ai_code_ratio": 0.25000000,
            "detection_method": "AI_DETECTION",
            "created_at": "2024-01-15T10:30:00.000Z",
            "updated_at": "2024-01-15T10:30:00.000Z"
          },
          {
            "id": 2,
            "report_id": 1,
            "pr_global_id": "PR-2024-001",
            "file_name": "src/components/Modal.vue",
            "file_type": "vue",
            "total_lines": 150,
            "changed_lines": 30,
            "ai_matched_lines": 8,
            "ai_code_ratio": 0.26666667,
            "detection_method": "AI_DETECTION",
            "created_at": "2024-01-15T10:30:00.000Z",
            "updated_at": "2024-01-15T10:30:00.000Z"
          }
        ]
      },
      {
        "id": 2,
        "pr_global_id": "PR-2024-002",
        "pr_title": "修复登录bug",
        "git_repository": "https://git.example.com/backend-service.git",
        "git_branch": "bugfix/login-issue",
        "mis_number": "lisi",
        "detection_method": "MANUAL_REVIEW",
        "analysis_timestamp": "2024-01-16T14:20:00.000Z",
        "total_files": 3,
        "total_lines": 500,
        "changed_lines": 100,
        "ai_generated_lines": 30,
        "ai_code_ratio": 0.30000000,
        "created_at": "2024-01-16T14:20:00.000Z",
        "updated_at": "2024-01-16T14:20:00.000Z",
        "fileDetails": [
          {
            "id": 3,
            "report_id": 2,
            "pr_global_id": "PR-2024-002",
            "file_name": "src/auth/login.py",
            "file_type": "py",
            "total_lines": 200,
            "changed_lines": 50,
            "ai_matched_lines": 15,
            "ai_code_ratio": 0.30000000,
            "detection_method": "MANUAL_REVIEW",
            "created_at": "2024-01-16T14:20:00.000Z",
            "updated_at": "2024-01-16T14:20:00.000Z"
          }
        ]
      }
    ],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalCount": 2,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 1.5 空结果返回示例

#### HTTP 状态码: 200

```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "list": [],
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "totalCount": 0,
      "totalPages": 0,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 1.6 错误返回示例

#### HTTP 状态码: 500

```json
{
  "success": false,
  "message": "服务器内部错误: 数据库连接失败"
}
```

---

## 2. POST 请求 - 存储分析结果

### 2.1 接口说明
- **方法**: POST
- **功能**: 存储新的AI代码分析结果
- **URL**: `POST /AICodingAnalysisResult`
- **Content-Type**: `application/json`

### 2.2 请求示例

#### 完整请求示例
```bash
POST /AICodingAnalysisResult
Content-Type: application/json

{
  "pr_global_id": "PR-2024-003",
  "pr_title": "重构用户认证模块",
  "git_repository": "https://git.example.com/auth-service.git",
  "git_branch": "refactor/user-auth",
  "mis_number": "wangwu",
  "detection_method": "AI_DETECTION",
  "analysis_timestamp": "2024-01-17T16:45:00.000Z",
  "total_files": 8,
  "total_lines": 2500,
  "changed_lines": 800,
  "ai_generated_lines": 200,
  "ai_code_ratio": 0.25000000,
  "file_details": [
    {
      "file_name": "src/auth/authentication.js",
      "file_type": "js",
      "total_lines": 300,
      "changed_lines": 120,
      "ai_matched_lines": 30,
      "ai_code_ratio": 0.25000000,
      "detection_method": "AI_DETECTION"
    },
    {
      "file_name": "src/auth/authorization.py",
      "file_type": "py",
      "total_lines": 250,
      "changed_lines": 100,
      "ai_matched_lines": 25,
      "ai_code_ratio": 0.25000000,
      "detection_method": "AI_DETECTION"
    },
    {
      "file_name": "src/components/LoginForm.vue",
      "file_type": "vue",
      "total_lines": 180,
      "changed_lines": 60,
      "ai_matched_lines": 15,
      "ai_code_ratio": 0.25000000,
      "detection_method": "AI_DETECTION"
    },
    {
      "file_name": "tests/auth.test.js",
      "file_type": "js",
      "total_lines": 150,
      "changed_lines": 80,
      "ai_matched_lines": 20,
      "ai_code_ratio": 0.25000000,
      "detection_method": "AI_DETECTION"
    }
  ]
}
```

#### 最简请求示例（仅必填字段）
```bash
POST /AICodingAnalysisResult
Content-Type: application/json

{
  "pr_global_id": "PR-2024-004",
  "pr_title": "简单bug修复",
  "git_repository": "https://git.example.com/simple-fix.git",
  "git_branch": "hotfix/simple-bug",
  "detection_method": "MANUAL_REVIEW",
  "analysis_timestamp": "2024-01-18T09:15:00.000Z"
}
```

### 2.3 请求字段说明

#### 主表字段（必填）

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pr_global_id | string | 是 | PR全局ID，唯一标识，最长50字符 | `"PR-2024-003"` |
| pr_title | string | 是 | PR标题，最长500字符 | `"重构用户认证模块"` |
| git_repository | string | 是 | Git仓库地址，最长500字符 | `"https://git.example.com/auth-service.git"` |
| git_branch | string | 是 | Git分支名，最长200字符 | `"refactor/user-auth"` |
| detection_method | string | 是 | 检测方法，最长50字符 | `"AI_DETECTION"` |
| analysis_timestamp | string | 是 | 分析时间戳 (ISO 8601格式) | `"2024-01-17T16:45:00.000Z"` |

#### 主表字段（可选）

| 字段名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| mis_number | string | 否 | null | MIS号/作者，最长50字符 | `"wangwu"` |
| total_files | number | 否 | 0 | 总文件数 | `8` |
| total_lines | number | 否 | 0 | 总行数 | `2500` |
| changed_lines | number | 否 | 0 | 变更行数 | `800` |
| ai_generated_lines | number | 否 | 0 | AI生成行数 | `200` |
| ai_code_ratio | number | 否 | 0.0 | AI代码占比 (0-1之间的小数) | `0.25000000` |

#### 文件详情字段（可选数组）

| 字段名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| file_name | string | 是 | - | 文件名/路径，最长1000字符 | `"src/auth/authentication.js"` |
| file_type | string | 是 | - | 文件类型，最长20字符 | `"js"` |
| total_lines | number | 否 | 0 | 文件总行数 | `300` |
| changed_lines | number | 否 | 0 | 文件变更行数 | `120` |
| ai_matched_lines | number | 否 | 0 | AI匹配行数 | `30` |
| ai_code_ratio | number | 否 | 0.0 | 文件AI代码占比 (0-1之间的小数) | `0.25000000` |
| detection_method | string | 否 | 继承主表 | 文件检测方法，最长50字符 | `"AI_DETECTION"` |

### 2.4 成功返回示例

#### HTTP 状态码: 201

```json
{
  "success": true,
  "message": "分析结果保存成功",
  "data": {
    "id": 3,
    "pr_global_id": "PR-2024-003",
    "pr_title": "重构用户认证模块",
    "git_repository": "https://git.example.com/auth-service.git",
    "git_branch": "refactor/user-auth",
    "mis_number": "wangwu",
    "detection_method": "AI_DETECTION",
    "analysis_timestamp": "2024-01-17T16:45:00.000Z",
    "total_files": 8,
    "total_lines": 2500,
    "changed_lines": 800,
    "ai_generated_lines": 200,
    "ai_code_ratio": 0.25000000,
    "created_at": "2024-01-17T16:45:30.123Z",
    "updated_at": "2024-01-17T16:45:30.123Z",
    "fileDetails": [
      {
        "id": 4,
        "report_id": 3,
        "pr_global_id": "PR-2024-003",
        "file_name": "src/auth/authentication.js",
        "file_type": "js",
        "total_lines": 300,
        "changed_lines": 120,
        "ai_matched_lines": 30,
        "ai_code_ratio": 0.25000000,
        "detection_method": "AI_DETECTION",
        "created_at": "2024-01-17T16:45:30.456Z",
        "updated_at": "2024-01-17T16:45:30.456Z"
      },
      {
        "id": 5,
        "report_id": 3,
        "pr_global_id": "PR-2024-003",
        "file_name": "src/auth/authorization.py",
        "file_type": "py",
        "total_lines": 250,
        "changed_lines": 100,
        "ai_matched_lines": 25,
        "ai_code_ratio": 0.25000000,
        "detection_method": "AI_DETECTION",
        "created_at": "2024-01-17T16:45:30.456Z",
        "updated_at": "2024-01-17T16:45:30.456Z"
      },
      {
        "id": 6,
        "report_id": 3,
        "pr_global_id": "PR-2024-003",
        "file_name": "src/components/LoginForm.vue",
        "file_type": "vue",
        "total_lines": 180,
        "changed_lines": 60,
        "ai_matched_lines": 15,
        "ai_code_ratio": 0.25000000,
        "detection_method": "AI_DETECTION",
        "created_at": "2024-01-17T16:45:30.456Z",
        "updated_at": "2024-01-17T16:45:30.456Z"
      },
      {
        "id": 7,
        "report_id": 3,
        "pr_global_id": "PR-2024-003",
        "file_name": "tests/auth.test.js",
        "file_type": "js",
        "total_lines": 150,
        "changed_lines": 80,
        "ai_matched_lines": 20,
        "ai_code_ratio": 0.25000000,
        "detection_method": "AI_DETECTION",
        "created_at": "2024-01-17T16:45:30.456Z",
        "updated_at": "2024-01-17T16:45:30.456Z"
      }
    ]
  }
}
```

### 2.5 重复提交错误返回

#### HTTP 状态码: 409

```json
{
  "success": false,
  "message": "PR ID PR-2024-003 已存在，不能重复提交",
  "data": {
    "existingReport": {
      "id": 3,
      "pr_global_id": "PR-2024-003",
      "pr_title": "重构用户认证模块",
      "git_repository": "https://git.example.com/auth-service.git",
      "git_branch": "refactor/user-auth",
      "mis_number": "wangwu",
      "detection_method": "AI_DETECTION",
      "analysis_timestamp": "2024-01-17T16:45:00.000Z",
      "total_files": 8,
      "total_lines": 2500,
      "changed_lines": 800,
      "ai_generated_lines": 200,
      "ai_code_ratio": 0.25000000,
      "created_at": "2024-01-17T16:45:30.123Z",
      "updated_at": "2024-01-17T16:45:30.123Z"
    }
  }
}
```

### 2.6 请求参数错误返回

#### HTTP 状态码: 400

```json
{
  "success": false,
  "message": "缺少必填字段: pr_global_id"
}
```

### 2.7 服务器错误返回

#### HTTP 状态码: 500

```json
{
  "success": false,
  "message": "服务器内部错误: 数据库连接失败"
}
```

---

## 3. 错误码说明

| HTTP状态码 | 说明 | 场景 |
|------------|------|------|
| 200 | 查询成功 | GET请求成功返回数据 |
| 201 | 创建成功 | POST请求成功创建记录 |
| 400 | 请求参数错误 | 缺少必填字段或参数格式错误 |
| 405 | 不支持的HTTP方法 | 使用了不支持的HTTP方法 |
| 409 | 资源冲突 | PR ID已存在，不能重复提交 |
| 500 | 服务器内部错误 | 数据库连接失败或其他服务器错误 |

---

## 4. 使用注意事项

### 4.1 数据约束
1. **pr_global_id 唯一性**: 每个PR ID只能提交一次，重复提交会返回409错误
2. **字符长度限制**:
   - pr_global_id: 最长50字符
   - pr_title: 最长500字符
   - git_repository: 最长500字符
   - git_branch: 最长200字符
   - mis_number: 最长50字符
   - detection_method: 最长50字符
   - file_name: 最长1000字符
   - file_type: 最长20字符

### 4.2 时间格式
- 所有时间字段都使用ISO 8601格式: `YYYY-MM-DDTHH:mm:ss.sssZ`
- 示例: `2024-01-17T16:45:00.000Z`

### 4.3 分页限制
- 默认每页20条记录
- 最大每页100条记录
- 页码从1开始

### 4.4 搜索功能
- misNumber、prId、gitRepository、gitBranch支持模糊搜索
- 时间支持精确匹配和范围查询
- 可以组合多个搜索条件

### 4.5 事务处理
- POST操作使用数据库事务，确保主表和详情表数据一致性
- 如果任何一个步骤失败，整个操作会回滚

### 4.6 数据类型
- ai_code_ratio: 0-1之间的小数，表示百分比
- 所有数值字段不能为负数
- 时间戳必须是有效的ISO 8601格式

---

## 5. 跨域支持 (CORS)

### 5.1 CORS 配置
API 已配置完整的 CORS 支持：

- **Access-Control-Allow-Origin**: `*` (允许所有域名)
- **Access-Control-Allow-Methods**: `GET, POST, PUT, DELETE, OPTIONS`
- **Access-Control-Allow-Headers**: `Content-Type, Authorization, X-Requested-With`
- **Access-Control-Max-Age**: `86400` (24小时)

### 5.2 预检请求
对于复杂请求，浏览器会自动发送 OPTIONS 预检请求：

```bash
OPTIONS /AICodingAnalysisResult
```

**响应示例**：
```json
HTTP/1.1 200 OK
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
Access-Control-Max-Age: 86400
```

### 5.3 前端调用示例

#### JavaScript Fetch API
```javascript
// GET 请求
const response = await fetch('https://your-domain.com/AICodingAnalysisResult?page=1&pageSize=10', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});

// POST 请求
const response = await fetch('https://your-domain.com/AICodingAnalysisResult', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    pr_global_id: "PR-2024-001",
    pr_title: "测试PR",
    // ... 其他字段
  })
});
```

#### jQuery AJAX
```javascript
// GET 请求
$.ajax({
  url: 'https://your-domain.com/AICodingAnalysisResult',
  method: 'GET',
  data: { page: 1, pageSize: 10 },
  success: function(data) {
    console.log(data);
  }
});

// POST 请求
$.ajax({
  url: 'https://your-domain.com/AICodingAnalysisResult',
  method: 'POST',
  contentType: 'application/json',
  data: JSON.stringify({
    pr_global_id: "PR-2024-001",
    pr_title: "测试PR"
  }),
  success: function(data) {
    console.log(data);
  }
});
```

### 5.4 测试跨域
使用提供的 `test-cors.html` 文件可以测试跨域功能：

1. 在浏览器中打开 `test-cors.html`
2. 修改 API URL 为实际地址
3. 点击测试按钮验证跨域功能

---

## 6. 常见问题

### Q1: 如何处理重复提交？
A: 系统会检查pr_global_id的唯一性，如果已存在会返回409错误，包含已存在记录的详细信息。

### Q2: 文件详情是否必须提供？
A: 文件详情是可选的，可以只提交主表数据。

### Q3: 如何进行时间范围查询？
A: 使用startTime和endTime参数，格式为ISO 8601。

### Q4: 分页查询的最大限制是多少？
A: 每页最大100条记录，超过会使用默认值20。

### Q5: 支持哪些搜索方式？
A: 支持精确匹配（时间）和模糊搜索（文本字段），可以组合使用。

### Q6: 跨域请求失败怎么办？
A: 检查浏览器控制台是否有CORS错误，确认API已正确配置CORS头信息。

### Q7: 预检请求失败怎么办？
A: 确认服务器支持OPTIONS方法，并返回正确的CORS头信息。
