# 生产环境设置指南

## 问题诊断

你的 POST 请求现在能正常工作，但数据库中没有数据。这是因为数据表还没有在生产环境中创建。

## 解决方案

### 方案一：自动创建表（推荐）

1. **运行数据库初始化脚本**：
   ```bash
   node init-production-db.js
   ```

2. **验证表是否创建成功**：
   脚本会输出创建结果，如果看到 "✅ 数据表创建成功！" 说明表已创建。

### 方案二：手动创建表

如果自动创建失败，可以手动执行 SQL：

1. **连接到数据库**
2. **执行 create-tables.sql 中的 SQL 语句**

## 验证步骤

### 1. 检查表是否存在
```sql
SHOW TABLES LIKE 'ai_code_analysis%';
```

应该看到两个表：
- `ai_code_analysis_reports`
- `ai_code_analysis_file_details`

### 2. 检查表结构
```sql
DESCRIBE ai_code_analysis_reports;
DESCRIBE ai_code_analysis_file_details;
```

### 3. 测试 API

重新执行你的 POST 请求：

```bash
curl --location --request POST 'https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c' \
--header 'Content-Type: application/json' \
--data '{
  "pr_global_id": "13073502",
  "pr_title": "【不要合并，只是测试webhook】",
  "git_repository": "ssh://*******************/dzfe/medical-home-page.git",
  "git_branch": "feature/fedo-213908",
  "mis_number": "yaoyan03",
  "detection_method": "行级匹配",
  "analysis_timestamp": "2025-05-24T12:37:01.729Z",
  "total_files": 5,
  "total_lines": 2141,
  "changed_lines": 2141,
  "ai_generated_lines": 971,
  "ai_code_ratio": 0.4535263895375993,
  "file_details": [...]
}'
```

### 4. 验证数据是否保存

```sql
SELECT COUNT(*) FROM ai_code_analysis_reports;
SELECT COUNT(*) FROM ai_code_analysis_file_details;
```

### 5. 测试 GET 请求

```bash
curl --location --request GET 'https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c?page=1&pageSize=10'
```

应该返回你刚才插入的数据。

## 常见问题

### Q1: 数据库初始化脚本失败
**A**: 检查网络连接和数据库权限，确保 appName 和 refKey 配置正确。

### Q2: 表创建成功但数据还是没有保存
**A**: 检查云函数日志，可能是权限问题或者数据验证失败。

### Q3: GET 请求返回空数据
**A**: 确认 POST 请求确实成功保存了数据，检查数据库中的实际数据。

## 日志检查

在云函数日志中查找以下关键信息：

1. **数据库连接**：
   - `Zebra-proxy 初始化成功`
   - `数据库表创建成功`

2. **POST 请求处理**：
   - `检测到的 HTTP 方法: POST`
   - `收到分析结果数据:`
   - `分析结果保存成功`

3. **错误信息**：
   - 任何包含 "Error" 或 "失败" 的日志

## 下一步

1. 先运行 `node init-production-db.js` 创建表
2. 重新测试 POST 请求
3. 验证数据是否正确保存
4. 测试 GET 请求是否能查询到数据

如果还有问题，请提供：
- 数据库初始化脚本的输出
- 云函数的完整日志
- 数据库中的表结构信息
