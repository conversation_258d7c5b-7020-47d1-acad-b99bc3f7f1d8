# AICodingAnalysis

## 功能介绍

本服务提供了一个HTTP API，用于接收webhook请求并触发Talos流水线部署。

## 配置Talos流水线

在使用前，请先配置Talos相关参数：

1. 打开`talos-config.js`文件
2. 修改以下配置项：
   - `TALOS_APP_ID`: 您的应用ID
   - `TALOS_CONFIG_ID`: 发布模板ID
   - `TALOS_OP_MIS`: 默认发布人MIS账号
   - `CLIENT_ID`: Talos API客户端ID（联系Talos客服获取）
   - `CLIENT_SECRET`: Talos API客户端密钥（联系Talos客服获取）
   - `MOCK.ENABLED`: 是否启用模拟模式（生产环境请设为false）

您也可以通过设置环境变量来覆盖这些配置：
- `TALOS_APP_ID`: 应用ID
- `TALOS_CONFIG_ID`: 发布模板ID
- `TALOS_OP_MIS`: 默认发布人MIS账号
- `TALOS_CLIENT_ID`: Talos API客户端ID
- `TALOS_CLIENT_SECRET`: Talos API客户端密钥
- `TALOS_MOCK_ENABLED`: 是否启用模拟模式（设为"true"启用）

## 技术实现

本服务使用官方SDK `@nibfe/talos-public-api` 来与Talos API通信，具体实现如下：

1. 使用`getAPI`函数初始化API客户端：
   ```javascript
   const getAPI = require('@nibfe/talos-public-api');
   const { publish } = getAPI({
     env: 'production', 
     clientId: 'YOUR_CLIENT_ID',
     clientSecret: 'YOUR_CLIENT_SECRET'
   });
   ```

2. 使用`publish`方法触发流水线，标准请求格式：
   ```javascript
   const response = await publish({
     id: appId,       // 应用ID
     cid: configId,   // 发布模板ID
     op: 'yourMis',   // 发布人MIS
     comment: {       // 发布说明
       note: '这是发布说明'  
     },
     envs: [          // 环境变量，放在顶层
       {
         key: 'KEY1',
         value: 'VALUE1'
       }
     ]
   });
   ```

## API接口

### POST /AICodingAnalysis

接收webhook请求并触发Talos流水线部署。

请求示例：
```json
{
  "author": {
    "name": "git_cd",
    "email": "<EMAIL>"
  },
  "comment": {
    "id": 40345723,
    "content": "PR合并后流水线完成通知",
    "updated_at": "2025-05-21T08:51:21.86Z"
  },
  "pull_request": {
    "pr_global_id": 13053500,
    "pr_id": 498,
    "title": "榜单新增预请求 的 PR",
    "source_ref": {
      "branch": "release/fedo-66338"
    },
    "target_ref": {
      "branch": "master"
    },
    "tmp_merge_ref": {
      "branch": "refs/pull-requests/498/merge"
    },
    "author": {
      "name": "yaoyan03",
      "email": "<EMAIL>"
    }
  }
}
```

响应示例：
```json
{
  "success": true,
  "message": "成功触发Talos流水线部署",
  "data": {
    "talosResponse": {
      "flow_id": 12345,
      "message": "流水线已触发"
    }
  }
}
```

## 环境变量提取

从webhook请求中提取以下信息作为Talos环境变量：

| 环境变量 | 来源 |
|----------|------|
| pr_global_id | pull_request.pr_global_id |
| author | pull_request.author.name |
| source_ref | pull_request.source_ref.branch |
| target_ref | pull_request.tmp_merge_ref.branch |
| title | pull_request.title |
| updated_at | comment.updated_at |

## 故障排除

如果遇到Talos API调用失败的问题，可以尝试以下方法：

1. 确认应用ID和发布模板ID是否有效
2. 验证CLIENT_ID和CLIENT_SECRET是否正确
3. 确保您有足够的权限触发Talos流水线
4. 启用模拟模式进行测试 (`MOCK.ENABLED = true`)
5. 查看日志输出中的详细错误信息