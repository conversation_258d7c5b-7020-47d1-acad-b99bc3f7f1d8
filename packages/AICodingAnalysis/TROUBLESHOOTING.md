# 数据库连接问题排查指南

## 当前问题

从日志可以看到：
```
Zebra-proxy 配置: {
  appName: 'com.sankuai.dzufebiz.manage',
  refKey: 'dzfrontendshanghai_swift_test.man',
  env: 'test',
  dbType: 'zebra-proxy'
}
Zebra-proxy 初始化成功
尝试连接数据库...
数据库操作失败: DatabaseError [SequelizeDatabaseError]: unknown error: no database
```

## 问题分析

1. ✅ **Zebra-proxy 初始化成功** - 说明配置基本正确
2. ❌ **数据库连接失败** - `unknown error: no database`
3. ❌ **后续操作使用模拟数据库** - 导致数据没有真正保存

## 可能的原因

### 1. RefKey 不正确
当前使用的 refKey: `dzfrontendshanghai_swift_test.man`

可能需要的 refKey 格式：
- `dzfrontendshanghai_swift_test` (去掉 .man 后缀)
- `dzfrontendshanghai.swift.test` (不同的分隔符)
- 其他格式

### 2. 数据库不存在
测试环境中可能没有创建对应的数据库实例。

### 3. 权限问题
应用可能没有访问该数据库的权限。

## 解决方案

### 方案1: 修改 RefKey

尝试不同的 refKey 格式：

```javascript
// 当前
refKey: 'dzfrontendshanghai_swift_test.man'

// 尝试1
refKey: 'dzfrontendshanghai_swift_test'

// 尝试2  
refKey: 'dzfrontendshanghai.swift.test'

// 尝试3
refKey: 'dzfrontendshanghai_swift_test.db'
```

### 方案2: 联系DBA

联系数据库管理员确认：
1. 测试环境数据库是否存在
2. 正确的 refKey 格式
3. 应用是否有访问权限

### 方案3: 使用其他数据库

如果测试环境数据库不可用，可以：
1. 使用开发环境数据库
2. 创建新的测试数据库实例

## 下一步行动

1. **确认 refKey 格式**
   - 查看其他项目的 zebra-proxy 配置
   - 咨询团队成员

2. **测试不同配置**
   - 修改 refKey 并重新部署
   - 观察日志变化

3. **联系支持**
   - 如果问题持续，联系数据库团队
   - 提供详细的错误日志

## 临时解决方案

在问题解决之前，可以：
1. 使用模拟数据库进行功能测试
2. 在本地环境测试完整功能
3. 准备生产环境的正确配置

## 验证步骤

修改配置后，检查日志中是否出现：
```
数据库连接成功
数据库表创建成功
数据库状态: REAL_DATABASE
```

而不是：
```
数据库操作失败
⚠️  警告：当前使用模拟数据库，数据不会真正保存！
数据库状态: MOCK_DATABASE
```
