/**
 * 数据库配置检查脚本
 * 用于诊断数据库连接和配置问题
 */

const {
  ZebraSequelizeFactory,
  DataTypes
} = require('@bfe/zebra-proxy-sequelize');

// 数据库配置
const appKey = 'com.sankuai.dzufebiz.manage';
const refKey = 'dzu_sh_test_12_dzufebiz_test';

async function checkDatabase() {
  console.log('=== 数据库配置检查 ===');
  console.log('AppKey:', appKey);
  console.log('RefKey:', refKey);

  try {
    // 创建 zebra-proxy 连接
    const options = {
      appName: appKey,
      refKey: refKey,
      dbType: "zebra-proxy",
      database: 'dzufebiz',
      pool: {
        max: 20,
        min: 1,
      },
      dialectOptions: {
        dateStrings: true,
      },
      logging: false
    };

    console.log('\n=== 创建 Zebra-proxy 连接 ===');
    const sequelize = ZebraSequelizeFactory.create(options);
    console.log('✅ Zebra-proxy 连接创建成功');

    // 测试连接
    console.log('\n=== 测试数据库连接 ===');
    await sequelize.authenticate();
    console.log('✅ 数据库连接测试成功');

    // 获取数据库信息
    console.log('\n=== 数据库信息 ===');
    try {
      const [results] = await sequelize.query('SELECT DATABASE() as current_database');
      console.log('当前数据库:', results[0]?.current_database || '未知');
    } catch (error) {
      console.log('❌ 无法获取当前数据库:', error.message);
    }

    // 检查表是否存在
    console.log('\n=== 检查表是否存在 ===');
    try {
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'ai_code_analysis%'");
      if (tables.length > 0) {
        console.log('✅ 找到相关表:');
        tables.forEach(table => {
          console.log('  -', Object.values(table)[0]);
        });
      } else {
        console.log('❌ 未找到相关表');
      }
    } catch (error) {
      console.log('❌ 检查表失败:', error.message);
    }

    // 尝试创建表
    console.log('\n=== 尝试创建表 ===');
    try {
      // 定义简单的测试表
      const TestModel = sequelize.define('ai_code_analysis_test', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        test_field: {
          type: DataTypes.STRING(50),
          allowNull: false
        }
      }, {
        freezeTableName: true,
        timestamps: false
      });

      await TestModel.sync({ force: false });
      console.log('✅ 测试表创建成功');

      // 删除测试表
      await TestModel.drop();
      console.log('✅ 测试表删除成功');

    } catch (error) {
      console.log('❌ 创建表失败:', error.message);
      console.log('错误详情:', {
        code: error.code,
        errno: error.errno,
        sql: error.sql
      });
    }

    // 关闭连接
    await sequelize.close();
    console.log('\n✅ 数据库连接已关闭');

  } catch (error) {
    console.error('\n❌ 数据库检查失败:', error.message);
    console.error('错误详情:', {
      code: error.code,
      errno: error.errno,
      sql: error.sql,
      stack: error.stack
    });
  }
}

// 运行检查
if (require.main === module) {
  checkDatabase().then(() => {
    console.log('\n=== 检查完成 ===');
    process.exit(0);
  }).catch(error => {
    console.error('\n=== 检查失败 ===');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { checkDatabase };
