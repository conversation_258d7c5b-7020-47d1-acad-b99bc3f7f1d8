/**
 * 检查数据库中的真实数据
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '*************',
  port: 5002,
  user: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'swift',
  connectTimeout: 10000
};

async function checkRealData() {
  console.log('🔍 检查数据库中的真实数据...\n');

  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查主表数据
    console.log('\n📊 检查主表 ai_code_analysis_reports:');
    const [reports] = await connection.execute('SELECT * FROM ai_code_analysis_reports ORDER BY created_at DESC LIMIT 10');
    console.log(`总记录数: ${reports.length}`);
    
    if (reports.length > 0) {
      console.log('\n最新的记录:');
      reports.forEach((report, index) => {
        console.log(`${index + 1}. ID: ${report.id}, PR: ${report.pr_global_id}, 标题: ${report.pr_title?.substring(0, 50)}...`);
        console.log(`   创建时间: ${report.created_at}, AI占比: ${report.ai_code_ratio}`);
      });
    } else {
      console.log('❌ 主表中没有数据');
    }

    // 检查详情表数据
    console.log('\n📋 检查详情表 ai_code_analysis_file_details:');
    const [details] = await connection.execute('SELECT * FROM ai_code_analysis_file_details ORDER BY created_at DESC LIMIT 10');
    console.log(`总记录数: ${details.length}`);
    
    if (details.length > 0) {
      console.log('\n最新的文件详情:');
      details.slice(0, 5).forEach((detail, index) => {
        console.log(`${index + 1}. ID: ${detail.id}, 报告ID: ${detail.report_id}, 文件: ${detail.file_name}`);
        console.log(`   PR: ${detail.pr_global_id}, AI占比: ${detail.ai_code_ratio}`);
      });
    } else {
      console.log('❌ 详情表中没有数据');
    }

    // 检查特定 PR ID
    console.log('\n🔍 检查特定 PR ID 13073644:');
    const [specificPR] = await connection.execute('SELECT * FROM ai_code_analysis_reports WHERE pr_global_id = ?', ['13073644']);
    if (specificPR.length > 0) {
      console.log('✅ 找到 PR 13073644:');
      console.log(specificPR[0]);
      
      // 检查对应的文件详情
      const [specificDetails] = await connection.execute('SELECT * FROM ai_code_analysis_file_details WHERE pr_global_id = ?', ['13073644']);
      console.log(`对应的文件详情数量: ${specificDetails.length}`);
    } else {
      console.log('❌ 没有找到 PR 13073644');
    }

    // 检查表结构
    console.log('\n🏗️  检查表结构:');
    const [mainTableStructure] = await connection.execute('DESCRIBE ai_code_analysis_reports');
    console.log('主表字段:');
    mainTableStructure.forEach(field => {
      console.log(`  - ${field.Field}: ${field.Type} ${field.Null === 'NO' ? 'NOT NULL' : ''} ${field.Key ? field.Key : ''}`);
    });

    // 检查索引
    console.log('\n📇 检查索引:');
    const [indexes] = await connection.execute('SHOW INDEX FROM ai_code_analysis_reports');
    console.log('主表索引:');
    indexes.forEach(index => {
      console.log(`  - ${index.Key_name}: ${index.Column_name} (${index.Index_type})`);
    });

    // 检查最近的插入操作
    console.log('\n⏰ 检查最近的数据变化:');
    const [recentData] = await connection.execute(`
      SELECT id, pr_global_id, created_at, updated_at 
      FROM ai_code_analysis_reports 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      ORDER BY created_at DESC
    `);
    
    if (recentData.length > 0) {
      console.log('最近1小时内的数据:');
      recentData.forEach(data => {
        console.log(`  - ID: ${data.id}, PR: ${data.pr_global_id}, 创建: ${data.created_at}`);
      });
    } else {
      console.log('最近1小时内没有新数据');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('错误详情:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 测试插入一条新数据
async function testInsertNewData() {
  console.log('\n🧪 测试插入新数据...\n');

  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    const testData = {
      pr_global_id: `TEST_DIRECT_${Date.now()}`,
      pr_title: '直接数据库测试',
      git_repository: 'https://git.example.com/direct-test.git',
      git_branch: 'direct-test',
      mis_number: 'direct_test',
      detection_method: '直接数据库测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    console.log('插入测试数据:', testData.pr_global_id);
    
    const [result] = await connection.execute(`
      INSERT INTO ai_code_analysis_reports 
      (pr_global_id, pr_title, git_repository, git_branch, mis_number, detection_method, analysis_timestamp, total_files, total_lines, changed_lines, ai_generated_lines, ai_code_ratio)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      testData.pr_global_id, testData.pr_title, testData.git_repository, testData.git_branch,
      testData.mis_number, testData.detection_method, testData.analysis_timestamp,
      testData.total_files, testData.total_lines, testData.changed_lines,
      testData.ai_generated_lines, testData.ai_code_ratio
    ]);

    console.log('✅ 插入成功，ID:', result.insertId);
    
    // 立即查询验证
    const [verify] = await connection.execute('SELECT * FROM ai_code_analysis_reports WHERE id = ?', [result.insertId]);
    if (verify.length > 0) {
      console.log('✅ 验证成功，数据已保存:', verify[0].pr_global_id);
    } else {
      console.log('❌ 验证失败，数据未找到');
    }

  } catch (error) {
    console.error('❌ 插入测试失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function runFullCheck() {
  await checkRealData();
  await testInsertNewData();
  
  console.log('\n📝 分析结论:');
  console.log('1. 如果表中有数据，说明数据库连接正常');
  console.log('2. 如果没有数据，可能是应用层的事务问题');
  console.log('3. 如果测试插入成功，说明数据库权限正常');
  console.log('4. 需要检查应用代码中的事务处理逻辑');
}

if (require.main === module) {
  runFullCheck().then(() => {
    console.log('\n🏁 检查完成');
    process.exit(0);
  }).catch(error => {
    console.error('检查出错:', error);
    process.exit(1);
  });
}

module.exports = { checkRealData, testInsertNewData };
