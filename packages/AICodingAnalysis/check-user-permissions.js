/**
 * 检查用户权限和可访问的数据库
 */

const mysql = require('mysql2/promise');

// 数据库连接配置（不指定数据库）
const dbConfig = {
  host: '*************',
  port: 5002,
  user: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  // 不指定 database，连接后查看权限
  connectTimeout: 10000
};

async function checkUserPermissions() {
  console.log('🔍 检查用户权限和可访问的数据库...\n');

  let connection;
  
  try {
    console.log('🔗 建立连接...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 连接成功');

    // 查看所有数据库
    console.log('\n📋 查看所有数据库:');
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('服务器上的数据库:');
    databases.forEach(db => {
      console.log(`  - ${Object.values(db)[0]}`);
    });

    // 查看用户权限
    console.log('\n🔐 查看当前用户权限:');
    try {
      const [grants] = await connection.execute('SHOW GRANTS');
      console.log('用户权限:');
      grants.forEach(grant => {
        console.log(`  - ${Object.values(grant)[0]}`);
      });
    } catch (error) {
      console.log('无法查看权限:', error.message);
    }

    // 尝试访问每个数据库
    console.log('\n🧪 测试访问每个数据库:');
    for (const dbRow of databases) {
      const dbName = Object.values(dbRow)[0];
      
      // 跳过系统数据库
      if (['information_schema', 'performance_schema', 'mysql', 'sys'].includes(dbName)) {
        console.log(`  - ${dbName}: (系统数据库，跳过)`);
        continue;
      }

      try {
        await connection.execute(`USE ${dbName}`);
        console.log(`  - ${dbName}: ✅ 可访问`);
        
        // 查看表
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`    表数量: ${tables.length}`);
        
        if (tables.length > 0) {
          console.log('    表列表:');
          tables.slice(0, 5).forEach(table => { // 只显示前5个表
            console.log(`      - ${Object.values(table)[0]}`);
          });
          if (tables.length > 5) {
            console.log(`      ... 还有 ${tables.length - 5} 个表`);
          }
        }

      } catch (error) {
        console.log(`  - ${dbName}: ❌ 无权限 (${error.message})`);
      }
    }

    // 查找包含 AI 或 analysis 相关的表
    console.log('\n🔍 查找 AI/analysis 相关的表:');
    for (const dbRow of databases) {
      const dbName = Object.values(dbRow)[0];
      
      if (['information_schema', 'performance_schema', 'mysql', 'sys'].includes(dbName)) {
        continue;
      }

      try {
        await connection.execute(`USE ${dbName}`);
        const [tables] = await connection.execute("SHOW TABLES LIKE '%ai%'");
        const [analysisTables] = await connection.execute("SHOW TABLES LIKE '%analysis%'");
        
        if (tables.length > 0 || analysisTables.length > 0) {
          console.log(`  数据库 ${dbName}:`);
          [...tables, ...analysisTables].forEach(table => {
            console.log(`    - ${Object.values(table)[0]}`);
          });
        }
      } catch (error) {
        // 忽略无权限的数据库
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 连接已关闭');
    }
  }
}

// 创建一个可用的数据库配置
async function findWorkingDatabase() {
  console.log('\n🎯 寻找可用的数据库配置...\n');

  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    const [databases] = await connection.execute('SHOW DATABASES');
    
    for (const dbRow of databases) {
      const dbName = Object.values(dbRow)[0];
      
      if (['information_schema', 'performance_schema', 'mysql', 'sys'].includes(dbName)) {
        continue;
      }

      try {
        await connection.execute(`USE ${dbName}`);
        console.log(`✅ 找到可用数据库: ${dbName}`);
        
        console.log('\n📝 建议的配置:');
        console.log(`
const dbConfig = {
  host: '${dbConfig.host}',
  port: ${dbConfig.port},
  user: '${dbConfig.user}',
  password: '${dbConfig.password}',
  database: '${dbName}',
  pool: {
    max: 20,
    min: 1
  }
};
        `);
        
        return dbName;
      } catch (error) {
        // 继续尝试下一个数据库
      }
    }
    
    console.log('❌ 没有找到可访问的数据库');
    return null;

  } catch (error) {
    console.error('❌ 查找失败:', error.message);
    return null;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function runPermissionCheck() {
  await checkUserPermissions();
  await findWorkingDatabase();
}

if (require.main === module) {
  runPermissionCheck().then(() => {
    console.log('\n🏁 权限检查完成');
    process.exit(0);
  }).catch(error => {
    console.error('检查失败:', error);
    process.exit(1);
  });
}

module.exports = { checkUserPermissions, findWorkingDatabase };
