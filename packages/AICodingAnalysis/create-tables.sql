-- AI代码分析数据库表创建脚本
-- 请在数据库中手动执行这些SQL语句来创建表

-- 主表
CREATE TABLE IF NOT EXISTS `ai_code_analysis_reports` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pr_global_id` varchar(50) NOT NULL COMMENT 'PR全局ID',
  `pr_title` varchar(500) NOT NULL COMMENT 'PR标题',
  `git_repository` varchar(500) NOT NULL COMMENT 'Git仓库地址',
  `git_branch` varchar(200) NOT NULL COMMENT 'Git分支名',
  `mis_number` varchar(50) DEFAULT NULL COMMENT 'MIS号/作者',
  `detection_method` varchar(50) NOT NULL COMMENT '检测方法',
  `analysis_timestamp` datetime NOT NULL COMMENT '分析时间戳',
  
  `total_files` int(11) NOT NULL DEFAULT '0' COMMENT '总文件数',
  `total_lines` int(11) NOT NULL DEFAULT '0' COMMENT '总行数',
  `changed_lines` int(11) NOT NULL DEFAULT '0' COMMENT '变更行数',
  `ai_generated_lines` int(11) NOT NULL DEFAULT '0' COMMENT 'AI生成行数',
  `ai_code_ratio` decimal(10,8) NOT NULL DEFAULT '0.00000000' COMMENT 'AI代码占比',
  
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_pr_global_id` (`pr_global_id`),
  KEY `idx_git_repository` (`git_repository`(255)),
  KEY `idx_git_branch` (`git_branch`),
  KEY `idx_mis_number` (`mis_number`),
  KEY `idx_analysis_timestamp` (`analysis_timestamp`),
  KEY `idx_ai_code_ratio` (`ai_code_ratio`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI代码分析报告主表';

-- 详情表（无外键约束）
CREATE TABLE IF NOT EXISTS `ai_code_analysis_file_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` bigint(20) NOT NULL COMMENT '报告ID，关联ai_code_analysis_reports.id',
  `pr_global_id` varchar(50) NOT NULL COMMENT 'PR全局ID，冗余字段便于查询',
  
  `file_name` varchar(1000) NOT NULL COMMENT '文件名/路径',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型',
  `total_lines` int(11) NOT NULL DEFAULT '0' COMMENT '文件总行数',
  `changed_lines` int(11) NOT NULL DEFAULT '0' COMMENT '文件变更行数',
  `ai_matched_lines` int(11) NOT NULL DEFAULT '0' COMMENT 'AI匹配行数',
  `ai_code_ratio` decimal(10,8) NOT NULL DEFAULT '0.00000000' COMMENT '文件AI代码占比',
  `detection_method` varchar(50) NOT NULL COMMENT '文件检测方法',
  
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_pr_global_id` (`pr_global_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_ai_code_ratio` (`ai_code_ratio`),
  KEY `idx_detection_method` (`detection_method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI代码分析文件详情表';

-- 验证表是否创建成功
SELECT 'Tables created successfully' as status;
SHOW TABLES LIKE 'ai_code_analysis%';
DESCRIBE ai_code_analysis_reports;
DESCRIBE ai_code_analysis_file_details;
