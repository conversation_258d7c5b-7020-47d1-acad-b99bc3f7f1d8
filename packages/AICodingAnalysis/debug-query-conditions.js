/**
 * 调试查询条件构建过程
 */

// 模拟生产环境
process.env.USER = 'root';

async function debugQueryConditions() {
  console.log('🔍 调试查询条件构建过程...\n');

  try {
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    await modelsModule.initializeDatabase();
    
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;

    // 模拟你的 curl 请求参数
    const queryParams = {
      page: '1',
      pageSize: '20',
      misNumber: '4234',
      prId: '4324',
      gitRepository: '4234',
      gitBranch: '432423',
      startTime: '2025-05-13T00:00:00.000Z',
      endTime: '2025-06-26T23:59:59.999Z'
    };

    console.log('1. 输入参数:', queryParams);

    // 解构参数（模拟 index.js 中的逻辑）
    const {
      page = 1,
      pageSize = 20,
      analysisTime,
      misNumber,
      prId,
      gitRepository,
      gitBranch,
      startTime,
      endTime
    } = queryParams;

    console.log('\n2. 解构后的参数:');
    console.log('- page:', page);
    console.log('- pageSize:', pageSize);
    console.log('- analysisTime:', analysisTime);
    console.log('- misNumber:', misNumber);
    console.log('- prId:', prId);
    console.log('- gitRepository:', gitRepository);
    console.log('- gitBranch:', gitBranch);
    console.log('- startTime:', startTime);
    console.log('- endTime:', endTime);

    // 构建查询条件（模拟 index.js 中的逻辑）
    const whereConditions = {};

    console.log('\n3. 构建查询条件...');

    // 时间条件处理
    if (startTime && endTime) {
      whereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.between]: [startTime, endTime]
      };
      console.log('✅ 添加时间范围条件:', whereConditions.analysis_timestamp);
    } else if (startTime) {
      whereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.gte]: startTime
      };
      console.log('✅ 添加开始时间条件:', whereConditions.analysis_timestamp);
    } else if (endTime) {
      whereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.lte]: endTime
      };
      console.log('✅ 添加结束时间条件:', whereConditions.analysis_timestamp);
    } else if (analysisTime) {
      whereConditions.analysis_timestamp = analysisTime;
      console.log('✅ 添加精确时间条件:', whereConditions.analysis_timestamp);
    }

    if (misNumber) {
      whereConditions.mis_number = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${misNumber}%`
      };
      console.log('✅ 添加 misNumber 条件:', whereConditions.mis_number);
    }

    if (prId) {
      whereConditions.pr_global_id = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${prId}%`
      };
      console.log('✅ 添加 prId 条件:', whereConditions.pr_global_id);
    }

    if (gitRepository) {
      whereConditions.git_repository = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${gitRepository}%`
      };
      console.log('✅ 添加 gitRepository 条件:', whereConditions.git_repository);
    }

    if (gitBranch) {
      whereConditions.git_branch = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${gitBranch}%`
      };
      console.log('✅ 添加 gitBranch 条件:', whereConditions.git_branch);
    }

    console.log('\n4. 最终查询条件:');
    console.log(JSON.stringify(whereConditions, null, 2));

    // 计算分页参数
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);

    console.log('\n5. 分页参数:');
    console.log('- offset:', offset);
    console.log('- limit:', limit);

    // 执行查询
    console.log('\n6. 执行查询...');
    const result = await AiCodeAnalysisReports.findAndCountAll({
      where: whereConditions,
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      order: [['analysis_timestamp', 'DESC']],
      offset: offset,
      limit: limit
    });

    console.log('\n7. 查询结果:');
    console.log('- 总记录数:', result.count);
    console.log('- 返回记录数:', result.rows.length);

    if (result.rows.length > 0) {
      console.log('\n8. 第一条记录详情:');
      const first = result.rows[0];
      console.log('- ID:', first.id);
      console.log('- PR ID:', first.pr_global_id);
      console.log('- MIS Number:', first.mis_number);
      console.log('- Git Repository:', first.git_repository);
      console.log('- Git Branch:', first.git_branch);
      console.log('- Analysis Timestamp:', first.analysis_timestamp);
    }

    // 验证查询条件是否应该匹配
    console.log('\n9. 验证查询条件匹配性:');
    if (result.rows.length > 0) {
      const first = result.rows[0];
      
      console.log('- misNumber 匹配检查:');
      console.log(`  查询: %${misNumber}%`);
      console.log(`  实际: ${first.mis_number}`);
      console.log(`  匹配: ${first.mis_number && first.mis_number.includes(misNumber)}`);
      
      console.log('- prId 匹配检查:');
      console.log(`  查询: %${prId}%`);
      console.log(`  实际: ${first.pr_global_id}`);
      console.log(`  匹配: ${first.pr_global_id && first.pr_global_id.includes(prId)}`);
      
      console.log('- gitRepository 匹配检查:');
      console.log(`  查询: %${gitRepository}%`);
      console.log(`  实际: ${first.git_repository}`);
      console.log(`  匹配: ${first.git_repository && first.git_repository.includes(gitRepository)}`);
      
      console.log('- gitBranch 匹配检查:');
      console.log(`  查询: %${gitBranch}%`);
      console.log(`  实际: ${first.git_branch}`);
      console.log(`  匹配: ${first.git_branch && first.git_branch.includes(gitBranch)}`);
    }

    // 测试空查询条件
    console.log('\n10. 测试空查询条件...');
    const emptyResult = await AiCodeAnalysisReports.findAndCountAll({
      where: {},
      limit: 1
    });
    console.log('空条件查询结果:', emptyResult.count, '条记录');

    return { success: true, queryConditions: whereConditions, resultCount: result.count };

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  debugQueryConditions().then((result) => {
    console.log('\n' + '='.repeat(60));
    if (result.success) {
      console.log('✅ 调试完成！');
      if (result.resultCount === 0) {
        console.log('🎯 查询条件正常工作 - 没有匹配的记录（符合预期）');
      } else {
        console.log('⚠️  查询条件可能有问题 - 返回了不应该匹配的记录');
      }
    } else {
      console.log('❌ 调试失败');
    }
    console.log('='.repeat(60));
    process.exit(0);
  }).catch(error => {
    console.error('调试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { debugQueryConditions };
