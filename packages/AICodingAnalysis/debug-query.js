/**
 * 调试查询问题
 */

// 模拟生产环境
process.env.USER = 'root';

const { AiCodeAnalysisReports, AiCodeAnalysisFileDetails, initializeDatabase, getDatabaseStatus } = require('./models/AICodingAnalysisModels-simple.js');

async function debugQuery() {
  console.log('🔍 调试查询问题...\n');

  try {
    // 初始化数据库
    await initializeDatabase();
    console.log('数据库状态:', getDatabaseStatus());

    // 1. 直接使用 sequelize 查询
    console.log('\n1. 直接使用 sequelize 查询...');
    const [directResults] = await AiCodeAnalysisReports.sequelize.query('SELECT * FROM ai_code_analysis_reports ORDER BY created_at DESC LIMIT 5');
    console.log('直接查询结果数量:', directResults.length);
    if (directResults.length > 0) {
      console.log('第一条记录:', {
        id: directResults[0].id,
        pr_global_id: directResults[0].pr_global_id,
        pr_title: directResults[0].pr_title
      });
    }

    // 2. 使用模型查询（不带关联）
    console.log('\n2. 使用模型查询（不带关联）...');
    const modelResults = await AiCodeAnalysisReports.findAll({
      order: [['created_at', 'DESC']],
      limit: 5
    });
    console.log('模型查询结果数量:', modelResults.length);
    if (modelResults.length > 0) {
      console.log('第一条记录:', {
        id: modelResults[0].id,
        pr_global_id: modelResults[0].pr_global_id,
        pr_title: modelResults[0].pr_title
      });
    }

    // 3. 使用 findAndCountAll（不带关联）
    console.log('\n3. 使用 findAndCountAll（不带关联）...');
    const countResults = await AiCodeAnalysisReports.findAndCountAll({
      order: [['created_at', 'DESC']],
      limit: 5
    });
    console.log('findAndCountAll 结果:');
    console.log('- 总数:', countResults.count);
    console.log('- 返回记录数:', countResults.rows.length);

    // 4. 检查表名
    console.log('\n4. 检查表名...');
    console.log('模型表名:', AiCodeAnalysisReports.getTableName ? AiCodeAnalysisReports.getTableName() : '无法获取');
    console.log('详情模型表名:', AiCodeAnalysisFileDetails.getTableName ? AiCodeAnalysisFileDetails.getTableName() : '无法获取');

    // 5. 测试带关联的查询
    console.log('\n5. 测试带关联的查询...');
    try {
      const associationResults = await AiCodeAnalysisReports.findAndCountAll({
        include: [{
          model: AiCodeAnalysisFileDetails,
          as: 'fileDetails',
          required: false
        }],
        order: [['created_at', 'DESC']],
        limit: 5
      });
      console.log('关联查询结果:');
      console.log('- 总数:', associationResults.count);
      console.log('- 返回记录数:', associationResults.rows.length);
      if (associationResults.rows.length > 0) {
        console.log('- 第一条记录文件详情:', associationResults.rows[0].fileDetails ? associationResults.rows[0].fileDetails.length : 'undefined');
      }
    } catch (assocError) {
      console.error('关联查询失败:', assocError.message);
    }

    // 6. 检查模型是否正确定义
    console.log('\n6. 检查模型定义...');
    console.log('AiCodeAnalysisReports 类型:', typeof AiCodeAnalysisReports);
    console.log('AiCodeAnalysisReports 方法:', Object.getOwnPropertyNames(AiCodeAnalysisReports).filter(name => typeof AiCodeAnalysisReports[name] === 'function'));
    
    // 7. 检查 sequelize 实例
    console.log('\n7. 检查 sequelize 实例...');
    console.log('sequelize 类型:', typeof AiCodeAnalysisReports.sequelize);
    console.log('sequelize 方法:', Object.getOwnPropertyNames(AiCodeAnalysisReports.sequelize).slice(0, 10));

    return { success: true };

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  debugQuery().then((result) => {
    if (result.success) {
      console.log('\n✅ 调试完成！');
    } else {
      console.log('\n❌ 调试失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('调试出错:', error);
    process.exit(1);
  });
}

module.exports = { debugQuery };
