#!/usr/bin/env bash
echo "******************** start compile deploy **********************"

npm --registry=http://r.npm.sankuai.com install

rm -rf ./node_modules/*

npm --registry=http://r.npm.sankuai.com install --production

npm --registry=http://r.npm.sankuai.com install @fdfe/nest-runtime-nodejs-v2@latest

# 确保nest.yml文件在根目录和输出目录
echo "Ensuring nest.yml exists in output directories..."
# 检查nest.yml是否存在
if [ -f "nest.yml" ]; then
  # 复制到多个可能需要的位置
  cp -f nest.yml ../
  mkdir -p ../../dist
  cp -f nest.yml ../../dist/
  echo "nest.yml has been copied to output directories"
else
  echo "Warning: nest.yml not found in current directory!"
  # 创建基本的nest.yml
  cat > nest.yml << EOL
appKey: com.sankuai.fetalos.mp.wxapp

provider:
  runtime: nodejs
  
templateType: function_nodejs

functions:
  AICodingAnalysis:
    handler: index.AICodingAnalysis
    events:
      - http:
          method: post
EOL
  echo "Created basic nest.yml file"
  # 复制到多个可能需要的位置
  cp -f nest.yml ../
  mkdir -p ../../dist
  cp -f nest.yml ../../dist/
fi

echo "******************** end compile deploy **********************"
