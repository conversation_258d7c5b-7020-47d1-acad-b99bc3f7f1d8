version: v1
build:
  tools:
    node: 16.20.2
    git:
  cache:
    dirs:
      - node_modules
  run:
    workDir: packages/AICodingAnalysis
    cmd:
      - sh deploy/compile.sh
  target:
    distDir: packages/AICodingAnalysis
    files:
      - ./*

autodeploy:
  targetDir: /opt/meituan/apps/nest/
  preRun: cp -f nest.yml /opt/meituan/apps/nest/
  run: sh deploy/run.sh
  check: curl -v localhost:8080/health
  checkRetry: 10
  checkInterval: 10s
  tools:
    node: 16.20.2
