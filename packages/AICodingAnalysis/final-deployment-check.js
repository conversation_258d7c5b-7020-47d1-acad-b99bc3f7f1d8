/**
 * 最终部署检查 - 确认所有配置都正确
 */

const fs = require('fs');

// 模拟生产环境
process.env.USER = 'root';

async function finalDeploymentCheck() {
  console.log('🔍 最终部署检查...\n');

  let allChecksPass = true;

  try {
    // 1. 检查主入口文件
    console.log('1. 检查主入口文件 index.js...');
    const indexContent = fs.readFileSync('./index.js', 'utf8');
    
    if (indexContent.includes('AICodingAnalysisModels-working.js')) {
      console.log('✅ index.js 使用正确的模型文件');
    } else {
      console.log('❌ index.js 模型文件引用不正确');
      allChecksPass = false;
    }

    // 2. 检查模型文件是否存在
    console.log('\n2. 检查模型文件...');
    if (fs.existsSync('./models/AICodingAnalysisModels-working.js')) {
      console.log('✅ 工作模型文件存在');
    } else {
      console.log('❌ 工作模型文件不存在');
      allChecksPass = false;
    }

    // 3. 测试模型加载
    console.log('\n3. 测试模型加载...');
    const models = require('./models/AICodingAnalysisModels-working.js');
    console.log('✅ 模型加载成功');

    // 4. 测试数据库初始化
    console.log('\n4. 测试数据库初始化...');
    await models.initializeDatabase();
    const dbStatus = models.getDatabaseStatus();
    console.log('数据库状态:', dbStatus);
    
    if (dbStatus === 'REAL_DATABASE') {
      console.log('✅ 真实数据库连接成功');
    } else {
      console.log('⚠️  使用模拟数据库（本地环境正常）');
    }

    // 5. 测试模型功能
    console.log('\n5. 测试模型功能...');
    
    // 测试查询
    const queryResult = await models.AiCodeAnalysisReports.findAndCountAll({
      limit: 1
    });
    console.log('✅ 查询功能正常，记录数:', queryResult.count);

    // 测试插入（如果是真实数据库）
    if (dbStatus === 'REAL_DATABASE') {
      console.log('\n6. 测试插入功能...');
      const testData = {
        pr_global_id: `FINAL_CHECK_${Date.now()}`,
        pr_title: '最终检查测试',
        git_repository: 'https://git.example.com/final-check.git',
        git_branch: 'final-check',
        mis_number: 'final_check',
        detection_method: '最终检查',
        analysis_timestamp: new Date(),
        total_files: 1,
        total_lines: 100,
        changed_lines: 50,
        ai_generated_lines: 25,
        ai_code_ratio: 0.25
      };

      const insertResult = await models.AiCodeAnalysisReports.create(testData);
      console.log('✅ 插入功能正常，ID:', insertResult.id);

      // 验证插入的数据
      const verifyResult = await models.AiCodeAnalysisReports.findByPk(insertResult.id);
      if (verifyResult && verifyResult.pr_global_id === testData.pr_global_id) {
        console.log('✅ 数据验证成功');
      } else {
        console.log('❌ 数据验证失败');
        allChecksPass = false;
      }
    }

    // 6. 测试关联查询
    console.log('\n7. 测试关联查询...');
    try {
      const associationResult = await models.AiCodeAnalysisReports.findAndCountAll({
        include: [{
          model: models.AiCodeAnalysisFileDetails,
          as: 'fileDetails',
          required: false
        }],
        limit: 1
      });
      console.log('✅ 关联查询功能正常');
    } catch (assocError) {
      console.log('❌ 关联查询失败:', assocError.message);
      allChecksPass = false;
    }

    // 7. 检查 Sequelize 操作符
    console.log('\n8. 检查 Sequelize 操作符...');
    try {
      const Op = models.AiCodeAnalysisReports.sequelize.Sequelize.Op;
      if (Op && Op.like && Op.between) {
        console.log('✅ Sequelize 操作符可用');
      } else {
        console.log('❌ Sequelize 操作符不可用');
        allChecksPass = false;
      }
    } catch (opError) {
      console.log('❌ Sequelize 操作符检查失败:', opError.message);
      allChecksPass = false;
    }

    // 8. 模拟完整的 POST 请求处理
    console.log('\n9. 模拟完整的 POST 请求处理...');
    const mockPostData = {
      pr_global_id: `MOCK_POST_${Date.now()}`,
      pr_title: '模拟POST请求',
      git_repository: 'https://git.example.com/mock-post.git',
      git_branch: 'mock-post',
      mis_number: 'mock_user',
      detection_method: '模拟POST',
      analysis_timestamp: new Date().toISOString(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25,
      file_details: [
        {
          file_name: 'test/mock.js',
          file_type: 'js',
          total_lines: 100,
          changed_lines: 50,
          ai_matched_lines: 25,
          ai_code_ratio: 0.25,
          detection_method: '模拟POST'
        }
      ]
    };

    if (dbStatus === 'REAL_DATABASE') {
      // 开始事务
      const transaction = await models.AiCodeAnalysisReports.sequelize.transaction();
      
      try {
        // 创建主报告
        const reportData = {
          pr_global_id: mockPostData.pr_global_id,
          pr_title: mockPostData.pr_title,
          git_repository: mockPostData.git_repository,
          git_branch: mockPostData.git_branch,
          mis_number: mockPostData.mis_number,
          detection_method: mockPostData.detection_method,
          analysis_timestamp: new Date(mockPostData.analysis_timestamp),
          total_files: mockPostData.total_files,
          total_lines: mockPostData.total_lines,
          changed_lines: mockPostData.changed_lines,
          ai_generated_lines: mockPostData.ai_generated_lines,
          ai_code_ratio: mockPostData.ai_code_ratio
        };

        const newReport = await models.AiCodeAnalysisReports.create(reportData, { transaction });
        
        // 创建文件详情
        const fileDetailsData = mockPostData.file_details.map(detail => ({
          report_id: newReport.id,
          pr_global_id: mockPostData.pr_global_id,
          file_name: detail.file_name,
          file_type: detail.file_type,
          total_lines: detail.total_lines,
          changed_lines: detail.changed_lines,
          ai_matched_lines: detail.ai_matched_lines,
          ai_code_ratio: detail.ai_code_ratio,
          detection_method: detail.detection_method
        }));

        await models.AiCodeAnalysisFileDetails.bulkCreate(fileDetailsData, { transaction });
        
        // 提交事务
        await transaction.commit();
        console.log('✅ 完整 POST 请求处理成功');
        
      } catch (error) {
        await transaction.rollback();
        console.log('❌ POST 请求处理失败:', error.message);
        allChecksPass = false;
      }
    } else {
      console.log('⚠️  跳过 POST 请求测试（模拟数据库环境）');
    }

    // 9. 模拟 GET 请求处理
    console.log('\n10. 模拟 GET 请求处理...');
    try {
      const getResult = await models.AiCodeAnalysisReports.findAndCountAll({
        include: [{
          model: models.AiCodeAnalysisFileDetails,
          as: 'fileDetails',
          required: false
        }],
        order: [['analysis_timestamp', 'DESC']],
        offset: 0,
        limit: 10
      });
      
      console.log('✅ GET 请求处理成功，返回记录数:', getResult.count);
    } catch (getError) {
      console.log('❌ GET 请求处理失败:', getError.message);
      allChecksPass = false;
    }

  } catch (error) {
    console.error('❌ 检查过程出错:', error.message);
    allChecksPass = false;
  }

  // 最终结果
  console.log('\n' + '='.repeat(50));
  if (allChecksPass) {
    console.log('🎉 所有检查通过！可以安全部署！');
    console.log('\n📋 部署清单:');
    console.log('✅ 模型文件配置正确');
    console.log('✅ 数据库连接正常');
    console.log('✅ POST 功能正常');
    console.log('✅ GET 功能正常');
    console.log('✅ 关联查询正常');
    console.log('✅ 事务处理正常');
    
    console.log('\n🚀 部署命令:');
    console.log('nest deploy -e test');
    
    console.log('\n🧪 测试命令:');
    console.log('POST: curl --location --request POST "https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c" --header "Content-Type: application/json" --data "..."');
    console.log('GET:  curl --location --request GET "https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c?page=1&pageSize=10"');
    
  } else {
    console.log('❌ 部分检查失败，请修复后再部署！');
  }
  console.log('='.repeat(50));

  return allChecksPass;
}

if (require.main === module) {
  finalDeploymentCheck().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('检查失败:', error);
    process.exit(1);
  });
}

module.exports = { finalDeploymentCheck };
