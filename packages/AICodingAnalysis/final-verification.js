/**
 * 最终验证脚本 - 确认所有配置都正确
 */

const fs = require('fs');
const path = require('path');

function checkFileContent() {
  console.log('🔍 检查代码配置...\n');

  // 检查主模型文件
  console.log('1. 检查 AICodingAnalysisModels.js:');
  const modelFile = fs.readFileSync('./models/AICodingAnalysisModels.js', 'utf8');
  
  if (modelFile.includes('zebra-proxy-sequelize')) {
    console.log('❌ 还在使用 zebra-proxy-sequelize');
    return false;
  } else {
    console.log('✅ 已更新为原生 Sequelize');
  }

  if (modelFile.includes('*************')) {
    console.log('✅ 使用正确的数据库地址');
  } else {
    console.log('❌ 数据库地址不正确');
    return false;
  }

  if (modelFile.includes('swift')) {
    console.log('✅ 使用正确的数据库名');
  } else {
    console.log('❌ 数据库名不正确');
    return false;
  }

  // 检查主入口文件
  console.log('\n2. 检查 index.js:');
  const indexFile = fs.readFileSync('./index.js', 'utf8');
  
  if (indexFile.includes('AICodingAnalysisModels.js')) {
    console.log('✅ 引用正确的模型文件');
  } else {
    console.log('❌ 模型文件引用不正确');
    return false;
  }

  // 检查 package.json
  console.log('\n3. 检查依赖包:');
  const packageFile = fs.readFileSync('./package.json', 'utf8');
  const packageData = JSON.parse(packageFile);
  
  if (packageData.dependencies && packageData.dependencies.sequelize) {
    console.log('✅ sequelize 已安装');
  } else {
    console.log('❌ sequelize 未安装');
    return false;
  }

  if (packageData.dependencies && packageData.dependencies.mysql2) {
    console.log('✅ mysql2 已安装');
  } else {
    console.log('❌ mysql2 未安装');
    return false;
  }

  return true;
}

function showDeploymentSummary() {
  console.log('\n📋 部署配置总结:\n');
  
  console.log('🔧 数据库配置:');
  console.log('  - 类型: MySQL 直连');
  console.log('  - 地址: *************:5002');
  console.log('  - 数据库: swift');
  console.log('  - 用户: rds_Analysis');
  console.log('  - 权限: 已验证 ✅');
  
  console.log('\n📦 依赖包:');
  console.log('  - sequelize: 原生 ORM');
  console.log('  - mysql2: MySQL 驱动');
  
  console.log('\n🚀 部署命令:');
  console.log('  nest deploy -e test');
  
  console.log('\n🧪 测试命令:');
  console.log(`  curl --location --request POST 'https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c' \\
  --header 'Content-Type: application/json' \\
  --data '{
    "pr_global_id": "FINAL_TEST_001",
    "pr_title": "最终测试",
    "git_repository": "https://git.example.com/final-test.git",
    "git_branch": "final-test",
    "mis_number": "final_user",
    "detection_method": "最终测试",
    "analysis_timestamp": "2025-05-24T15:30:00.000Z",
    "total_files": 1,
    "total_lines": 100,
    "changed_lines": 50,
    "ai_generated_lines": 25,
    "ai_code_ratio": 0.25
  }'`);
}

function showExpectedLogs() {
  console.log('\n📊 期望的日志输出:\n');
  
  console.log('✅ 成功的日志:');
  console.log(`
环境检测: { NODE_ENV: undefined, USER: 'root', isLocal: false }
生产环境：使用直连 MySQL
MySQL 配置: {
  host: '*************',
  port: 5002,
  username: 'rds_Analysis',
  database: 'swift',
  dialect: 'mysql'
}
MySQL 连接创建成功
尝试连接数据库...
数据库连接成功
尝试同步数据库表...
数据库表同步成功
数据库状态: REAL_DATABASE
收到分析结果数据: {...}
开始数据库事务...
准备创建主报告记录...
主报告记录创建成功，ID: 123
数据库事务提交成功
分析结果保存成功
  `);

  console.log('\n❌ 如果失败会看到:');
  console.log(`
数据库状态: MOCK_DATABASE
⚠️ 警告：当前使用模拟数据库，数据不会真正保存！
  `);
}

function runFinalVerification() {
  console.log('🎯 最终验证开始...\n');
  
  const configOK = checkFileContent();
  
  if (configOK) {
    console.log('\n🎉 所有配置检查通过！');
    console.log('\n✅ 准备就绪，可以部署了！');
    
    showDeploymentSummary();
    showExpectedLogs();
    
    console.log('\n🚀 下一步操作:');
    console.log('1. 执行: nest deploy -e test');
    console.log('2. 测试 POST 请求');
    console.log('3. 检查日志确认 "数据库状态: REAL_DATABASE"');
    console.log('4. 测试 GET 请求验证数据保存');
    
  } else {
    console.log('\n❌ 配置检查失败，请修复后再部署');
  }
  
  console.log('\n📝 重要提醒:');
  console.log('- 确保云函数能访问数据库地址 *************:5002');
  console.log('- 数据库表已存在，无需手动创建');
  console.log('- 用户权限已验证，可以正常读写');
  
  return configOK;
}

if (require.main === module) {
  const success = runFinalVerification();
  process.exit(success ? 0 : 1);
}

module.exports = { runFinalVerification };
