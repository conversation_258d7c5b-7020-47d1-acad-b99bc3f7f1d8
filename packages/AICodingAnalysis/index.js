/**
 * AICodingAnalysis - Webhook处理服务
 * 用于接收PR相关webhook请求并触发Talos流水线部署
 */
// 使用CommonJS语法导入SDK - 正确的方式
const getAPI = require('@nibfe/talos-public-api').default;
const talosConfig = require('./talos-config.js');
const modelsModule = require('./models/AICodingAnalysisModels-working.js');
const dashboardModelsModule = require('./models/DashboardModels.js');

/**
 * 统一处理AI占比数据格式化
 * @param {number} ratio - 原始AI占比数据
 * @returns {number} - 格式化后的百分比数值（0-100）
 */
function formatAIRatio(ratio) {
  if (!ratio || ratio === 0) return 0;
  
  // 如果数据大于1，说明数据库中存储的已经是百分比形式
  if (ratio > 1) {
    return Math.max(0, Math.min(ratio, 100));
  } else {
    // 如果数据小于等于1，说明数据库中存储的是小数形式，需要转换为百分比
    return Math.max(0, Math.min(ratio * 100, 100));
  }
}

/**
 * 获取 CORS 头信息
 */
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400'
  };
}

// Talos API配置
const TALOS_APP_ID = talosConfig.TALOS_APP_ID;
const TALOS_CONFIG_ID = talosConfig.TALOS_CONFIG_ID;
const TALOS_OP_MIS = talosConfig.TALOS_OP_MIS;

// 按照官方示例直接解构获取publish方法
const { publish } = getAPI({
  clientId: talosConfig.CLIENT_ID,
  clientSecret: talosConfig.CLIENT_SECRET,
  env: 'production'
});

console.log('Talos API客户端初始化成功，publish函数可用:', !!publish);

/**
 * 触发Talos流水线部署
 * @param {Object} webhookData - 收到的webhook数据
 * @returns {Promise<Object>} - Talos API响应
 */
async function triggerTalosPipeline(webhookData) {
  try {
    if (!webhookData || !webhookData.merge_request_id) {
      throw new Error('无效的webhook数据：缺少merge_request_id');
    }

    console.log('解析webhook数据...');

    // 从新数据结构中提取字段
    const author = webhookData.user_name;
    const sourceRepo = webhookData.source.ssh_url;
    const sourceBranch = webhookData.from.replace('refs/heads/', '');
    const targetBranch = webhookData.to.replace('refs/heads/', '');
    const title = webhookData.title;
    const prGlobalId = webhookData.merge_request_id;

    console.log('提取的数据:', {
      author,
      sourceRepo,
      sourceBranch,
      targetBranch,
      title,
      prGlobalId
    });

    // 从webhook数据中提取所需的环境变量 - 按照官方示例格式
    const envs = [
      {
        key: 'pr_global_id',
        value: String(prGlobalId)
      },
      {
        key: 'author',
        value: author
      },
      {
        key: 'source_ref',
        value: sourceBranch
      },
      {
        key: 'target_ref',
        value: targetBranch
      },
      {
        key: 'title',
        value: title
      },
      {
        key: 'GIT_REPO',
        value: String(sourceRepo)
      },
      {
        key: 'GIT_BRANCH',
        value: String(sourceBranch)
      },
    ];

    // 添加时间戳（如果有的话）
    if (webhookData.comment && webhookData.comment.updated_at) {
      envs.push({
        key: 'updated_at',
        value: webhookData.comment.updated_at
      });
    }

    // 构造Talos API请求参数 - 严格按照官方示例格式
    const requestData = {
      id: TALOS_APP_ID,
      cid: TALOS_CONFIG_ID,
      op: author || TALOS_OP_MIS, // 使用op字段
      comment: {
        note: `自动部署: ${title || 'PR触发部署'}`
      },
      envs: envs, // 环境变量放在顶层
      config: {
        refType: 'branch',
        branch: sourceBranch
      }
    };

    console.log('触发Talos部署请求:', JSON.stringify(requestData, null, 2));

    // 检查SDK是否正确初始化
    if (!publish || typeof publish !== 'function') {
      throw new Error('Talos API客户端未正确初始化');
    }

    // 调用Talos API触发部署
    console.log('开始调用Talos API...', requestData);
    const response = await publish(requestData);
    console.log('Talos API响应:', response);
    return response;
  } catch (error) {
    console.error('触发Talos部署失败:', error);
    throw error;
  }
}

module.exports.AICodingAnalysis = async (event, context) => {
  console.log('event', event)

  // 检查是否为 OPTIONS 预检请求
  const method = event.httpMethod || event.method || (event.extensions && event.extensions.request && event.extensions.request.method) || 'POST';

  if (method.toUpperCase() === 'OPTIONS') {
    // 处理 CORS 预检请求
    return {
      statusCode: 200,
      headers: getCorsHeaders(),
      body: ''
    };
  }

  // 如果请求包含body，则尝试处理
  if (event.extensions && event.extensions.request && event.extensions.request.body) {
    try {
      const requestBody = event.extensions.request.body;
      console.log('成功处理请求数据:', requestBody);

      try {
        // 触发Talos流水线部署
        const talosResponse = await triggerTalosPipeline(requestBody);

        return {
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            ...getCorsHeaders()
          },
          body: {
            merge_request_id: requestBody.merge_request_id,
            status: 7,
            can_merge: 0,
            success: true,
            message: '成功触发Talos流水线部署',
            data: {
              talosResponse,
              webhookData: requestBody
            }
          }
        };
      } catch (talosError) {
        // 返回Talos相关错误，但仍然返回200状态码
        console.error('Talos处理错误:', talosError.message);
        return {
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            ...getCorsHeaders()
          },
          body: {
            success: false,
            message: `Talos流水线触发失败: ${talosError.message}`,
            error: talosError.message,
            received: true,
            webhookData: requestBody
          }
        };
      }
    } catch (error) {
      console.error('请求处理错误:', error);
      return {
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          ...getCorsHeaders()
        },
        body: {
          success: false,
          message: '请求处理失败: ' + error.message
        }
      };
    }
  }

  return {
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      ...getCorsHeaders()
    },
    body: {
      success: true,
      message: 'AICodingAnalysis API已准备就绪，但未收到有效请求数据'
    }
  }
}

/**
 * AI代码分析结果管理函数
 * GET: 分页查询分析结果，支持多条件搜索
 * POST: 存储新的分析结果
 */
module.exports.AICodingAnalysisResult = async (event, context) => {
  // 安全地记录 event 对象，避免循环引用
  const safeEvent = {
    httpMethod: event.httpMethod,
    method: event.method,
    body: event.body ? (typeof event.body === 'string' ? event.body.substring(0, 200) + '...' : '[Object]') : undefined,
    queryStringParameters: event.queryStringParameters,
    pathParameters: event.pathParameters,
    headers: event.headers,
    requestContext: event.requestContext ? {
      httpMethod: event.requestContext.httpMethod,
      path: event.requestContext.path
    } : undefined,
    extensions: event.extensions ? {
      request: event.extensions.request ? {
        method: event.extensions.request.method,
        url: event.extensions.request.url,
        path: event.extensions.request.path
      } : undefined
    } : undefined
  };

  console.log('AICodingAnalysisResult event:', JSON.stringify(safeEvent, null, 2));

  // 多种方式获取 HTTP 方法
  let method = 'GET'; // 默认值

  if (event.httpMethod) {
    method = event.httpMethod.toUpperCase();
  } else if (event.method) {
    method = event.method.toUpperCase();
  } else if (event.extensions && event.extensions.request && event.extensions.request.method) {
    method = event.extensions.request.method.toUpperCase();
  } else if (event.requestContext && event.requestContext.httpMethod) {
    method = event.requestContext.httpMethod.toUpperCase();
  } else {
    // 如果有请求体数据，很可能是 POST 请求
    if (event.body || (event.extensions && event.extensions.request && event.extensions.request.body)) {
      method = 'POST';
    }
  }

  console.log('检测到的 HTTP 方法:', method);

  try {
    if (method === 'OPTIONS') {
      // 处理 CORS 预检请求
      return {
        statusCode: 200,
        headers: getCorsHeaders(),
        body: ''
      };
    } else if (method === 'GET') {
      return await handleGetAnalysisResults(event);
    } else if (method === 'POST') {
      return await handlePostAnalysisResult(event);
    } else {
      return {
        statusCode: 405,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          ...getCorsHeaders()
        },
        body: {
          success: false,
          message: `不支持的HTTP方法: ${method}`
        }
      };
    }
  } catch (error) {
    console.error('AICodingAnalysisResult处理错误:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        ...getCorsHeaders()
      },
      body: {
        success: false,
        message: '服务器内部错误: ' + error.message
      }
    };
  }
}

/**
 * 处理GET请求 - 分页查询分析结果
 */
async function handleGetAnalysisResults(event) {
  try {
    // 初始化数据库连接
    await modelsModule.initializeDatabase();
    // 解析查询参数
    let queryParams = event.queryStringParameters || {};

    // 如果 queryStringParameters 为空，尝试从 URL 中解析
    if (Object.keys(queryParams).length === 0 && event.extensions && event.extensions.request && event.extensions.request.url) {
      const url = event.extensions.request.url;
      console.log('从 URL 解析查询参数:', url);

      try {
        const urlObj = new URL(url, 'http://localhost');
        queryParams = {};
        for (const [key, value] of urlObj.searchParams) {
          queryParams[key] = value;
        }
        console.log('解析后的查询参数:', queryParams);
      } catch (urlError) {
        console.error('URL 解析失败:', urlError.message);
      }
    }
    const {
      page = 1,
      pageSize = 20,
      analysisTime,
      misNumber,
      prId,
      gitRepository,
      gitBranch,
      startTime,
      endTime
    } = queryParams;

    // 动态获取模型对象
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;

    // 获取 Sequelize 操作符
    const { Op } = AiCodeAnalysisReports.sequelize.Sequelize;

    // 构建查询条件
    const whereConditions = {};

    // 时间条件处理（优先级：startTime/endTime > analysisTime）
    if (startTime && endTime) {
      whereConditions.analysis_timestamp = {
        [Op.between]: [startTime, endTime]
      };
    } else if (startTime) {
      whereConditions.analysis_timestamp = {
        [Op.gte]: startTime
      };
    } else if (endTime) {
      whereConditions.analysis_timestamp = {
        [Op.lte]: endTime
      };
    } else if (analysisTime) {
      whereConditions.analysis_timestamp = analysisTime;
    }

    if (misNumber) {
      whereConditions.mis_number = {
        [Op.like]: `%${misNumber}%`
      };
    }

    if (prId) {
      whereConditions.pr_global_id = {
        [Op.like]: `%${prId}%`
      };
    }

    if (gitRepository) {
      whereConditions.git_repository = {
        [Op.like]: `%${gitRepository}%`
      };
    }

    if (gitBranch) {
      whereConditions.git_branch = {
        [Op.like]: `%${gitBranch}%`
      };
    }

    // 调试日志
    console.log('查询参数:', { misNumber, prId, gitRepository, gitBranch, startTime, endTime });
    console.log('构建的查询条件数量:', Object.keys(whereConditions).length);

    // 计算分页参数
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);

    // 执行查询
    const result = await AiCodeAnalysisReports.findAndCountAll({
      where: whereConditions,
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      order: [
        ['analysis_timestamp', 'DESC'],
        // 按 AI 代码占比从高到低排序 fileDetails
        [{ model: AiCodeAnalysisFileDetails, as: 'fileDetails' }, 'ai_code_ratio', 'DESC']
      ],
      offset: offset,
      limit: limit,
      // 修复 count 问题：只计算主记录数，不包含关联记录
      distinct: true,
      col: 'id'
    });

    // 计算分页信息 - 使用修正后的计数
    const totalCount = result.count;
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = parseInt(page);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        ...getCorsHeaders()
      },
      body: {
        success: true,
        message: '查询成功',
        data: {
          list: result.rows,
          pagination: {
            currentPage: currentPage,
            pageSize: limit,
            totalCount: totalCount,
            totalPages: totalPages,
            hasNext: currentPage < totalPages,
            hasPrev: currentPage > 1
          }
        }
      }
    };
  } catch (error) {
    console.error('查询分析结果失败:', error);
    throw error;
  }
}

/**
 * 处理POST请求 - 存储新的分析结果
 */
async function handlePostAnalysisResult(event) {
  try {
    // 初始化数据库连接
    await modelsModule.initializeDatabase();

    // 动态获取模型对象
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;

    // 检查数据库状态
    const dbStatus = modelsModule.getDatabaseStatus();
    console.log('数据库状态:', dbStatus);

    // 解析请求体
    let requestBody;
    if (event.extensions && event.extensions.request && event.extensions.request.body) {
      requestBody = event.extensions.request.body;
    } else if (event.body) {
      requestBody = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      throw new Error('请求体为空');
    }

    console.log('收到分析结果数据:', JSON.stringify(requestBody, null, 2));

    // 验证必填字段
    const requiredFields = [
      'pr_global_id', 'pr_title', 'git_repository', 'git_branch',
      'detection_method', 'analysis_timestamp'
    ];

    for (const field of requiredFields) {
      if (!requestBody[field]) {
        throw new Error(`缺少必填字段: ${field}`);
      }
    }

    // 检查pr_global_id是否已存在
    const existingReport = await AiCodeAnalysisReports.findOne({
      where: { pr_global_id: requestBody.pr_global_id }
    });

    if (existingReport) {
      return {
        statusCode: 409,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          ...getCorsHeaders()
        },
        body: {
          success: false,
          message: `PR ID ${requestBody.pr_global_id} 已存在，不能重复提交`,
          data: {
            existingReport: existingReport
          }
        }
      };
    }

    // 开始事务
    console.log('开始数据库事务...');
    const transaction = await AiCodeAnalysisReports.sequelize.transaction();

    try {
      // 创建主报告记录
      const reportData = {
        pr_global_id: requestBody.pr_global_id,
        pr_title: requestBody.pr_title,
        git_repository: requestBody.git_repository,
        git_branch: requestBody.git_branch,
        mis_number: requestBody.mis_number || null,
        detection_method: requestBody.detection_method,
        analysis_timestamp: new Date(requestBody.analysis_timestamp),
        total_files: requestBody.total_files || 0,
        total_lines: requestBody.total_lines || 0,
        changed_lines: requestBody.changed_lines || 0,
        ai_generated_lines: requestBody.ai_generated_lines || 0,
        ai_code_ratio: requestBody.ai_code_ratio || 0.0
      };

      console.log('准备创建主报告记录:', JSON.stringify(reportData, null, 2));
      const newReport = await AiCodeAnalysisReports.create(reportData, { transaction });
      console.log('主报告记录创建成功，ID:', newReport.id);

      // 如果有文件详情数据，创建详情记录
      if (requestBody.file_details && Array.isArray(requestBody.file_details)) {
        console.log('准备创建文件详情记录，数量:', requestBody.file_details.length);
        const fileDetailsData = requestBody.file_details.map(detail => ({
          report_id: newReport.id,
          pr_global_id: requestBody.pr_global_id,
          file_name: detail.file_name,
          file_type: detail.file_type,
          total_lines: detail.total_lines || 0,
          changed_lines: detail.changed_lines || 0,
          ai_matched_lines: detail.ai_matched_lines || 0,
          ai_code_ratio: detail.ai_code_ratio || 0.0,
          detection_method: detail.detection_method || requestBody.detection_method
        }));

        await AiCodeAnalysisFileDetails.bulkCreate(fileDetailsData, { transaction });
        console.log('文件详情记录创建成功');
      }

      // 提交事务
      console.log('提交数据库事务...');
      await transaction.commit();
      console.log('数据库事务提交成功');

      // 查询完整的结果（包含关联数据）
      const completeResult = await AiCodeAnalysisReports.findByPk(newReport.id, {
        include: [{
          model: AiCodeAnalysisFileDetails,
          as: 'fileDetails'
        }]
      });

      return {
        statusCode: 201,
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          ...getCorsHeaders()
        },
        body: {
          success: true,
          message: '分析结果保存成功',
          data: completeResult
        }
      };

    } catch (error) {
      // 回滚事务
      if (transaction) {
        try {
          await transaction.rollback();
          console.log('数据库事务已回滚');
        } catch (rollbackError) {
          console.error('事务回滚失败:', rollbackError);
        }
      }
      throw error;
    }

  } catch (error) {
    console.error('保存分析结果失败:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    throw error;
  }
}

/**
 * AI代码分析大盘 - 数据可视化分析
 * GET: 获取大盘统计数据
 */
module.exports.AICodingDashboard = async (event, context) => {
  // 安全地记录 event 对象，避免循环引用
  const safeEvent = {
    httpMethod: event.httpMethod,
    queryStringParameters: event.queryStringParameters,
    headers: event.headers ? {
      'content-type': event.headers['content-type'],
      'user-agent': event.headers['user-agent']
    } : undefined,
    extensions: event.extensions ? {
      request: event.extensions.request ? {
        method: event.extensions.request.method,
        url: event.extensions.request.url,
        path: event.extensions.request.path
      } : undefined
    } : undefined
  };

  console.log('AICodingDashboard event:', JSON.stringify(safeEvent, null, 2));

  // 检查是否为 OPTIONS 预检请求
  const method = event.httpMethod || event.method || (event.extensions && event.extensions.request && event.extensions.request.method) || 'GET';

  if (method.toUpperCase() === 'OPTIONS') {
    // 处理 CORS 预检请求
    return {
      statusCode: 200,
      headers: getCorsHeaders(),
      body: ''
    };
  }

  try {
    // 初始化大盘数据库连接
    await dashboardModelsModule.initializeDashboardDatabase();

    // 检查数据库状态
    const dbStatus = dashboardModelsModule.getDashboardDatabaseStatus();
    console.log('大盘数据库状态:', dbStatus);

    // 解析查询参数
    let queryParams = event.queryStringParameters || {};

    // 如果 queryStringParameters 为空，尝试从 URL 中解析
    if (Object.keys(queryParams).length === 0 && event.extensions && event.extensions.request && event.extensions.request.url) {
      const url = event.extensions.request.url;
      console.log('从 URL 解析大盘查询参数:', url);

      try {
        const urlObj = new URL(url, 'http://localhost');
        queryParams = {};
        for (const [key, value] of urlObj.searchParams) {
          queryParams[key] = value;
        }
        console.log('解析后的大盘查询参数:', queryParams);
      } catch (urlError) {
        console.error('URL 解析失败:', urlError.message);
      }
    }

    const {
      timeRange = '30d',
      startDate,
      endDate,
      repository,
      developer,
      fileType
    } = queryParams;

    console.log('大盘查询参数:', { timeRange, startDate, endDate, repository, developer, fileType });

    // 获取大盘数据
    const dashboardData = await getDashboardAnalytics({
      timeRange,
      startDate,
      endDate,
      repository,
      developer,
      fileType
    });

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        ...getCorsHeaders()
      },
      body: {
        success: true,
        message: '大盘数据获取成功',
        data: dashboardData
      }
    };

  } catch (error) {
    console.error('获取大盘数据失败:', error);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        ...getCorsHeaders()
      },
      body: {
        success: false,
        message: '获取大盘数据失败',
        error: error.message
      }
    };
  }
};

/**
 * 获取大盘分析数据
 */
async function getDashboardAnalytics(params) {
  const { timeRange, startDate, endDate, repository, developer, fileType } = params;

  // 动态获取模型对象
  const DashboardReports = dashboardModelsModule.DashboardReports;
  const DashboardFileDetails = dashboardModelsModule.DashboardFileDetails;
  const DashboardHistoricalRecords = dashboardModelsModule.DashboardHistoricalRecords;

  // 计算时间范围
  const timeFilter = getTimeFilter(timeRange, startDate, endDate);
  console.log('时间过滤条件:', timeFilter);

  // 构建基础查询条件
  const baseWhereConditions = {
    ...timeFilter
  };

  if (repository) {
    baseWhereConditions.git_repository = {
      [DashboardReports.sequelize.Sequelize.Op.like]: `%${repository}%`
    };
  }

  if (developer) {
    baseWhereConditions.mis_number = {
      [DashboardReports.sequelize.Sequelize.Op.like]: `%${developer}%`
    };
  }

  console.log('基础查询条件:', JSON.stringify(baseWhereConditions, null, 2));

  // 并行获取各种统计数据
  const [
    overview,
    trends,
    rankings,
    distributions,
    recentActivities
  ] = await Promise.all([
    getOverviewStats(DashboardReports, DashboardFileDetails, baseWhereConditions, timeFilter),
    getTrendStats(DashboardReports, baseWhereConditions, timeRange),
    getRankingStats(DashboardReports, DashboardFileDetails, baseWhereConditions, fileType),
    getDistributionStats(DashboardReports, DashboardFileDetails, DashboardHistoricalRecords, baseWhereConditions),
    getRecentActivities(DashboardReports, baseWhereConditions)
  ]);

  return {
    overview,
    trends,
    rankings,
    distributions,
    recentActivities,
    metadata: {
      generatedAt: new Date().toISOString(),
      timeRange,
      filters: { repository, developer, fileType }
    }
  };
}

/**
 * 获取时间过滤条件
 */
function getTimeFilter(timeRange, startDate, endDate) {
  const now = new Date();
  let start, end;

  if (timeRange === 'custom' && startDate && endDate) {
    start = new Date(startDate);
    end = new Date(endDate);
  } else {
    // 预设时间范围
    end = now;
    switch (timeRange) {
      case '7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  return {
    analysis_timestamp: {
      [dashboardModelsModule.mainSequelize.Sequelize.Op.between]: [start, end]
    }
  };
}

/**
 * 获取总览统计数据
 */
async function getOverviewStats(DashboardReports, DashboardFileDetails, whereConditions, timeFilter) {
  console.log('获取总览统计数据...');

  // 获取模型对象
  const DashboardHistoricalRecords = dashboardModelsModule.DashboardHistoricalRecords;

  // 当前期间统计 (不包含 activeDevelopers 和 activeRepos)
  const currentStats = await DashboardReports.findOne({
    where: whereConditions,
    attributes: [
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('pr_global_id'))), 'totalPRs'],
      [DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'avgAIRatio']
    ],
    raw: true
  });

  // 从 ai_code_gen_records 表统计活跃开发者数量
  let activeDevelopers = 0;
  try {
    // 先尝试无时间限制的统计（获取所有活跃开发者）
    const allDeveloperStats = await DashboardHistoricalRecords.findOne({
      attributes: [
        [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.fn('DISTINCT', DashboardHistoricalRecords.sequelize.col('git_username'))), 'activeDevelopers']
      ],
      where: {
        git_username: {
          [DashboardHistoricalRecords.sequelize.Sequelize.Op.and]: [
            { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null },
            { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: '' }
          ]
        }
      },
      raw: true
    });

    activeDevelopers = parseInt(allDeveloperStats?.activeDevelopers) || 0;
    console.log(`从历史记录表统计到活跃开发者: ${activeDevelopers} 人 (全部时间)`);
  } catch (error) {
    console.warn('获取活跃开发者统计失败，使用主表数据:', error.message);
    // 如果历史记录表查询失败，回退到主表的 mis_number 统计
    const fallbackStats = await DashboardReports.findOne({
      where: whereConditions,
      attributes: [
        [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('mis_number'))), 'activeDevelopers']
      ],
      raw: true
    });
    activeDevelopers = parseInt(fallbackStats?.activeDevelopers) || 0;
  }

  // 从 ai_code_gen_records 表统计活跃仓库数量
  let activeRepos = 0;
  try {
    const allRepoStats = await DashboardHistoricalRecords.findOne({
      attributes: [
        [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.fn('DISTINCT', DashboardHistoricalRecords.sequelize.col('git_repository'))), 'activeRepos']
      ],
      where: {
        git_repository: {
          [DashboardHistoricalRecords.sequelize.Sequelize.Op.and]: [
            { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null },
            { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: '' }
          ]
        }
      },
      raw: true
    });

    activeRepos = parseInt(allRepoStats?.activeRepos) || 0;
    console.log(`从历史记录表统计到活跃仓库: ${activeRepos} 个 (全部时间)`);
  } catch (error) {
    console.warn('获取活跃仓库统计失败，使用主表数据:', error.message);
    // 如果历史记录表查询失败，回退到主表的 git_repository 统计
    const fallbackRepoStats = await DashboardReports.findOne({
      where: whereConditions,
      attributes: [
        [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('git_repository'))), 'activeRepos']
      ],
      raw: true
    });
    activeRepos = parseInt(fallbackRepoStats?.activeRepos) || 0;
  }

  // 计算上期对比数据（用于增长率计算）
  const previousTimeFilter = getPreviousTimeFilter(timeFilter);
  const previousStats = await DashboardReports.findOne({
    where: {
      ...whereConditions,
      analysis_timestamp: previousTimeFilter.analysis_timestamp
    },
    attributes: [
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('pr_global_id'))), 'totalPRs'],
      [DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'avgAIRatio']
    ],
    raw: true
  });

  // 使用统一的格式化函数处理 AI 占比数据
  const avgAIRatio = formatAIRatio(currentStats.avgAIRatio);
  console.log(`处理后的平均 AI 占比: ${avgAIRatio}%`);

  // 计算增长率
  const prGrowth = calculateGrowthRate(currentStats.totalPRs, previousStats?.totalPRs || 0);
  const aiRatioGrowth = calculateGrowthRate(avgAIRatio, previousStats?.avgAIRatio || 0);

  return {
    totalPRs: parseInt(currentStats.totalPRs) || 0,
    avgAIRatio: parseFloat(avgAIRatio.toFixed(1)),
    activeRepos: activeRepos,
    activeDevelopers: activeDevelopers,
    trends: {
      prGrowth: parseFloat(prGrowth.toFixed(1)),
      aiRatioGrowth: parseFloat(aiRatioGrowth.toFixed(1))
    }
  };
}

/**
 * 获取趋势统计数据
 */
async function getTrendStats(DashboardReports, whereConditions, timeRange) {
  console.log('获取趋势统计数据...');

  // 根据时间范围确定分组间隔
  const groupBy = timeRange === '7d' ? 'day' : timeRange === '30d' ? 'day' : 'week';

  const dateFormat = groupBy === 'day' ? '%Y-%m-%d' : '%Y-%u';

  const trendData = await DashboardReports.findAll({
    where: whereConditions,
    attributes: [
      [DashboardReports.sequelize.fn('DATE_FORMAT', DashboardReports.sequelize.col('analysis_timestamp'), dateFormat), 'date'],
      [DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'aiRatio'],
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('pr_global_id'))), 'prCount']
    ],
    group: [DashboardReports.sequelize.fn('DATE_FORMAT', DashboardReports.sequelize.col('analysis_timestamp'), dateFormat)],
    order: [[DashboardReports.sequelize.fn('DATE_FORMAT', DashboardReports.sequelize.col('analysis_timestamp'), dateFormat), 'ASC']],
    raw: true
  });

  return {
    daily: trendData.map(item => ({
      date: item.date,
      aiRatio: parseFloat(formatAIRatio(item.aiRatio).toFixed(1)),
      prCount: parseInt(item.prCount)
    }))
  };
}

/**
 * 获取排行榜统计数据
 */
async function getRankingStats(DashboardReports, DashboardFileDetails, whereConditions, fileType) {
  console.log('获取排行榜统计数据...');

  // 仓库排行榜
  const repositories = await DashboardReports.findAll({
    where: whereConditions,
    attributes: [
      'git_repository',
      [DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'aiRatio'],
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('pr_global_id'))), 'prCount'],
      [DashboardReports.sequelize.fn('SUM', DashboardReports.sequelize.col('ai_generated_lines')), 'totalAILines']
    ],
    group: ['git_repository'],
    order: [[DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'DESC']],
    limit: 10,
    raw: true
  });

  // 开发者排行榜
  const developers = await DashboardReports.findAll({
    where: {
      ...whereConditions,
      mis_number: { [DashboardReports.sequelize.Sequelize.Op.ne]: null }
    },
    attributes: [
      'mis_number',
      [DashboardReports.sequelize.fn('SUM', DashboardReports.sequelize.col('ai_generated_lines')), 'aiLines'],
      [DashboardReports.sequelize.fn('SUM', DashboardReports.sequelize.col('total_lines')), 'totalLines'],
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.fn('DISTINCT', DashboardReports.sequelize.col('pr_global_id'))), 'prCount']
    ],
    group: ['mis_number'],
    order: [[DashboardReports.sequelize.fn('SUM', DashboardReports.sequelize.col('ai_generated_lines')), 'DESC']],
    limit: 10,
    raw: true
  });

  // 文件类型排行榜
  let fileTypeConditions = {};
  if (fileType) {
    fileTypeConditions.file_type = {
      [DashboardFileDetails.sequelize.Sequelize.Op.like]: `%${fileType}%`
    };
  }

  const fileTypes = await DashboardFileDetails.findAll({
    include: [{
      model: DashboardReports,
      as: 'report',
      where: whereConditions,
      attributes: []
    }],
    where: fileTypeConditions,
    attributes: [
      'file_type',
      [DashboardFileDetails.sequelize.fn('AVG', DashboardFileDetails.sequelize.col('ai_code_analysis_file_details.ai_code_ratio')), 'aiRatio'],
      [DashboardFileDetails.sequelize.fn('COUNT', DashboardFileDetails.sequelize.col('ai_code_analysis_file_details.id')), 'fileCount'],
      [DashboardFileDetails.sequelize.fn('SUM', DashboardFileDetails.sequelize.col('ai_matched_lines')), 'totalAILines']
    ],
    group: ['file_type'],
    order: [[DashboardFileDetails.sequelize.fn('AVG', DashboardFileDetails.sequelize.col('ai_code_analysis_file_details.ai_code_ratio')), 'DESC']],
    limit: 10,
    raw: true
  });

  return {
    repositories: repositories.map(item => ({
      name: item.git_repository.split('/').pop().replace('.git', ''),
      fullName: item.git_repository,
      aiRatio: parseFloat(formatAIRatio(item.aiRatio).toFixed(1)),
      prCount: parseInt(item.prCount),
      totalAILines: parseInt(item.totalAILines)
    })),
    developers: developers.map(item => ({
      name: item.mis_number,
      aiLines: parseInt(item.aiLines),
      totalLines: parseInt(item.totalLines),
      aiRatio: parseFloat(formatAIRatio(item.aiLines / item.totalLines).toFixed(1)),
      prCount: parseInt(item.prCount)
    })),
    fileTypes: fileTypes.map(item => ({
      type: item.file_type,
      aiRatio: parseFloat(formatAIRatio(item.aiRatio).toFixed(1)),
      fileCount: parseInt(item.fileCount),
      totalAILines: parseInt(item.totalAILines)
    }))
  };
}

/**
 * 获取分布统计数据
 */
async function getDistributionStats(DashboardReports, DashboardFileDetails, DashboardHistoricalRecords, whereConditions) {
  console.log('获取分布统计数据...');

  // AI占比区间分布
  const aiRatioRanges = await DashboardReports.findAll({
    where: whereConditions,
    attributes: [
      [DashboardReports.sequelize.literal(`
        CASE
          WHEN ai_code_ratio < 0.2 THEN '0-20%'
          WHEN ai_code_ratio < 0.4 THEN '20-40%'
          WHEN ai_code_ratio < 0.6 THEN '40-60%'
          WHEN ai_code_ratio < 0.8 THEN '60-80%'
          ELSE '80-100%'
        END
      `), 'range'],
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.col('id')), 'count']
    ],
    group: [DashboardReports.sequelize.literal(`
      CASE
        WHEN ai_code_ratio < 0.2 THEN '0-20%'
        WHEN ai_code_ratio < 0.4 THEN '20-40%'
        WHEN ai_code_ratio < 0.6 THEN '40-60%'
        WHEN ai_code_ratio < 0.8 THEN '60-80%'
        ELSE '80-100%'
      END
    `)],
    raw: true
  });

  // 检测方法分布
  const detectionMethods = await DashboardReports.findAll({
    where: whereConditions,
    attributes: [
      'detection_method',
      [DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.col('id')), 'count'],
      [DashboardReports.sequelize.fn('AVG', DashboardReports.sequelize.col('ai_code_ratio')), 'avgAccuracy']
    ],
    group: ['detection_method'],
    order: [[DashboardReports.sequelize.fn('COUNT', DashboardReports.sequelize.col('id')), 'DESC']],
    raw: true
  });

  // AI来源分布（从历史记录表获取）
  let aiSources = [];
  try {
    aiSources = await DashboardHistoricalRecords.findAll({
      attributes: [
        'ai_source',
        [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.col('id')), 'count'],
        [DashboardHistoricalRecords.sequelize.fn('AVG', DashboardHistoricalRecords.sequelize.col('confidence_level')), 'avgConfidence']
      ],
      where: {
        ai_source: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null }
      },
      group: ['ai_source'],
      order: [[DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.col('id')), 'DESC']],
      limit: 10,
      raw: true
    });
  } catch (error) {
    console.warn('获取AI来源分布失败:', error.message);
  }

  return {
    aiRatioRanges: aiRatioRanges.map(item => ({
      range: item.range,
      count: parseInt(item.count)
    })),
    detectionMethods: detectionMethods.map(item => ({
      method: item.detection_method,
      count: parseInt(item.count),
      accuracy: parseFloat(formatAIRatio(item.avgAccuracy).toFixed(1))
    })),
    aiSources: aiSources.map(item => ({
      source: item.ai_source,
      count: parseInt(item.count),
      avgConfidence: parseFloat(formatAIRatio(item.avgConfidence).toFixed(1))
    }))
  };
}

/**
 * 获取最近活动数据
 */
async function getRecentActivities(DashboardReports, whereConditions) {
  console.log('获取最近活动数据...');

  const recentReports = await DashboardReports.findAll({
    where: whereConditions,
    order: [['analysis_timestamp', 'DESC']],
    limit: 10,
    raw: true
  });

  return recentReports.map(report => ({
    timestamp: report.analysis_timestamp,
    type: 'analysis_completed',
    prId: report.pr_global_id,
    prTitle: report.pr_title,
    aiRatio: parseFloat(formatAIRatio(report.ai_code_ratio).toFixed(1)),
    developer: report.mis_number,
    repository: report.git_repository.split('/').pop().replace('.git', '')
  }));
}

/**
 * 获取上期时间过滤条件
 */
function getPreviousTimeFilter(currentTimeFilter) {
  const current = currentTimeFilter.analysis_timestamp[dashboardModelsModule.mainSequelize.Sequelize.Op.between];
  const start = new Date(current[0]);
  const end = new Date(current[1]);

  const duration = end.getTime() - start.getTime();
  const previousStart = new Date(start.getTime() - duration);
  const previousEnd = new Date(start.getTime());

  return {
    analysis_timestamp: {
      [dashboardModelsModule.mainSequelize.Sequelize.Op.between]: [previousStart, previousEnd]
    }
  };
}

/**
 * 计算增长率
 */
function calculateGrowthRate(current, previous) {
  if (!previous || previous === 0) {
    return current > 0 ? 100 : 0;
  }
  return ((current - previous) / previous) * 100;
}