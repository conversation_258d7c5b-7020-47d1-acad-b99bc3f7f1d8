/**
 * 生产环境数据库初始化脚本
 * 用于在生产环境中创建数据表
 */

const {
  ZebraSequelizeFactory,
  DataTypes
} = require('@bfe/zebra-proxy-sequelize');

// 数据库配置
const appKey = 'com.sankuai.dzufebiz.manage';
const refKey = 'dzu_sh_test_12_dzufebiz_test';

async function initProductionDatabase() {
  console.log('开始初始化生产环境数据库...');

  try {
    // 创建 zebra-proxy 连接
    const options = {
      appName: appKey,
      refKey: refKey,
      dbType: "zebra-proxy",
      database: 'dzufebiz',
      pool: {
        max: 20,
        min: 1,
      },
      dialectOptions: {
        dateStrings: true,
      },
      logging: false
    };

    const sequelize = ZebraSequelizeFactory.create(options);
    console.log('Zebra-proxy 连接创建成功');

    // 定义主表模型
    const AiCodeAnalysisReports = sequelize.define(
      "ai_code_analysis_reports",
      {
        id: {
          type: DataTypes.BIGINT,
          primaryKey: true,
          autoIncrement: true,
          comment: '主键ID'
        },
        pr_global_id: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true,
          comment: 'PR全局ID'
        },
        pr_title: {
          type: DataTypes.STRING(500),
          allowNull: false,
          comment: 'PR标题'
        },
        git_repository: {
          type: DataTypes.STRING(500),
          allowNull: false,
          comment: 'Git仓库地址'
        },
        git_branch: {
          type: DataTypes.STRING(200),
          allowNull: false,
          comment: 'Git分支名'
        },
        mis_number: {
          type: DataTypes.STRING(50),
          allowNull: true,
          comment: 'MIS号/作者'
        },
        detection_method: {
          type: DataTypes.STRING(50),
          allowNull: false,
          comment: '检测方法'
        },
        analysis_timestamp: {
          type: DataTypes.DATE,
          allowNull: false,
          comment: '分析时间戳'
        },
        total_files: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '总文件数'
        },
        total_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '总行数'
        },
        changed_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '变更行数'
        },
        ai_generated_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'AI生成行数'
        },
        ai_code_ratio: {
          type: DataTypes.DECIMAL(10, 8),
          allowNull: false,
          defaultValue: 0.00000000,
          comment: 'AI代码占比'
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
          comment: '创建时间'
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
          comment: '更新时间'
        }
      },
      {
        freezeTableName: true,
        timestamps: false
      }
    );

    // 定义详情表模型
    const AiCodeAnalysisFileDetails = sequelize.define(
      "ai_code_analysis_file_details",
      {
        id: {
          type: DataTypes.BIGINT,
          primaryKey: true,
          autoIncrement: true,
          comment: '主键ID'
        },
        report_id: {
          type: DataTypes.BIGINT,
          allowNull: false,
          comment: '报告ID，关联ai_code_analysis_reports.id'
        },
        pr_global_id: {
          type: DataTypes.STRING(50),
          allowNull: false,
          comment: 'PR全局ID，冗余字段便于查询'
        },
        file_name: {
          type: DataTypes.STRING(1000),
          allowNull: false,
          comment: '文件名/路径'
        },
        file_type: {
          type: DataTypes.STRING(20),
          allowNull: false,
          comment: '文件类型'
        },
        total_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '文件总行数'
        },
        changed_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '文件变更行数'
        },
        ai_matched_lines: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'AI匹配行数'
        },
        ai_code_ratio: {
          type: DataTypes.DECIMAL(10, 8),
          allowNull: false,
          defaultValue: 0.00000000,
          comment: '文件AI代码占比'
        },
        detection_method: {
          type: DataTypes.STRING(50),
          allowNull: false,
          comment: '文件检测方法'
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
          comment: '创建时间'
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW,
          comment: '更新时间'
        }
      },
      {
        freezeTableName: true,
        timestamps: false
      }
    );

    // 定义关联关系
    AiCodeAnalysisReports.hasMany(AiCodeAnalysisFileDetails, {
      foreignKey: 'report_id',
      as: 'fileDetails'
    });

    AiCodeAnalysisFileDetails.belongsTo(AiCodeAnalysisReports, {
      foreignKey: 'report_id',
      as: 'report'
    });

    // 创建表
    console.log('开始创建数据表...');
    await sequelize.sync({ force: false });
    console.log('✅ 数据表创建成功！');

    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接测试成功！');

    // 关闭连接
    await sequelize.close();
    console.log('数据库连接已关闭');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    console.error('详细错误:', error.message);
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initProductionDatabase();
}

module.exports = { initProductionDatabase };
