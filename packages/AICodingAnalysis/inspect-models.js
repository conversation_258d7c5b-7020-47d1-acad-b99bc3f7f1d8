/**
 * 检查模型对象结构
 */

// 模拟生产环境
process.env.USER = 'root';

const models = require('./models/AICodingAnalysisModels-simple.js');

async function inspectModels() {
  console.log('🔍 检查模型对象结构...\n');

  try {
    console.log('1. 初始化前的模型:');
    console.log('AiCodeAnalysisReports 类型:', typeof models.AiCodeAnalysisReports);
    console.log('AiCodeAnalysisReports 方法:', Object.getOwnPropertyNames(models.AiCodeAnalysisReports).slice(0, 10));
    console.log('sequelize 类型:', typeof models.AiCodeAnalysisReports.sequelize);

    // 初始化数据库
    console.log('\n2. 初始化数据库...');
    await models.initializeDatabase();
    console.log('数据库状态:', models.getDatabaseStatus());

    console.log('\n3. 初始化后的模型:');
    console.log('AiCodeAnalysisReports 类型:', typeof models.AiCodeAnalysisReports);
    console.log('AiCodeAnalysisReports 方法:', Object.getOwnPropertyNames(models.AiCodeAnalysisReports).slice(0, 10));
    console.log('sequelize 类型:', typeof models.AiCodeAnalysisReports.sequelize);
    
    if (models.AiCodeAnalysisReports.sequelize) {
      console.log('sequelize 方法:', Object.getOwnPropertyNames(models.AiCodeAnalysisReports.sequelize).slice(0, 10));
      console.log('sequelize.query 类型:', typeof models.AiCodeAnalysisReports.sequelize.query);
    }

    // 检查 sequelize 实例
    console.log('\n4. 检查 sequelize 实例:');
    console.log('models.sequelize 类型:', typeof models.sequelize);
    if (models.sequelize) {
      console.log('models.sequelize.query 类型:', typeof models.sequelize.query);
    }

    // 尝试直接查询
    console.log('\n5. 尝试直接查询...');
    if (models.sequelize && typeof models.sequelize.query === 'function') {
      const [results] = await models.sequelize.query('SELECT COUNT(*) as count FROM ai_code_analysis_reports');
      console.log('直接查询结果:', results[0]);
    } else {
      console.log('无法执行直接查询');
    }

    // 尝试模型查询
    console.log('\n6. 尝试模型查询...');
    if (typeof models.AiCodeAnalysisReports.findAll === 'function') {
      const results = await models.AiCodeAnalysisReports.findAll({ limit: 1 });
      console.log('模型查询结果数量:', results.length);
    } else {
      console.log('findAll 不是函数');
    }

    return { success: true };

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      stack: error.stack.split('\n').slice(0, 5)
    });
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  inspectModels().then((result) => {
    if (result.success) {
      console.log('\n✅ 检查完成！');
    } else {
      console.log('\n❌ 检查失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('检查出错:', error);
    process.exit(1);
  });
}

module.exports = { inspectModels };
