/**
 * AI代码分析相关数据模型 - 清理版本（仅支持真实数据库）
 */

const { Sequelize, DataTypes } = require('sequelize');

// 数据库配置 - 直连 MySQL
const dbConfig = {
  host: '*************',
  port: 5002,
  username: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'swift',
  dialect: 'mysql',
  pool: {
    max: 20,
    min: 1,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    dateStrings: true,
  },
  logging: false,
  timezone: '+08:00'
};

let sequelize;
let isInitialized = false;

console.log('环境检测:', {
  NODE_ENV: process.env.NODE_ENV,
  USER: process.env.USER,
  FUNCTION_NAME: process.env.FUNCTION_NAME
});

// 全局模型对象
global.AiCodeAnalysisReports = null;
global.AiCodeAnalysisFileDetails = null;

// 检查数据库状态
function getDatabaseStatus() {
  if (!sequelize) {
    return 'NOT_INITIALIZED';
  }
  return 'REAL_DATABASE';
}

// 获取当前模型
function getModels() {
  if (global.AiCodeAnalysisReports && global.AiCodeAnalysisFileDetails) {
    return {
      AiCodeAnalysisReports: global.AiCodeAnalysisReports,
      AiCodeAnalysisFileDetails: global.AiCodeAnalysisFileDetails
    };
  } else {
    throw new Error('数据库模型未初始化，请先调用 initializeDatabase()');
  }
}

// 初始化数据库连接
async function initializeDatabase() {
  if (isInitialized) {
    return sequelize;
  }

  console.log('初始化 MySQL 数据库连接...');
  try {
    console.log('MySQL 配置:', {
      host: dbConfig.host,
      port: dbConfig.port,
      username: dbConfig.username,
      database: dbConfig.database,
      dialect: dbConfig.dialect
    });

    sequelize = new Sequelize(dbConfig);
    console.log('MySQL 连接创建成功');

    // 测试连接
    console.log('测试数据库连接...');
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 定义模型
    console.log('定义数据库模型...');
    defineModels();
    
    console.log('同步数据库表...');
    await sequelize.sync({ force: false });
    console.log('数据库表同步成功');
    
    console.log('✅ 数据库模型初始化完成');

  } catch (error) {
    console.error('❌ MySQL 初始化失败:', error.message);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      errno: error.errno
    });
    throw error;
  }
  
  isInitialized = true;
  return sequelize;
}

// 定义数据库模型
function defineModels() {
  // 主表模型
  global.AiCodeAnalysisReports = sequelize.define(
    "ai_code_analysis_reports",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: 'PR全局ID'
      },
      pr_title: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'PR标题'
      },
      git_repository: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'Git仓库地址'
      },
      git_branch: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: 'Git分支名'
      },
      mis_number: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: 'MIS号/作者'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '检测方法'
      },
      analysis_timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '分析时间戳'
      },
      total_files: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总文件数'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '变更行数'
      },
      ai_generated_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI生成行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: 'AI代码占比'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 详情表模型
  global.AiCodeAnalysisFileDetails = sequelize.define(
    "ai_code_analysis_file_details",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '报告ID，关联ai_code_analysis_reports.id'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: 'PR全局ID，冗余字段便于查询'
      },
      file_name: {
        type: DataTypes.STRING(1000),
        allowNull: false,
        comment: '文件名/路径'
      },
      file_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: '文件类型'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件变更行数'
      },
      ai_matched_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI匹配行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: '文件AI代码占比'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '文件检测方法'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 定义关联关系
  global.AiCodeAnalysisReports.hasMany(global.AiCodeAnalysisFileDetails, {
    foreignKey: 'report_id',
    as: 'fileDetails'
  });

  global.AiCodeAnalysisFileDetails.belongsTo(global.AiCodeAnalysisReports, {
    foreignKey: 'report_id',
    as: 'report'
  });
  
  console.log('✅ 数据库模型定义完成');
}

// 导出模型
module.exports = {
  get sequelize() { return sequelize; },
  get AiCodeAnalysisReports() { return getModels().AiCodeAnalysisReports; },
  get AiCodeAnalysisFileDetails() { return getModels().AiCodeAnalysisFileDetails; },
  initializeDatabase,
  getDatabaseStatus
};
