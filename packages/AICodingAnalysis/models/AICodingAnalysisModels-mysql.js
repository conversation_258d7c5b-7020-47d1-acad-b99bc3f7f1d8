/**
 * AI代码分析相关数据模型 - 使用直连 MySQL
 */

const { Sequelize, DataTypes } = require('sequelize');

// 数据库配置 - 使用直连 MySQL
const dbConfig = {
  host: '*************',
  port: 5002,
  username: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'swift',
  dialect: 'mysql',
  pool: {
    max: 20,
    min: 1,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    dateStrings: true,
  },
  logging: false,
  timezone: '+08:00'
};

// 检查是否在本地开发环境
const isLocal = process.env.NODE_ENV === 'development' ||
               (process.env.USER && process.env.USER !== 'root' && typeof window === 'undefined');

let sequelize;
let isInitialized = false;

console.log('环境检测:', {
  NODE_ENV: process.env.NODE_ENV,
  USER: process.env.USER,
  isLocal: isLocal
});

// 创建模拟数据库对象
function createMockSequelize() {
  return {
    define: () => ({
      findAndCountAll: () => Promise.resolve({ count: 0, rows: [] }),
      findOne: () => Promise.resolve(null),
      create: () => Promise.resolve({ id: 1 }),
      findByPk: () => Promise.resolve({ id: 1, fileDetails: [] }),
      bulkCreate: () => Promise.resolve([]),
      sequelize: {
        transaction: () => Promise.resolve({
          commit: () => Promise.resolve(),
          rollback: () => Promise.resolve()
        }),
        Sequelize: { Op: { between: 'between', gte: 'gte', lte: 'lte', like: 'like' } }
      }
    }),
    sync: () => Promise.resolve(),
    transaction: () => Promise.resolve({
      commit: () => Promise.resolve(),
      rollback: () => Promise.resolve()
    }),
    Sequelize: { Op: { between: 'between', gte: 'gte', lte: 'lte', like: 'like' } },
    options: { dialect: 'sqlite', storage: ':memory:' }
  };
}

// 检查数据库状态
function getDatabaseStatus() {
  if (!sequelize) {
    return 'NOT_INITIALIZED';
  }
  
  // 检查是否是模拟数据库
  if (sequelize.options && sequelize.options.dialect === 'sqlite' && sequelize.options.storage === ':memory:') {
    return 'MOCK_DATABASE';
  }
  
  return 'REAL_DATABASE';
}

// 延迟初始化数据库连接
async function initializeDatabase() {
  if (isInitialized) {
    return sequelize;
  }

  if (isLocal) {
    console.log('本地开发环境：创建模拟数据库');
    sequelize = createMockSequelize();
  } else {
    // 生产环境使用直连 MySQL
    console.log('生产环境：使用直连 MySQL');
    try {
      console.log('MySQL 配置:', {
        host: dbConfig.host,
        port: dbConfig.port,
        username: dbConfig.username,
        database: dbConfig.database,
        dialect: dbConfig.dialect
      });

      sequelize = new Sequelize(dbConfig);
      console.log('MySQL 连接创建成功');

      // 测试连接
      try {
        console.log('尝试连接数据库...');
        await sequelize.authenticate();
        console.log('数据库连接成功');
        
        console.log('尝试同步数据库表...');
        await sequelize.sync({ force: false });
        console.log('数据库表同步成功');
      } catch (syncError) {
        console.error('数据库操作失败:', syncError);
        console.error('错误详情:', {
          message: syncError.message,
          code: syncError.code,
          errno: syncError.errno
        });
        console.warn('数据库连接失败，回退到模拟数据库模式');
        
        // 数据库连接失败，回退到模拟数据库
        sequelize = createMockSequelize();
      }

    } catch (error) {
      console.error('MySQL 初始化失败，使用模拟数据库:', error.message);
      sequelize = createMockSequelize();
    }
  }
  
  // 重新定义模型
  redefineModels();
  
  isInitialized = true;
  return sequelize;
}

// 重新定义模型
function redefineModels() {
  // 重新定义主表模型
  module.exports.AiCodeAnalysisReports = sequelize.define(
    "ai_code_analysis_reports",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: 'PR全局ID'
      },
      pr_title: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'PR标题'
      },
      git_repository: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'Git仓库地址'
      },
      git_branch: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: 'Git分支名'
      },
      mis_number: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: 'MIS号/作者'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '检测方法'
      },
      analysis_timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '分析时间戳'
      },
      total_files: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总文件数'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '变更行数'
      },
      ai_generated_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI生成行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: 'AI代码占比'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 重新定义详情表模型
  module.exports.AiCodeAnalysisFileDetails = sequelize.define(
    "ai_code_analysis_file_details",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '报告ID，关联ai_code_analysis_reports.id'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: 'PR全局ID，冗余字段便于查询'
      },
      file_name: {
        type: DataTypes.STRING(1000),
        allowNull: false,
        comment: '文件名/路径'
      },
      file_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: '文件类型'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件变更行数'
      },
      ai_matched_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI匹配行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: '文件AI代码占比'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '文件检测方法'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 定义关联关系
  if (typeof module.exports.AiCodeAnalysisReports.hasMany === 'function') {
    module.exports.AiCodeAnalysisReports.hasMany(module.exports.AiCodeAnalysisFileDetails, {
      foreignKey: 'report_id',
      as: 'fileDetails'
    });
  }

  if (typeof module.exports.AiCodeAnalysisFileDetails.belongsTo === 'function') {
    module.exports.AiCodeAnalysisFileDetails.belongsTo(module.exports.AiCodeAnalysisReports, {
      foreignKey: 'report_id',
      as: 'report'
    });
  }
}

// 初始化为模拟数据库，避免启动时错误
sequelize = createMockSequelize();

// 初始模型定义（使用模拟数据库）
const AiCodeAnalysisReports = {
  findAndCountAll: () => Promise.resolve({ count: 0, rows: [] }),
  findOne: () => Promise.resolve(null),
  create: () => Promise.resolve({ id: 1 }),
  findByPk: () => Promise.resolve({ id: 1, fileDetails: [] }),
  bulkCreate: () => Promise.resolve([]),
  sequelize: sequelize
};

const AiCodeAnalysisFileDetails = {
  findAndCountAll: () => Promise.resolve({ count: 0, rows: [] }),
  findOne: () => Promise.resolve(null),
  create: () => Promise.resolve({ id: 1 }),
  bulkCreate: () => Promise.resolve([]),
  sequelize: sequelize
};

module.exports = {
  sequelize,
  AiCodeAnalysisReports,
  AiCodeAnalysisFileDetails,
  initializeDatabase,
  getDatabaseStatus
};
