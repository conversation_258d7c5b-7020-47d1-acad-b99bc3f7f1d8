/**
 * AI代码分析大盘专用数据模型 - 支持多数据库连接
 */

const { Sequelize, DataTypes } = require('sequelize');

// 主数据库配置 (swift) - AI分析报告
const mainDbConfig = {
  host: '*************',
  port: 5002,
  username: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'swift',
  dialect: 'mysql',
  pool: {
    max: 20,
    min: 1,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    dateStrings: true,
  },
  logging: false,
  timezone: '+08:00'
};

// 增长数据库配置 (GrowthDistributionCore) - AI代码历史记录
const growthDbConfig = {
  host: '**************',
  port: 5002,
  username: 'rds_yaoyan03',
  password: 'JI%XbcBm%OaMX#',
  database: 'GrowthDistributionCore',
  dialect: 'mysql',
  pool: {
    max: 20,
    min: 1,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    dateStrings: true,
  },
  logging: false,
  timezone: '+08:00'
};

let mainSequelize;
let growthSequelize;
let isInitialized = false;

// 全局模型对象
global.DashboardReports = null;
global.DashboardFileDetails = null;
global.DashboardHistoricalRecords = null;

console.log('大盘数据库环境检测:', {
  NODE_ENV: process.env.NODE_ENV,
  USER: process.env.USER,
  FUNCTION_NAME: process.env.FUNCTION_NAME
});

// 检查数据库状态
function getDashboardDatabaseStatus() {
  if (!mainSequelize || !growthSequelize) {
    return 'NOT_INITIALIZED';
  }
  return 'REAL_DATABASE';
}

// 获取当前模型
function getDashboardModels() {
  if (global.DashboardReports && global.DashboardFileDetails && global.DashboardHistoricalRecords) {
    return {
      DashboardReports: global.DashboardReports,
      DashboardFileDetails: global.DashboardFileDetails,
      DashboardHistoricalRecords: global.DashboardHistoricalRecords
    };
  } else {
    throw new Error('大盘数据库模型未初始化，请先调用 initializeDashboardDatabase()');
  }
}

// 初始化大盘数据库连接
async function initializeDashboardDatabase() {
  if (isInitialized) {
    return { mainSequelize, growthSequelize };
  }

  console.log('初始化大盘数据库连接...');

  try {
    // 初始化主数据库连接
    console.log('连接主数据库 (swift)...');
    console.log('主数据库配置:', {
      host: mainDbConfig.host,
      port: mainDbConfig.port,
      username: mainDbConfig.username,
      database: mainDbConfig.database
    });

    mainSequelize = new Sequelize(mainDbConfig);
    await mainSequelize.authenticate();
    console.log('主数据库连接成功');

    // 初始化增长数据库连接
    console.log('连接增长数据库 (GrowthDistributionCore)...');
    console.log('增长数据库配置:', {
      host: growthDbConfig.host,
      port: growthDbConfig.port,
      username: growthDbConfig.username,
      database: growthDbConfig.database
    });

    growthSequelize = new Sequelize(growthDbConfig);
    await growthSequelize.authenticate();
    console.log('增长数据库连接成功');

    // 定义模型
    console.log('定义大盘数据库模型...');
    defineDashboardModels();

    console.log('同步数据库表...');
    await mainSequelize.sync({ force: false });
    await growthSequelize.sync({ force: false });
    console.log('数据库表同步成功');

    console.log('✅ 大盘数据库模型初始化完成');

  } catch (error) {
    console.error('❌ 大盘数据库初始化失败:', error.message);
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      errno: error.errno
    });
    throw error;
  }

  isInitialized = true;
  return { mainSequelize, growthSequelize };
}

// 定义大盘数据库模型
function defineDashboardModels() {
  // 主表模型 (来自 swift 数据库)
  global.DashboardReports = mainSequelize.define(
    "ai_code_analysis_reports",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: 'PR全局ID'
      },
      pr_title: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'PR标题'
      },
      git_repository: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: 'Git仓库地址'
      },
      git_branch: {
        type: DataTypes.STRING(200),
        allowNull: false,
        comment: 'Git分支名'
      },
      mis_number: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: 'MIS号/作者'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '检测方法'
      },
      analysis_timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '分析时间戳'
      },
      total_files: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总文件数'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '变更行数'
      },
      ai_generated_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI生成行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: 'AI代码占比'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 详情表模型 (来自 swift 数据库)
  global.DashboardFileDetails = mainSequelize.define(
    "ai_code_analysis_file_details",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID'
      },
      report_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '报告ID，关联ai_code_analysis_reports.id'
      },
      pr_global_id: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: 'PR全局ID，冗余字段便于查询'
      },
      file_name: {
        type: DataTypes.STRING(1000),
        allowNull: false,
        comment: '文件名/路径'
      },
      file_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        comment: '文件类型'
      },
      total_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件总行数'
      },
      changed_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '文件变更行数'
      },
      ai_matched_lines: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'AI匹配行数'
      },
      ai_code_ratio: {
        type: DataTypes.DECIMAL(10, 8),
        allowNull: false,
        defaultValue: 0.00000000,
        comment: '文件AI代码占比'
      },
      detection_method: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '文件检测方法'
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 历史记录模型 (来自 GrowthDistributionCore 数据库)
  global.DashboardHistoricalRecords = growthSequelize.define(
    "ai_code_gen_records",
    {
      id: {
        type: DataTypes.BIGINT.UNSIGNED,
        primaryKey: true,
        autoIncrement: true
      },
      repo_id: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      file_path: {
        type: DataTypes.STRING(500),
        allowNull: false
      },
      commit_hash: {
        type: DataTypes.CHAR(40),
        allowNull: false
      },
      code_snippet: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      snippet_hash: {
        type: DataTypes.CHAR(64),
        allowNull: false
      },
      start_line: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false
      },
      end_line: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false
      },
      ai_source: {
        type: DataTypes.STRING(50),
        allowNull: true
      },
      git_username: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: 'Git用户名'
      },
      git_repository: {
        type: DataTypes.STRING(500),
        allowNull: true,
        comment: 'Git仓库地址'
      },
      generation_timestamp: {
        type: DataTypes.DATE,
        allowNull: true
      },
      confidence_level: {
        type: DataTypes.DECIMAL(4, 3),
        allowNull: false,
        defaultValue: 1.000
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true
      }
    },
    {
      freezeTableName: true,
      timestamps: false
    }
  );

  // 定义关联关系
  global.DashboardReports.hasMany(global.DashboardFileDetails, {
    foreignKey: 'report_id',
    as: 'fileDetails'
  });

  global.DashboardFileDetails.belongsTo(global.DashboardReports, {
    foreignKey: 'report_id',
    as: 'report'
  });

  console.log('✅ 大盘数据库模型定义完成');
}

// 导出模型
module.exports = {
  get mainSequelize() { return mainSequelize; },
  get growthSequelize() { return growthSequelize; },
  get DashboardReports() { return getDashboardModels().DashboardReports; },
  get DashboardFileDetails() { return getDashboardModels().DashboardFileDetails; },
  get DashboardHistoricalRecords() { return getDashboardModels().DashboardHistoricalRecords; },
  initializeDashboardDatabase,
  getDashboardDatabaseStatus
};
