{"component": [{"name": "s3", "type": "s3", "enable": false, "doc": "使用文档：https://km.sankuai.com/collabpage/1570702633", "parameter": {"accessKeyId": "", "accessKeySecret": "", "endpoint": "", "bucket": "", "secure": false}}, {"name": "rds", "type": "rds-orm", "doc": "使用文档：https://km.sankuai.com/collabpage/1552157661", "enable": true, "useZebraProxy": true, "parameter": {"dialect": "mysql", "appName": "com.sankuai.dzufebiz.manage", "jdbcRef": "dzu_sh_test_12_dzufebiz_test", "refKey": "dzu_sh_test_12_dzufebiz_test", "dbType": "zebra-proxy", "routeType": "master-only", "pool": {"min": 1, "max": 20, "idle": 30000}, "define": {"freezeTableName": true, "timestamps": false}, "dialectOptions": {"dateStrings": true}}}, {"name": "rds1", "type": "rds-native", "doc": "使用文档：https://km.sankuai.com/collabpage/1552157661", "enable": false, "useZebraProxy": true, "parameter": {"appName": "com.sankuai.air.data.service", "jdbcRef": "air_inf_bom_db_test_test", "refKey": "air_inf_bom_db_test_test", "dbType": "zebra-proxy", "routeType": "master-only"}}, {"name": "squirrel", "type": "squirrel", "doc": "使用文档：https://km.sankuai.com/collabpage/1541710188", "enable": false, "parameter": {"database": "redis-mws_qa", "methodTimeOut": 1000, "routerType": "master-slave", "appName": "com.sankuai.air.data.service", "connTimeout": 1000, "dsMonitorable": false, "configFilePath": "./nodesdk_samples/squirrel/squirrel-proxy.conf"}}, {"name": "mafka", "type": "mafka", "doc": "使用文档：https://km.sankuai.com/collabpage/1570781867", "enable": false, "parameter": {"consumer": {"namespace": "", "appkey": "", "topic": "", "group": "", "highLevelProducer": {}, "kafkaClient": {}}, "producer": {"namespace": "", "appkey": "", "topic": ""}}}, {"name": "kms", "type": "kms", "doc": "使用文档：https://km.sankuai.com/collabpage/1541643538", "enable": false, "parameter": {}}, {"name": "lion", "type": "lion", "doc": "使用文档：https://km.sankuai.com/collabpage/1570732435", "enable": false, "parameter": {"userName": "", "passWord": ""}}, {"name": "sso", "type": "sso", "doc": "使用文档：https://km.sankuai.com/collabpage/1541539640", "isMiddleWare": true, "excludes": [], "context": ["userInfo", "accessToken", "_ssoClient"], "enable": false, "parameter": {"clientId": "", "secret": ""}}, {"name": "thrift", "type": "thrift", "doc": "使用文档：https://km.sankuai.com/collabpage/1570602939", "enable": false, "parameter": {"localAppKey": "com.sankuai.nest.example.nodejs", "remoteAppKey": "com.sankuai.cloudx.web", "env": "test", "enableAuth": true, "serviceName": "", "unified": true, "thriftFileOutPath": "./thrift/target", "thriftFilePath": "./thrift/source"}}]}