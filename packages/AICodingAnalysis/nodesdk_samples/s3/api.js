const path = require('path')
const infBomTest = async (event, context) => {
  // 更多接口调用方法，请参考文档https://km.sankuai.com/collabpage/1570702633
  // context/event包含字段详解 请参考文档https://km.sankuai.com/collabpage/1567884543
  const key = 'helloword.txt'
  const buf = Buffer.from('Hello world', 'utf-8')
  await context.sdk.s3.putObject(key, buf)
  const value = await context.sdk.s3.getObject('helloword.txt', path.join('./nodesdk_samples/s3/' + key))
  return {
    s3:value
  }
}
module.exports = infBomTest