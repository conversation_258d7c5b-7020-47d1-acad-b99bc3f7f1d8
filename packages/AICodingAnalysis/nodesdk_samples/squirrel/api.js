const infBomTest = async (event, context) => {
  // 更多接口调用方法，请参考文档https://km.sankuai.com/collabpage/1541710188
  // context/event包含字段详解 请参考文档https://km.sankuai.com/collabpage/1567884543
  await context.sdk.squirrel.setCache('inf_bom__test_squirrel', 'html_test_sdk', 'hello squirrel')
  const value = await context.sdk.squirrel.getCache('inf_bom__test_squirrel', 'html_test_sdk')
  return {
    squirrel:value
  }
}
module.exports = infBomTest