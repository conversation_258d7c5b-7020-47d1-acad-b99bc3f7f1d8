const Sample = require('./Sample')
const infBomTest = async (event, context) => {
  // 更多接口调用方法，请参考文档https://km.sankuai.com/collabpage/1570602939
  // context/event包含字段详解 请参考文档https://km.sankuai.com/collabpage/1567884543
  const value = await context.sdk.thrift.exec(Sample, {
    methodName: 'getOrderById',
    timeout: 10000 // 本次调用超时为 10 秒
  }, 1, 'zhangjing129')
  return {
    thrift:value
  }
}
module.exports = infBomTest