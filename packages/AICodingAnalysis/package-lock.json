{"name": "nest-example", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "nest-example", "version": "1.0.0", "license": "ISC", "dependencies": {"@bfe/zebra-proxy-sequelize": "^1.0.0", "@fdfe/era-cloud-uploader": "^0.11.2", "@nibfe/talos-public-api": "^1.2.15", "axios": "^1.9.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"eslint": "^5.11.1", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "mocha": "^5.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@bfe/zebra-node-proxy": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/@bfe/zebra-node-proxy/download/@bfe/zebra-node-proxy-1.0.5.tgz", "integrity": "sha1-QjzuouemoDgnPwnvyXScIPCPiGI=", "license": "MIT", "dependencies": {"@dp/cat-client": "^3.0.4", "@dp/lion-client": "^3.2.1", "@dp/node-kms": "^2.0.9", "@mtfe/cat": "^1.1.0", "bluebird": "^3.7.2", "dayjs": "^1.11.10", "ip": "^1.1.8", "lodash": "^4.17.21", "log4js": "^6.9.1", "mysql2": "^2.3.3", "request": "^2.88.2"}}, "node_modules/@bfe/zebra-node-proxy/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@bfe/zebra-node-proxy/node_modules/lru-cache": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@bfe/zebra-node-proxy/node_modules/mysql2": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-2.3.3.tgz", "integrity": "sha1-lE897KSxZikFL/hhT7+J1VUlRaA=", "license": "MIT", "dependencies": {"denque": "^2.0.1", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^4.0.0", "lru-cache": "^6.0.0", "named-placeholders": "^1.1.2", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "engines": {"node": ">= 8.0"}}, "node_modules/@bfe/zebra-proxy-sequelize": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/@bfe/zebra-proxy-sequelize/download/@bfe/zebra-proxy-sequelize-1.0.6.tgz", "integrity": "sha1-yweWrHLP4j79x3qjjPFlgK3RyzY=", "license": "MIT", "dependencies": {"@bfe/zebra-node-proxy": "1.0.5", "@dp/cat-client": "^3.0.4", "@dp/node-kms": "^2.0.9", "@mtfe/cat": "^1.1.0", "ip": "^1.1.8", "lodash": "^4.17.21", "log4js": "^6.9.1", "mysql2": "^1.6.5", "sequelize": "^5.9.4", "sequelize-pool": "^2.3.0", "uuid": "^9.0.1"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/denque": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/denque/download/denque-1.5.1.tgz", "integrity": "sha1-B/Zw4pyaePj67LJWah4sEZKcXL8=", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/iconv-lite": {"version": "0.5.2", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.5.2.tgz", "integrity": "sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/inflection": {"version": "1.12.0", "resolved": "http://r.npm.sankuai.com/inflection/download/inflection-1.12.0.tgz", "integrity": "sha1-ogCTVlbW9fa8TcdQLhrstwMihBY=", "engines": ["node >= 0.4.0"], "license": "MIT"}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/mysql2": {"version": "1.7.0", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-1.7.0.tgz", "integrity": "sha1-L78xTaAWph0Dj/zVeioKo7e46sw=", "license": "MIT", "dependencies": {"denque": "^1.4.1", "generate-function": "^2.3.1", "iconv-lite": "^0.5.0", "long": "^4.0.0", "lru-cache": "^5.1.1", "named-placeholders": "^1.1.2", "seq-queue": "^0.0.5", "sqlstring": "^2.3.1"}, "engines": {"node": ">= 8.0"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/retry-as-promised": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/retry-as-promised/download/retry-as-promised-3.2.0.tgz", "integrity": "sha1-dp9j1Ta+xHg1SdsHd8tW2t2dhUM=", "license": "MIT", "dependencies": {"any-promise": "^1.3.0"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/sequelize": {"version": "5.22.5", "resolved": "http://r.npm.sankuai.com/sequelize/download/sequelize-5.22.5.tgz", "integrity": "sha1-/3/dNJgKLZVFakpX4WFTwg1X6W4=", "deprecated": "Please update to v6 or higher! A migration guide can be found here: https://sequelize.org/v6/manual/upgrade-to-v6.html", "license": "MIT", "dependencies": {"bluebird": "^3.5.0", "cls-bluebird": "^2.1.0", "debug": "^4.1.1", "dottie": "^2.0.0", "inflection": "1.12.0", "lodash": "^4.17.15", "moment": "^2.24.0", "moment-timezone": "^0.5.21", "retry-as-promised": "^3.2.0", "semver": "^6.3.0", "sequelize-pool": "^2.3.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.7.0", "wkx": "^0.4.8"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/sequelize/node_modules/uuid": {"version": "8.3.2", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/uuid": {"version": "9.0.1", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@bfe/zebra-proxy-sequelize/node_modules/wkx": {"version": "0.4.8", "resolved": "http://r.npm.sankuai.com/wkx/download/wkx-0.4.8.tgz", "integrity": "sha1-oJLPCI0RJoP9xxgv0xSTssWCAAM=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@colors/colors": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@colors/colors/download/@colors/colors-1.6.0.tgz", "integrity": "sha1-7GzSN0QHALwjyiMIf1E8dVCJWLA=", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/@dabh/diagnostics": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz", "integrity": "sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=", "license": "MIT", "dependencies": {"colorspace": "1.1.x", "enabled": "2.0.x", "kuler": "^2.0.0"}}, "node_modules/@dp/cat-client": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-3.0.4.tgz", "integrity": "sha1-xy3RivDxDiLsgZmgyAkgmBTE0pU=", "hasInstallScript": true, "license": "MIT", "dependencies": {"@dp/logger-container": "^1.1.0", "@dp/simple-util": "^1.0.0", "buffer-builder": "^0.2.0", "debug": "^2.2.0", "mkdirp": "^0.5.1", "moment": "^2.10.6", "node-addon-api": "^3.1.0", "request": "^2.67.0", "semver": "^6.1.2", "xml2js": "^0.4.15"}}, "node_modules/@dp/cat-client/node_modules/debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/@dp/cat-client/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/@dp/lion-client": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/@dp/lion-client/download/@dp/lion-client-3.3.1.tgz", "integrity": "sha1-XwDz7tzEQrOVn+EN6boKNWs8tWI=", "license": "MIT", "dependencies": {"@dp/logger-container": "^1.2.0", "@dp/server-env": "^1.0.3", "@dp/simple-util": "^1.1.1", "@mtfe/cat": "^1.1.0", "async": "^3.2.4", "async-lock": "^1.4.0", "imurmurhash": "^0.1.4", "ip": "^1.1.5", "moment": "^2.19.4", "request": "^2.88.2", "winston": "3.12.0"}}, "node_modules/@dp/lion-client/node_modules/async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "license": "MIT"}, "node_modules/@dp/logger-container": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz", "integrity": "sha1-AuoLwhN37/N6UHL/tRTccMB88m8=", "license": "ISC"}, "node_modules/@dp/node-kms": {"version": "2.0.13", "resolved": "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.13.tgz", "integrity": "sha1-aOGzsEXrSiDJj0aCHIBICVl+Qx0=", "license": "ISC", "dependencies": {"@dp/server-env": "^1.0.3", "@dp/simple-util": "^1.1.1", "bindings": "^1.5.0", "co-request": "^1.0.0", "ip": "^1.1.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@dp/server-env": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz", "integrity": "sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=", "license": "MIT", "dependencies": {"@dp/logger-container": "^1.1.0", "properties-parser": "^0.3.1"}}, "node_modules/@dp/simple-util": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz", "integrity": "sha1-lyx6QVEa7L2mekrEiTstPr89WEE=", "license": "ISC"}, "node_modules/@fdfe/era-cloud-uploader": {"version": "0.11.5", "resolved": "http://r.npm.sankuai.com/@fdfe/era-cloud-uploader/download/@fdfe/era-cloud-uploader-0.11.5.tgz", "integrity": "sha1-X0Gz5D5fAIX6f7n2KvPbMcFYQ70=", "license": "ISC", "dependencies": {"@zeit/ncc": "^0.20.5", "archiver": "^3.0.0", "cac": "^5.0.12", "chalk": "^2.4.1", "debug": "^4.1.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "js-yaml": "^3.12.0", "lodash": "^4.17.14", "minimatch": "^3.0.4", "node-stream-zip": "^1.7.0", "ora": "^3.0.0", "request": "^2.88.0", "rimraf": "^2.6.2", "semver": "^6.0.0", "tar": "^6.0.1"}, "bin": {"ecf-builder": "bin/build", "ecf-uploader": "bin/index", "era-cloud-builder": "bin/build", "era-cloud-uploader": "bin/index"}}, "node_modules/@mtfe/cat": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@mtfe/cat/download/@mtfe/cat-1.1.0.tgz", "integrity": "sha1-N2Y+9a+8nQng2bQAhAbWEa140Ag=", "license": "ISC", "dependencies": {"@dp/cat-client": "^3.0.3", "debug": "^4.3.3"}, "peerDependencies": {"@dp/cat-client": ">=1.0.0"}}, "node_modules/@nibfe/talos-public-api": {"version": "1.2.15", "resolved": "http://r.npm.sankuai.com/@nibfe/talos-public-api/download/@nibfe/talos-public-api-1.2.15.tgz", "integrity": "sha1-frU4CU3ryT8AhL3Ru4GgqrTw9tw=", "dependencies": {"@types/git-url-parse": "9.0.0", "axios": "0.21.1", "form-data": "2.5.0", "git-url-parse": "11.1.2"}}, "node_modules/@nibfe/talos-public-api/node_modules/axios": {"version": "0.21.1", "resolved": "http://r.npm.sankuai.com/axios/download/axios-0.21.1.tgz", "integrity": "sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=", "license": "MIT", "dependencies": {"follow-redirects": "^1.10.0"}}, "node_modules/@nibfe/talos-public-api/node_modules/form-data": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-2.5.0.tgz", "integrity": "sha1-CU7DWdxLVefWLg20rNduif6HTTc=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/@rtsao/scc": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@rtsao/scc/download/@rtsao/scc-1.1.0.tgz", "integrity": "sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=", "dev": true, "license": "MIT"}, "node_modules/@types/debug": {"version": "4.1.12", "resolved": "http://r.npm.sankuai.com/@types/debug/download/@types/debug-4.1.12.tgz", "integrity": "sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=", "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/git-url-parse": {"version": "9.0.0", "resolved": "http://r.npm.sankuai.com/@types/git-url-parse/download/@types/git-url-parse-9.0.0.tgz", "integrity": "sha1-qsExWkT6TtWlLDgg9sPC+3nL0S0=", "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "resolved": "http://r.npm.sankuai.com/@types/json5/download/@types/json5-0.0.29.tgz", "integrity": "sha1-7ihweulOEdK4J7y+UnC86n8+ce4=", "dev": true, "license": "MIT"}, "node_modules/@types/minimist": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.5.tgz", "integrity": "sha1-7BB1XocUl7zYPv6SfkPsRujAdH4=", "license": "MIT"}, "node_modules/@types/ms": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/@types/ms/download/@types/ms-2.1.0.tgz", "integrity": "sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=", "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.21", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-22.15.21.tgz", "integrity": "sha1-GW7xT+INh/fK8eezmDJ2f5qZW3c=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/triple-beam": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/@types/triple-beam/download/@types/triple-beam-1.3.5.tgz", "integrity": "sha1-dP75/7qhmOuLWIvgKfOLACmcqiw=", "license": "MIT"}, "node_modules/@types/validator": {"version": "13.15.1", "resolved": "http://r.npm.sankuai.com/@types/validator/download/@types/validator-13.15.1.tgz", "integrity": "sha1-UvNhfrW6MN0AGDgMZFaEQNC03go=", "license": "MIT"}, "node_modules/@zeit/ncc": {"version": "0.20.5", "resolved": "http://r.npm.sankuai.com/@zeit/ncc/download/@zeit/ncc-0.20.5.tgz", "integrity": "sha1-pBr25ryrSlj0YSuuYTf3C84BkuM=", "deprecated": "@zeit/ncc is no longer maintained. Please use @vercel/ncc instead.", "license": "MIT", "bin": {"ncc": "dist/ncc/cli.js"}}, "node_modules/acorn": {"version": "6.4.2", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-regex": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz", "integrity": "sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ansi-styles": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "license": "MIT"}, "node_modules/archiver": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/archiver/download/archiver-3.1.1.tgz", "integrity": "sha1-nbeBnU2vYK7BD+hrFsuSWM7WbqA=", "license": "MIT", "dependencies": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.2"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/archiver-utils/download/archiver-utils-2.1.0.tgz", "integrity": "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI=", "license": "MIT", "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils/node_modules/isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/archiver-utils/node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/archiver-utils/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/archiver-utils/node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/argparse": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz", "integrity": "sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.8", "resolved": "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz", "integrity": "sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.6", "resolved": "http://r.npm.sankuai.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.6.tgz", "integrity": "sha1-z6EGXIHctk40VXybgdAS9qQhxWQ=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz", "integrity": "sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz", "integrity": "sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz", "integrity": "sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asn1": {"version": "0.2.6", "resolved": "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz", "integrity": "sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/astral-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz", "integrity": "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/async": {"version": "2.6.4", "resolved": "http://r.npm.sankuai.com/async/download/async-2.6.4.tgz", "integrity": "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/async-function": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/async-function/download/async-function-1.0.0.tgz", "integrity": "sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/async-lock": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/async-lock/download/async-lock-1.4.1.tgz", "integrity": "sha1-VrhxiRWptosQ/OLyqaPd33Ze9T8=", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws-ssl-profiles": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/aws-ssl-profiles/download/aws-ssl-profiles-1.1.2.tgz", "integrity": "sha1-FX3Xfp8ZsdEjZ46T8SDm8ZMCJkE=", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/aws4": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz", "integrity": "sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=", "license": "MIT"}, "node_modules/axios": {"version": "1.9.0", "resolved": "http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axios/node_modules/form-data": {"version": "4.0.2", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bindings": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "license": "MIT", "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bl": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/browser-stdout": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/browser-stdout/download/browser-stdout-1.3.1.tgz", "integrity": "sha1-uqVZ7hTO1zRSIputcyZGfGH6vWA=", "dev": true, "license": "ISC"}, "node_modules/buffer": {"version": "5.7.1", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-builder": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz", "integrity": "sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=", "license": "MIT/X11"}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/cac": {"version": "5.0.16", "resolved": "http://r.npm.sankuai.com/cac/download/cac-5.0.16.tgz", "integrity": "sha1-GRRpf1ihboY/m8K9G2ERZiBFwUc=", "license": "MIT", "dependencies": {"chalk": "^2.4.1", "joycon": "^2.1.2", "minimost": "^1.2.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}, "engines": {"node": ">=6"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "license": "Apache-2.0"}, "node_modules/chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chardet": {"version": "0.7.0", "resolved": "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=", "dev": true, "license": "MIT"}, "node_modules/chownr": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/cli-cursor": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cli-spinners": {"version": "2.9.2", "resolved": "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz", "integrity": "sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-width": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz", "integrity": "sha1-sEM9C06chH7xiGik7xb9X8gnHEg=", "dev": true, "license": "ISC"}, "node_modules/clone": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4=", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/cls-bluebird": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cls-bluebird/download/cls-bluebird-2.1.0.tgz", "integrity": "sha1-N+8eCAqP+1XC9BZPU28ZGeeWiu4=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"is-bluebird": "^1.0.2", "shimmer": "^1.1.0"}}, "node_modules/co-request": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz", "integrity": "sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=", "dependencies": {"request": "*"}}, "node_modules/color": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/color/download/color-3.2.1.tgz", "integrity": "sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=", "license": "MIT", "dependencies": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}}, "node_modules/color-convert": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz", "integrity": "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/colorspace": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/colorspace/download/colorspace-1.1.4.tgz", "integrity": "sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=", "license": "MIT", "dependencies": {"color": "^3.1.3", "text-hex": "1.0.x"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.15.1", "resolved": "http://r.npm.sankuai.com/commander/download/commander-2.15.1.tgz", "integrity": "sha1-30boZ9D8Kuxmo0ZitAapzK//Ww8=", "dev": true, "license": "MIT"}, "node_modules/compress-commons": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/compress-commons/download/compress-commons-2.1.1.tgz", "integrity": "sha1-lBDZpTTPhDXj+7t8bOSN4twvBhA=", "license": "MIT", "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "engines": {"node": ">= 6"}}, "node_modules/compress-commons/node_modules/isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/compress-commons/node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/compress-commons/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/compress-commons/node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "license": "MIT"}, "node_modules/crc": {"version": "3.8.0", "resolved": "http://r.npm.sankuai.com/crc/download/crc-3.8.0.tgz", "integrity": "sha1-rWAmnCyFb4wpnixMwN5FVpFAVsY=", "license": "MIT", "dependencies": {"buffer": "^5.1.0"}}, "node_modules/crc32-stream": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/crc32-stream/download/crc32-stream-3.0.1.tgz", "integrity": "sha1-yubu7QA7DkTXOdJ53lrmOxcbToU=", "license": "MIT", "dependencies": {"crc": "^3.4.4", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 6.9.0"}}, "node_modules/cross-spawn": {"version": "6.0.6", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.6.tgz", "integrity": "sha1-MNDvoHEt2361p24ehyG/+vprXVc=", "dev": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/cross-spawn/node_modules/semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-view-buffer": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz", "integrity": "sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz", "integrity": "sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz", "integrity": "sha1-BoMH+bcat2274QKROJ4CCFZgYZE=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/date-format": {"version": "4.0.14", "resolved": "http://r.npm.sankuai.com/date-format/download/date-format-4.0.14.tgz", "integrity": "sha1-eo5YRDT7FppSHIt6pIHzVYENlAA=", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw=", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decode-uri-component": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz", "integrity": "sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/defaults": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz", "integrity": "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/denque": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/denque/download/denque-2.1.0.tgz", "integrity": "sha1-6T4aZWn7XmbxajwqKWRhfTSdarE=", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "node_modules/diff": {"version": "3.5.0", "resolved": "http://r.npm.sankuai.com/diff/download/diff-3.5.0.tgz", "integrity": "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dottie": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/dottie/download/dottie-2.0.6.tgz", "integrity": "sha1-NFZOv8bsXldyJy1GZCStW2lkhNQ=", "license": "MIT"}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/emoji-regex": {"version": "7.0.3", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true, "license": "MIT"}, "node_modules/enabled": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/enabled/download/enabled-2.0.0.tgz", "integrity": "sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I=", "license": "MIT"}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/es-abstract": {"version": "1.23.10", "resolved": "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.10.tgz", "integrity": "sha1-hHksFS/yiY7HPv4zwcEyOj39h/g=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-regex": "^1.2.1", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz", "integrity": "sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz", "integrity": "sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eslint": {"version": "5.16.0", "resolved": "http://r.npm.sankuai.com/eslint/download/eslint-5.16.0.tgz", "integrity": "sha1-oeOsGq5KP72Clvz496tzFMu2q+o=", "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "ajv": "^6.9.1", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^4.0.3", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.1", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^6.2.2", "js-yaml": "^3.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.11", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^5.2.3", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^6.14.0 || ^8.10.0 || >=9.10.0"}}, "node_modules/eslint-config-standard": {"version": "12.0.0", "resolved": "http://r.npm.sankuai.com/eslint-config-standard/download/eslint-config-standard-12.0.0.tgz", "integrity": "sha1-Y4tMZdsL1aQTGflruh8V3a0hB9k=", "dev": true, "license": "MIT", "peerDependencies": {"eslint": ">=5.0.0", "eslint-plugin-import": ">=2.13.0", "eslint-plugin-node": ">=7.0.0", "eslint-plugin-promise": ">=4.0.0", "eslint-plugin-standard": ">=4.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "resolved": "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz", "integrity": "sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils": {"version": "2.12.0", "resolved": "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.12.0.tgz", "integrity": "sha1-/kz7lI1h9JID17CIcZgrZbmvCws=", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-es": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz", "integrity": "sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=", "dev": true, "license": "MIT", "dependencies": {"eslint-utils": "^1.4.2", "regexpp": "^2.0.1"}, "engines": {"node": ">=6.5.0"}, "peerDependencies": {"eslint": ">=4.19.1"}}, "node_modules/eslint-plugin-import": {"version": "2.31.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.31.0.tgz", "integrity": "sha1-MQzn5yDKHZwLs/aa39HGvdfZ4Oc=", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.8", "array.prototype.findlastindex": "^1.2.5", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.0", "hasown": "^2.0.2", "is-core-module": "^2.15.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.0", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.8", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/doctrine": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-node": {"version": "8.0.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-node/download/eslint-plugin-node-8.0.1.tgz", "integrity": "sha1-Va41YAIoY9FB+noReZUyNApoWWQ=", "dev": true, "license": "MIT", "dependencies": {"eslint-plugin-es": "^1.3.1", "eslint-utils": "^1.3.1", "ignore": "^5.0.2", "minimatch": "^3.0.4", "resolve": "^1.8.1", "semver": "^5.5.0"}, "engines": {"node": ">=6"}, "peerDependencies": {"eslint": ">=4.19.1"}}, "node_modules/eslint-plugin-node/node_modules/ignore": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/eslint-plugin-node/node_modules/semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/eslint-plugin-promise": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz", "integrity": "sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=", "dev": true, "license": "ISC", "engines": {"node": ">=6"}}, "node_modules/eslint-plugin-standard": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz", "integrity": "sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peerDependencies": {"eslint": ">=5.0.0"}}, "node_modules/eslint-scope": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/eslint-utils": {"version": "1.4.3", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-1.4.3.tgz", "integrity": "sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6"}}, "node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/eslint/node_modules/semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/espree": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/espree/download/espree-5.0.1.tgz", "integrity": "sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esquery/node_modules/estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/extend": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=", "license": "MIT"}, "node_modules/external-editor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "dev": true, "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fecha": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz", "integrity": "sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=", "license": "MIT"}, "node_modules/figures": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=4"}}, "node_modules/file-entry-cache": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz", "integrity": "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "license": "MIT"}, "node_modules/filter-obj": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/filter-obj/download/filter-obj-1.1.0.tgz", "integrity": "sha1-mzERErxsYSehbgFsbF1/GeCAXFs=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/flat-cache": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/flat-cache/download/flat-cache-2.0.1.tgz", "integrity": "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}, "engines": {"node": ">=4"}}, "node_modules/flat-cache/node_modules/rimraf": {"version": "2.6.3", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-2.6.3.tgz", "integrity": "sha1-stEE/g2Psnz54KHNqCYt04M8bKs=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/flatted": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/flatted/download/flatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=", "dev": true, "license": "ISC"}, "node_modules/fn.name": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/fn.name/download/fn.name-1.1.0.tgz", "integrity": "sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw=", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "resolved": "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "license": "MIT"}, "node_modules/fs-extra": {"version": "8.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz", "integrity": "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "resolved": "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz", "integrity": "sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functional-red-black-tree": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true, "license": "MIT"}, "node_modules/functions-have-names": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/generate-function": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/generate-function/download/generate-function-2.3.1.tgz", "integrity": "sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=", "license": "MIT", "dependencies": {"is-property": "^1.0.2"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz", "integrity": "sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/git-up": {"version": "4.0.5", "resolved": "http://r.npm.sankuai.com/git-up/download/git-up-4.0.5.tgz", "integrity": "sha1-57twmBo36i+4/gSWaYAKH5oB11k=", "license": "MIT", "dependencies": {"is-ssh": "^1.3.0", "parse-url": "^6.0.0"}}, "node_modules/git-url-parse": {"version": "11.1.2", "resolved": "http://r.npm.sankuai.com/git-url-parse/download/git-url-parse-11.1.2.tgz", "integrity": "sha1-r/Gol8NsyTaZJwWHvqPby7uV3mc=", "license": "MIT", "dependencies": {"git-up": "^4.0.0"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globalthis": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz", "integrity": "sha1-dDDtOpddl7+1m8zkH1yruvplEjY=", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "license": "ISC"}, "node_modules/growl": {"version": "1.10.5", "resolved": "http://r.npm.sankuai.com/growl/download/growl-1.10.5.tgz", "integrity": "sha1-8nNdwig2dPpnR4sQGBBZNVw2nl4=", "dev": true, "license": "MIT", "engines": {"node": ">=4.x"}}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "resolved": "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz", "integrity": "sha1-HwgDufjLIMD6E4It8ezds2veHv0=", "deprecated": "this library is no longer supported", "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has-bigints": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz", "integrity": "sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz", "integrity": "sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/he/download/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/inflection": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/inflection/download/inflection-1.13.4.tgz", "integrity": "sha1-ZappbE4tpiJbFI16FUxEk2ZjOjI=", "engines": ["node >= 0.4.0"], "license": "MIT"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/inquirer": {"version": "6.5.2", "resolved": "http://r.npm.sankuai.com/inquirer/download/inquirer-6.5.2.tgz", "integrity": "sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^3.2.0", "chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^2.0.0", "lodash": "^4.17.12", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.4.0", "string-width": "^2.1.0", "strip-ansi": "^5.1.0", "through": "^2.3.6"}, "engines": {"node": ">=6.0.0"}}, "node_modules/inquirer/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/inquirer/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/internal-slot": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz", "integrity": "sha1-HqyRdilH0vcFa8g42T4TsulgSWE=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ip": {"version": "1.1.9", "resolved": "http://r.npm.sankuai.com/ip/download/ip-1.1.9.tgz", "integrity": "sha1-jfvMmadU0H9CUxC4aplUaxFR45Y=", "license": "MIT"}, "node_modules/is-array-buffer": {"version": "3.0.5", "resolved": "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz", "integrity": "sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "integrity": "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=", "license": "MIT"}, "node_modules/is-async-function": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.1.tgz", "integrity": "sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=", "dev": true, "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz", "integrity": "sha1-3aejRF31ekJYPbQihoLrp8QXBnI=", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bluebird": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-bluebird/download/is-bluebird-1.0.2.tgz", "integrity": "sha1-CWQ5Bg9KpBGr7hkUOoTWpVNG1uI=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.2.tgz", "integrity": "sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz", "integrity": "sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz", "integrity": "sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz", "integrity": "sha1-7v3NxslN3QZ02chYh7+T+USpfJA=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-map": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz", "integrity": "sha1-7elrf+HicLPERl46RlZYdkkm1i4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number-object": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz", "integrity": "sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-property": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-property/download/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=", "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz", "integrity": "sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz", "integrity": "sha1-m2eES9m38ka6BwjDqT40Jpx3T28=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ssh": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/is-ssh/download/is-ssh-1.4.1.tgz", "integrity": "sha1-dt4c2+j5KouQXRoXK2vAlwTCA5Y=", "license": "MIT", "dependencies": {"protocols": "^2.0.1"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-string": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz", "integrity": "sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz", "integrity": "sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "license": "MIT"}, "node_modules/is-weakmap": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz", "integrity": "sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.1.tgz", "integrity": "sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz", "integrity": "sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "license": "MIT"}, "node_modules/joycon": {"version": "2.2.5", "resolved": "http://r.npm.sankuai.com/joycon/download/joycon-2.2.5.tgz", "integrity": "sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "license": "MIT"}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz", "integrity": "sha1-995M9u+rg4666zI2R0y7paGTCrU=", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "license": "ISC"}, "node_modules/json5": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz", "integrity": "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/jsonfile": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz", "integrity": "sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=", "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/kuler": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/kuler/download/kuler-2.0.0.tgz", "integrity": "sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM=", "license": "MIT"}, "node_modules/lazystream": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/lazystream/download/lazystream-1.0.1.tgz", "integrity": "sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=", "license": "MIT", "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lazystream/node_modules/isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/lazystream/node_modules/readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/lazystream/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/lazystream/node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/levn": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash.defaults": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz", "integrity": "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=", "license": "MIT"}, "node_modules/lodash.difference": {"version": "4.5.0", "resolved": "http://r.npm.sankuai.com/lodash.difference/download/lodash.difference-4.5.0.tgz", "integrity": "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=", "license": "MIT"}, "node_modules/lodash.flatten": {"version": "4.4.0", "resolved": "http://r.npm.sankuai.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=", "license": "MIT"}, "node_modules/lodash.union": {"version": "4.6.0", "resolved": "http://r.npm.sankuai.com/lodash.union/download/lodash.union-4.6.0.tgz", "integrity": "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=", "license": "MIT"}, "node_modules/log-symbols": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz", "integrity": "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=", "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/log4js": {"version": "6.9.1", "resolved": "http://r.npm.sankuai.com/log4js/download/log4js-6.9.1.tgz", "integrity": "sha1-q6Wj/054cq40+LTFM3BnU3CeOLY=", "license": "Apache-2.0", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "flatted": "^3.2.7", "rfdc": "^1.3.0", "streamroller": "^3.1.5"}, "engines": {"node": ">=8.0"}}, "node_modules/log4js/node_modules/flatted": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "license": "ISC"}, "node_modules/logform": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/logform/download/logform-2.7.0.tgz", "integrity": "sha1-z8qXUo7ykPLhJaCDloBQArLQYNE=", "license": "MIT", "dependencies": {"@colors/colors": "1.6.0", "@types/triple-beam": "^1.3.2", "fecha": "^4.2.0", "ms": "^2.1.1", "safe-stable-stringify": "^2.3.1", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/long": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/long/download/long-4.0.0.tgz", "integrity": "sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=", "license": "Apache-2.0"}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/lru-cache/node_modules/yallist": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "license": "ISC"}, "node_modules/lru.min": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/lru.min/download/lru.min-1.1.2.tgz", "integrity": "sha1-Ac4dcsxQx/r4vR+Anr8F1DMQIes=", "license": "MIT", "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minimost": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/minimost/download/minimost-1.2.0.tgz", "integrity": "sha1-o3+R1gOV/AAxgNIIyp4DFrzE46I=", "license": "MIT", "dependencies": {"@types/minimist": "^1.2.0", "minimist": "^1.2.0"}, "engines": {"node": ">=4"}}, "node_modules/minipass": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz", "integrity": "sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mocha": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/mocha/download/mocha-5.2.0.tgz", "integrity": "sha1-bYrlCPWRZ/lA8rWzxKYSrlDJCuY=", "dev": true, "license": "MIT", "dependencies": {"browser-stdout": "1.3.1", "commander": "2.15.1", "debug": "3.1.0", "diff": "3.5.0", "escape-string-regexp": "1.0.5", "glob": "7.1.2", "growl": "1.10.5", "he": "1.1.1", "minimatch": "3.0.4", "mkdirp": "0.5.1", "supports-color": "5.4.0"}, "bin": {"_mocha": "bin/_mocha", "mocha": "bin/mocha"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/mocha/node_modules/debug": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/mocha/node_modules/glob": {"version": "7.1.2", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.1.2.tgz", "integrity": "sha1-wZyd+aAocC1nhhI4SmVSQExjbRU=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/mocha/node_modules/minimatch": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/mocha/node_modules/minimist": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true, "license": "MIT"}, "node_modules/mocha/node_modules/mkdirp": {"version": "0.5.1", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mocha/node_modules/ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/mocha/node_modules/supports-color": {"version": "5.4.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.4.0.tgz", "integrity": "sha1-HGszdALCE3YF7+GfEP7DkPb6q1Q=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/moment": {"version": "2.30.1", "resolved": "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.48", "resolved": "http://r.npm.sankuai.com/moment-timezone/download/moment-timezone-0.5.48.tgz", "integrity": "sha1-ERcnuydHNKUYrhVLXKWJKD8FiWc=", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/mute-stream": {"version": "0.0.7", "resolved": "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true, "license": "ISC"}, "node_modules/mysql2": {"version": "3.14.1", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-3.14.1.tgz", "integrity": "sha1-d4YWCr8Ib9J54CU+FuNMBbSrOz4=", "license": "MIT", "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "engines": {"node": ">= 8.0"}}, "node_modules/mysql2/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mysql2/node_modules/long": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/long/download/long-5.3.2.tgz", "integrity": "sha1-HYRGMJWZkmLX17f4v9SozFUWf4M=", "license": "Apache-2.0"}, "node_modules/named-placeholders": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/named-placeholders/download/named-placeholders-1.1.3.tgz", "integrity": "sha1-31lXmaNmVNpV3aYVK6ehN60dk1E=", "license": "MIT", "dependencies": {"lru-cache": "^7.14.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/named-placeholders/node_modules/lru-cache": {"version": "7.18.3", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz", "integrity": "sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true, "license": "MIT"}, "node_modules/node-addon-api": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-3.2.1.tgz", "integrity": "sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=", "license": "MIT"}, "node_modules/node-stream-zip": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz", "integrity": "sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o=", "license": "MIT", "engines": {"node": ">=0.12.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/antelle"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz", "integrity": "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.fromentries": {"version": "2.0.8", "resolved": "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz", "integrity": "sha1-9xldipuXvZXLwZmeqTns0aKwDGU=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/object.groupby/download/object.groupby-1.0.3.tgz", "integrity": "sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.values": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz", "integrity": "sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/one-time": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/one-time/download/one-time-1.0.0.tgz", "integrity": "sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=", "license": "MIT", "dependencies": {"fn.name": "1.x.x"}}, "node_modules/onetime": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/optionator": {"version": "0.8.3", "resolved": "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz", "integrity": "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "3.4.0", "resolved": "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz", "integrity": "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=", "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/ora/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ora/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/own-keys": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz", "integrity": "sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-path": {"version": "4.0.4", "resolved": "http://r.npm.sankuai.com/parse-path/download/parse-path-4.0.4.tgz", "integrity": "sha1-S/Qk5rdD+wgIMfA7U2r5/EPw/+o=", "license": "MIT", "dependencies": {"is-ssh": "^1.3.0", "protocols": "^1.4.0", "qs": "^6.9.4", "query-string": "^6.13.8"}}, "node_modules/parse-path/node_modules/protocols": {"version": "1.4.8", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-1.4.8.tgz", "integrity": "sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg=", "license": "MIT"}, "node_modules/parse-path/node_modules/qs": {"version": "6.14.0", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/parse-url": {"version": "6.0.5", "resolved": "http://r.npm.sankuai.com/parse-url/download/parse-url-6.0.5.tgz", "integrity": "sha1-Ssq4mCzvGEag+GdfpobO8ksvb5s=", "license": "MIT", "dependencies": {"is-ssh": "^1.3.0", "normalize-url": "^6.1.0", "parse-path": "^4.0.0", "protocols": "^1.4.0"}}, "node_modules/parse-url/node_modules/protocols": {"version": "1.4.8", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-1.4.8.tgz", "integrity": "sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg=", "license": "MIT"}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-inside": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/path-is-inside/download/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true, "license": "(WTFPL OR MIT)"}, "node_modules/path-key": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "license": "MIT"}, "node_modules/pg-connection-string": {"version": "2.9.0", "resolved": "http://r.npm.sankuai.com/pg-connection-string/download/pg-connection-string-2.9.0.tgz", "integrity": "sha1-914GWR/dQux2Nv4sagP+vu2+yb8=", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true, "license": "ISC"}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/prelude-ls": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "license": "MIT"}, "node_modules/progress": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/properties-parser": {"version": "0.3.1", "resolved": "http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz", "integrity": "sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=", "license": "MIT", "dependencies": {"string.prototype.codepointat": "^0.2.0"}, "engines": {"node": ">= 0.3.1"}}, "node_modules/protocols": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-2.0.2.tgz", "integrity": "sha1-gi6Pzcs99TVlOLPpG/2JCwZ/0KQ=", "license": "MIT"}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/psl": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz", "integrity": "sha1-vazjGJbx2XzsannoIkiYzpPZdMY=", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.5.3", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz", "integrity": "sha1-Ou7/yRln7241wOSI70b7KWq3aq0=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/query-string": {"version": "6.14.1", "resolved": "http://r.npm.sankuai.com/query-string/download/query-string-6.14.1.tgz", "integrity": "sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=", "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.0", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/redent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz", "integrity": "sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=", "license": "MIT", "dependencies": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz", "integrity": "sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "resolved": "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexpp": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/regexpp/download/regexpp-2.0.1.tgz", "integrity": "sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.5.0"}}, "node_modules/request": {"version": "2.88.2", "resolved": "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz", "integrity": "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/restore-cursor": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/retry-as-promised": {"version": "7.1.1", "resolved": "http://r.npm.sankuai.com/retry-as-promised/download/retry-as-promised-7.1.1.tgz", "integrity": "sha1-NiYkbwTBlB/xDOvPo98Fd/2Kstc=", "license": "MIT"}, "node_modules/rfdc": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz", "integrity": "sha1-d492xPtzHZNBTo+SX77PZMzn9so=", "license": "MIT"}, "node_modules/rimraf": {"version": "2.7.1", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/run-async": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/rxjs": {"version": "6.6.7", "resolved": "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz", "integrity": "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz", "integrity": "sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz", "integrity": "sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-stable-stringify": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/safe-stable-stringify/download/safe-stable-stringify-2.5.0.tgz", "integrity": "sha1-TKL444XygxxDKnGbEIo7969Cod0=", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/sax": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/sax/download/sax-1.4.1.tgz", "integrity": "sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=", "license": "ISC"}, "node_modules/semver": {"version": "6.3.1", "resolved": "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/seq-queue": {"version": "0.0.5", "resolved": "http://r.npm.sankuai.com/seq-queue/download/seq-queue-0.0.5.tgz", "integrity": "sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4="}, "node_modules/sequelize": {"version": "6.37.7", "resolved": "http://r.npm.sankuai.com/sequelize/download/sequelize-6.37.7.tgz", "integrity": "sha1-Vab4VVrnbB+9S852sqxfzAoebrY=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/sequelize"}], "license": "MIT", "dependencies": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.6", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.1", "retry-as-promised": "^7.0.4", "semver": "^7.5.4", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}, "engines": {"node": ">=10.0.0"}, "peerDependenciesMeta": {"ibm_db": {"optional": true}, "mariadb": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-hstore": {"optional": true}, "snowflake-sdk": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}}}, "node_modules/sequelize-pool": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/sequelize-pool/download/sequelize-pool-2.3.0.tgz", "integrity": "sha1-ZPH+h0QigXLEdPUwYEthM75kmT0=", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/sequelize/node_modules/semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/sequelize/node_modules/sequelize-pool": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/sequelize-pool/download/sequelize-pool-7.1.0.tgz", "integrity": "sha1-IQs5GvQAJ2L4IxiP1uz8dBMCB2g=", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/sequelize/node_modules/uuid": {"version": "8.3.2", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz", "integrity": "sha1-FqcFxaDcL15jjKltiozU4cK5CYU=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz", "integrity": "sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/shebang-command": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/shimmer": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/shimmer/download/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "license": "ISC"}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/slice-ansi": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz", "integrity": "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/split-on-first": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/split-on-first/download/split-on-first-1.1.0.tgz", "integrity": "sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sqlstring": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/sqlstring/download/sqlstring-2.3.3.tgz", "integrity": "sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/sshpk": {"version": "1.18.0", "resolved": "http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz", "integrity": "sha1-FmPlXN301oi4aka3fw1f42OroCg=", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stack-trace": {"version": "0.0.10", "resolved": "http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/streamroller": {"version": "3.1.5", "resolved": "http://r.npm.sankuai.com/streamroller/download/streamroller-3.1.5.tgz", "integrity": "sha1-EmMYIymkXe8f+u9Y0xsV0T0u5/8=", "license": "MIT", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "fs-extra": "^8.1.0"}, "engines": {"node": ">=8.0"}}, "node_modules/strict-uri-encode": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz", "integrity": "sha1-ucczDHBChi9rFC3CdLvMWGbONUY=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/string.prototype.codepointat": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz", "integrity": "sha1-AErUTIr8cnUnsQjNRitNlxzUabw=", "license": "MIT"}, "node_modules/string.prototype.trim": {"version": "1.2.10", "resolved": "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz", "integrity": "sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz", "integrity": "sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz", "integrity": "sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/strip-bom": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/table": {"version": "5.4.6", "resolved": "http://r.npm.sankuai.com/table/download/table-5.4.6.tgz", "integrity": "sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/table/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/table/node_modules/string-width": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/table/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/tar/download/tar-6.2.1.tgz", "integrity": "sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-stream": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz", "integrity": "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=", "license": "MIT", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tar/node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/text-hex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/text-hex/download/text-hex-1.0.0.tgz", "integrity": "sha1-adycGxdEbueakr9biEu0uRJ1BvU=", "license": "MIT"}, "node_modules/text-table": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true, "license": "MIT"}, "node_modules/tmp": {"version": "0.0.33", "resolved": "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/toposort-class": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/toposort-class/download/toposort-class-1.0.1.tgz", "integrity": "sha1-f/0feMi+KMO6Rc1OGj9e4ZO9mYg=", "license": "MIT"}, "node_modules/tough-cookie": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/triple-beam": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/triple-beam/download/triple-beam-1.4.1.tgz", "integrity": "sha1-b95wJx3G5dc8oMOyTi2Sr7dEGYQ=", "license": "MIT", "engines": {"node": ">= 14.0.0"}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "resolved": "http://r.npm.sankuai.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz", "integrity": "sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tslib": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=", "dev": true, "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "license": "Unlicense"}, "node_modules/type-check": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz", "integrity": "sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz", "integrity": "sha1-hAegT314aE89JSqhoUPSt3tBYM4=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz", "integrity": "sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz", "integrity": "sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz", "integrity": "sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/universalify": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/uuid": {"version": "3.4.0", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/validator": {"version": "13.15.0", "resolved": "http://r.npm.sankuai.com/validator/download/validator-13.15.0.tgz", "integrity": "sha1-LcfOBX51E6VVhRCe7CmyyOjBrv0=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/which": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz", "integrity": "sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz", "integrity": "sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz", "integrity": "sha1-Yn73YkOSChB+fOjpYZHevksWwqA=", "dev": true, "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/winston": {"version": "3.12.0", "resolved": "http://r.npm.sankuai.com/winston/download/winston-3.12.0.tgz", "integrity": "sha1-pdllpB09wxvlQI+MZuknlYhGwNA=", "license": "MIT", "dependencies": {"@colors/colors": "^1.6.0", "@dabh/diagnostics": "^2.0.2", "async": "^3.2.3", "is-stream": "^2.0.0", "logform": "^2.4.0", "one-time": "^1.0.0", "readable-stream": "^3.4.0", "safe-stable-stringify": "^2.3.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.7.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/winston-transport": {"version": "4.9.0", "resolved": "http://r.npm.sankuai.com/winston-transport/download/winston-transport-4.9.0.tgz", "integrity": "sha1-O7o0XeECl2VOpvM1GUJFYAA7O/k=", "license": "MIT", "dependencies": {"logform": "^2.7.0", "readable-stream": "^3.6.2", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/winston/node_modules/async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "license": "MIT"}, "node_modules/wkx": {"version": "0.5.0", "resolved": "http://r.npm.sankuai.com/wkx/download/wkx-0.5.0.tgz", "integrity": "sha1-xsNwGaz0DlF8xrlGV6JaPUqjPow=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/write": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/write/download/write-1.0.3.tgz", "integrity": "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=", "dev": true, "license": "MIT", "dependencies": {"mkdirp": "^0.5.1"}, "engines": {"node": ">=4"}}, "node_modules/xml2js": {"version": "0.4.23", "resolved": "http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz", "integrity": "sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz", "integrity": "sha1-vpuuHIoEbnazESdyY0fQrXACvrM=", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/zip-stream": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/zip-stream/download/zip-stream-2.1.3.tgz", "integrity": "sha1-JsxL25NkGoWQ3QcRLh93rxdYhls=", "license": "MIT", "dependencies": {"archiver-utils": "^2.1.0", "compress-commons": "^2.1.1", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 6"}}}, "dependencies": {"@babel/code-frame": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "dev": true}, "@bfe/zebra-node-proxy": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/@bfe/zebra-node-proxy/download/@bfe/zebra-node-proxy-1.0.5.tgz", "integrity": "sha1-QjzuouemoDgnPwnvyXScIPCPiGI=", "requires": {"@dp/cat-client": "^3.0.4", "@dp/lion-client": "^3.2.1", "@dp/node-kms": "^2.0.9", "@mtfe/cat": "^1.1.0", "bluebird": "^3.7.2", "dayjs": "^1.11.10", "ip": "^1.1.8", "lodash": "^4.17.21", "log4js": "^6.9.1", "mysql2": "^2.3.3", "request": "^2.88.2"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "lru-cache": {"version": "6.0.0", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "requires": {"yallist": "^4.0.0"}}, "mysql2": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-2.3.3.tgz", "integrity": "sha1-lE897KSxZikFL/hhT7+J1VUlRaA=", "requires": {"denque": "^2.0.1", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^4.0.0", "lru-cache": "^6.0.0", "named-placeholders": "^1.1.2", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}}}}, "@bfe/zebra-proxy-sequelize": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/@bfe/zebra-proxy-sequelize/download/@bfe/zebra-proxy-sequelize-1.0.6.tgz", "integrity": "sha1-yweWrHLP4j79x3qjjPFlgK3RyzY=", "requires": {"@bfe/zebra-node-proxy": "1.0.5", "@dp/cat-client": "^3.0.4", "@dp/node-kms": "^2.0.9", "@mtfe/cat": "^1.1.0", "ip": "^1.1.8", "lodash": "^4.17.21", "log4js": "^6.9.1", "mysql2": "^1.6.5", "sequelize": "^5.9.4", "sequelize-pool": "^2.3.0", "uuid": "^9.0.1"}, "dependencies": {"denque": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/denque/download/denque-1.5.1.tgz", "integrity": "sha1-B/Zw4pyaePj67LJWah4sEZKcXL8="}, "iconv-lite": {"version": "0.5.2", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.5.2.tgz", "integrity": "sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "inflection": {"version": "1.12.0", "resolved": "http://r.npm.sankuai.com/inflection/download/inflection-1.12.0.tgz", "integrity": "sha1-ogCTVlbW9fa8TcdQLhrstwMihBY="}, "mysql2": {"version": "1.7.0", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-1.7.0.tgz", "integrity": "sha1-L78xTaAWph0Dj/zVeioKo7e46sw=", "requires": {"denque": "^1.4.1", "generate-function": "^2.3.1", "iconv-lite": "^0.5.0", "long": "^4.0.0", "lru-cache": "^5.1.1", "named-placeholders": "^1.1.2", "seq-queue": "^0.0.5", "sqlstring": "^2.3.1"}}, "retry-as-promised": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/retry-as-promised/download/retry-as-promised-3.2.0.tgz", "integrity": "sha1-dp9j1Ta+xHg1SdsHd8tW2t2dhUM=", "requires": {"any-promise": "^1.3.0"}}, "sequelize": {"version": "5.22.5", "resolved": "http://r.npm.sankuai.com/sequelize/download/sequelize-5.22.5.tgz", "integrity": "sha1-/3/dNJgKLZVFakpX4WFTwg1X6W4=", "requires": {"bluebird": "^3.5.0", "cls-bluebird": "^2.1.0", "debug": "^4.1.1", "dottie": "^2.0.0", "inflection": "1.12.0", "lodash": "^4.17.15", "moment": "^2.24.0", "moment-timezone": "^0.5.21", "retry-as-promised": "^3.2.0", "semver": "^6.3.0", "sequelize-pool": "^2.3.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.7.0", "wkx": "^0.4.8"}, "dependencies": {"uuid": {"version": "8.3.2", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}}}, "uuid": {"version": "9.0.1", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-9.0.1.tgz", "integrity": "sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA="}, "wkx": {"version": "0.4.8", "resolved": "http://r.npm.sankuai.com/wkx/download/wkx-0.4.8.tgz", "integrity": "sha1-oJLPCI0RJoP9xxgv0xSTssWCAAM=", "requires": {"@types/node": "*"}}}}, "@colors/colors": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/@colors/colors/download/@colors/colors-1.6.0.tgz", "integrity": "sha1-7GzSN0QHALwjyiMIf1E8dVCJWLA="}, "@dabh/diagnostics": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz", "integrity": "sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=", "requires": {"colorspace": "1.1.x", "enabled": "2.0.x", "kuler": "^2.0.0"}}, "@dp/cat-client": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-3.0.4.tgz", "integrity": "sha1-xy3RivDxDiLsgZmgyAkgmBTE0pU=", "requires": {"@dp/logger-container": "^1.1.0", "@dp/simple-util": "^1.0.0", "buffer-builder": "^0.2.0", "debug": "^2.2.0", "mkdirp": "^0.5.1", "moment": "^2.10.6", "node-addon-api": "^3.1.0", "request": "^2.67.0", "semver": "^6.1.2", "xml2js": "^0.4.15"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "@dp/lion-client": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/@dp/lion-client/download/@dp/lion-client-3.3.1.tgz", "integrity": "sha1-XwDz7tzEQrOVn+EN6boKNWs8tWI=", "requires": {"@dp/logger-container": "^1.2.0", "@dp/server-env": "^1.0.3", "@dp/simple-util": "^1.1.1", "@mtfe/cat": "^1.1.0", "async": "^3.2.4", "async-lock": "^1.4.0", "imurmurhash": "^0.1.4", "ip": "^1.1.5", "moment": "^2.19.4", "request": "^2.88.2", "winston": "3.12.0"}, "dependencies": {"async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4="}}}, "@dp/logger-container": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz", "integrity": "sha1-AuoLwhN37/N6UHL/tRTccMB88m8="}, "@dp/node-kms": {"version": "2.0.13", "resolved": "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.13.tgz", "integrity": "sha1-aOGzsEXrSiDJj0aCHIBICVl+Qx0=", "requires": {"@dp/server-env": "^1.0.3", "@dp/simple-util": "^1.1.1", "bindings": "^1.5.0", "co-request": "^1.0.0", "ip": "^1.1.9"}}, "@dp/server-env": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz", "integrity": "sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=", "requires": {"@dp/logger-container": "^1.1.0", "properties-parser": "^0.3.1"}}, "@dp/simple-util": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz", "integrity": "sha1-lyx6QVEa7L2mekrEiTstPr89WEE="}, "@fdfe/era-cloud-uploader": {"version": "0.11.5", "resolved": "http://r.npm.sankuai.com/@fdfe/era-cloud-uploader/download/@fdfe/era-cloud-uploader-0.11.5.tgz", "integrity": "sha1-X0Gz5D5fAIX6f7n2KvPbMcFYQ70=", "requires": {"@zeit/ncc": "^0.20.5", "archiver": "^3.0.0", "cac": "^5.0.12", "chalk": "^2.4.1", "debug": "^4.1.0", "fs-extra": "^8.1.0", "glob": "^7.1.3", "js-yaml": "^3.12.0", "lodash": "^4.17.14", "minimatch": "^3.0.4", "node-stream-zip": "^1.7.0", "ora": "^3.0.0", "request": "^2.88.0", "rimraf": "^2.6.2", "semver": "^6.0.0", "tar": "^6.0.1"}}, "@mtfe/cat": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@mtfe/cat/download/@mtfe/cat-1.1.0.tgz", "integrity": "sha1-N2Y+9a+8nQng2bQAhAbWEa140Ag=", "requires": {"@dp/cat-client": "^3.0.3", "debug": "^4.3.3"}}, "@nibfe/talos-public-api": {"version": "1.2.15", "resolved": "http://r.npm.sankuai.com/@nibfe/talos-public-api/download/@nibfe/talos-public-api-1.2.15.tgz", "integrity": "sha1-frU4CU3ryT8AhL3Ru4GgqrTw9tw=", "requires": {"@types/git-url-parse": "9.0.0", "axios": "0.21.1", "form-data": "2.5.0", "git-url-parse": "11.1.2"}, "dependencies": {"axios": {"version": "0.21.1", "resolved": "http://r.npm.sankuai.com/axios/download/axios-0.21.1.tgz", "integrity": "sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=", "requires": {"follow-redirects": "^1.10.0"}}, "form-data": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-2.5.0.tgz", "integrity": "sha1-CU7DWdxLVefWLg20rNduif6HTTc=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}}}, "@rtsao/scc": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/@rtsao/scc/download/@rtsao/scc-1.1.0.tgz", "integrity": "sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=", "dev": true}, "@types/debug": {"version": "4.1.12", "resolved": "http://r.npm.sankuai.com/@types/debug/download/@types/debug-4.1.12.tgz", "integrity": "sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=", "requires": {"@types/ms": "*"}}, "@types/git-url-parse": {"version": "9.0.0", "resolved": "http://r.npm.sankuai.com/@types/git-url-parse/download/@types/git-url-parse-9.0.0.tgz", "integrity": "sha1-qsExWkT6TtWlLDgg9sPC+3nL0S0="}, "@types/json5": {"version": "0.0.29", "resolved": "http://r.npm.sankuai.com/@types/json5/download/@types/json5-0.0.29.tgz", "integrity": "sha1-7ihweulOEdK4J7y+UnC86n8+ce4=", "dev": true}, "@types/minimist": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.5.tgz", "integrity": "sha1-7BB1XocUl7zYPv6SfkPsRujAdH4="}, "@types/ms": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/@types/ms/download/@types/ms-2.1.0.tgz", "integrity": "sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g="}, "@types/node": {"version": "22.15.21", "resolved": "http://r.npm.sankuai.com/@types/node/download/@types/node-22.15.21.tgz", "integrity": "sha1-GW7xT+INh/fK8eezmDJ2f5qZW3c=", "requires": {"undici-types": "~6.21.0"}}, "@types/triple-beam": {"version": "1.3.5", "resolved": "http://r.npm.sankuai.com/@types/triple-beam/download/@types/triple-beam-1.3.5.tgz", "integrity": "sha1-dP75/7qhmOuLWIvgKfOLACmcqiw="}, "@types/validator": {"version": "13.15.1", "resolved": "http://r.npm.sankuai.com/@types/validator/download/@types/validator-13.15.1.tgz", "integrity": "sha1-UvNhfrW6MN0AGDgMZFaEQNC03go="}, "@zeit/ncc": {"version": "0.20.5", "resolved": "http://r.npm.sankuai.com/@zeit/ncc/download/@zeit/ncc-0.20.5.tgz", "integrity": "sha1-pBr25ryrSlj0YSuuYTf3C84BkuM="}, "acorn": {"version": "6.4.2", "resolved": "http://r.npm.sankuai.com/acorn/download/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true}, "acorn-jsx": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "requires": {}}, "ajv": {"version": "6.12.6", "resolved": "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-escapes": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=", "dev": true}, "ansi-regex": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz", "integrity": "sha1-Ej1keekq1FrYl9QFTjx8p9tJROE="}, "ansi-styles": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "any-promise": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8="}, "archiver": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/archiver/download/archiver-3.1.1.tgz", "integrity": "sha1-nbeBnU2vYK7BD+hrFsuSWM7WbqA=", "requires": {"archiver-utils": "^2.1.0", "async": "^2.6.3", "buffer-crc32": "^0.2.1", "glob": "^7.1.4", "readable-stream": "^3.4.0", "tar-stream": "^2.1.0", "zip-stream": "^2.1.2"}}, "archiver-utils": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/archiver-utils/download/archiver-utils-2.1.0.tgz", "integrity": "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI=", "requires": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "argparse": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "array-buffer-byte-length": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz", "integrity": "sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=", "dev": true, "requires": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}}, "array-includes": {"version": "3.1.8", "resolved": "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz", "integrity": "sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=", "dev": true, "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "is-string": "^1.0.7"}}, "array.prototype.findlastindex": {"version": "1.2.6", "resolved": "http://r.npm.sankuai.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.6.tgz", "integrity": "sha1-z6EGXIHctk40VXybgdAS9qQhxWQ=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}}, "array.prototype.flat": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz", "integrity": "sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=", "dev": true, "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}}, "array.prototype.flatmap": {"version": "1.3.3", "resolved": "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz", "integrity": "sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=", "dev": true, "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}}, "arraybuffer.prototype.slice": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz", "integrity": "sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=", "dev": true, "requires": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}}, "asn1": {"version": "0.2.6", "resolved": "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz", "integrity": "sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "astral-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz", "integrity": "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=", "dev": true}, "async": {"version": "2.6.4", "resolved": "http://r.npm.sankuai.com/async/download/async-2.6.4.tgz", "integrity": "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=", "requires": {"lodash": "^4.17.14"}}, "async-function": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/async-function/download/async-function-1.0.0.tgz", "integrity": "sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=", "dev": true}, "async-lock": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/async-lock/download/async-lock-1.4.1.tgz", "integrity": "sha1-VrhxiRWptosQ/OLyqaPd33Ze9T8="}, "asynckit": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "available-typed-arrays": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "dev": true, "requires": {"possible-typed-array-names": "^1.0.0"}}, "aws-sign2": {"version": "0.7.0", "resolved": "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws-ssl-profiles": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/aws-ssl-profiles/download/aws-ssl-profiles-1.1.2.tgz", "integrity": "sha1-FX3Xfp8ZsdEjZ46T8SDm8ZMCJkE="}, "aws4": {"version": "1.13.2", "resolved": "http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz", "integrity": "sha1-CqFnIWllrJR0zPqDiSz7az4eUu8="}, "axios": {"version": "1.9.0", "resolved": "http://r.npm.sankuai.com/axios/download/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "requires": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}, "dependencies": {"form-data": {"version": "4.0.2", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}}}}, "balanced-match": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "base64-js": {"version": "1.5.1", "resolved": "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "requires": {"tweetnacl": "^0.14.3"}}, "bindings": {"version": "1.5.0", "resolved": "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "requires": {"file-uri-to-path": "1.0.0"}}, "bl": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "requires": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "bluebird": {"version": "3.7.2", "resolved": "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="}, "brace-expansion": {"version": "1.1.11", "resolved": "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "browser-stdout": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/browser-stdout/download/browser-stdout-1.3.1.tgz", "integrity": "sha1-uqVZ7hTO1zRSIputcyZGfGH6vWA=", "dev": true}, "buffer": {"version": "5.7.1", "resolved": "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "buffer-builder": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz", "integrity": "sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8="}, "buffer-crc32": {"version": "0.2.13", "resolved": "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "cac": {"version": "5.0.16", "resolved": "http://r.npm.sankuai.com/cac/download/cac-5.0.16.tgz", "integrity": "sha1-GRRpf1ihboY/m8K9G2ERZiBFwUc=", "requires": {"chalk": "^2.4.1", "joycon": "^2.1.2", "minimost": "^1.2.0", "redent": "^2.0.0", "string-width": "^2.1.1", "text-table": "^0.2.0"}}, "call-bind": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "dev": true, "requires": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}}, "call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "requires": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}}, "call-bound": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "requires": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}}, "callsites": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}, "caseless": {"version": "0.12.0", "resolved": "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chalk": {"version": "2.4.2", "resolved": "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chardet": {"version": "0.7.0", "resolved": "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=", "dev": true}, "chownr": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4="}, "cli-cursor": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "requires": {"restore-cursor": "^2.0.0"}}, "cli-spinners": {"version": "2.9.2", "resolved": "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz", "integrity": "sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE="}, "cli-width": {"version": "2.2.1", "resolved": "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz", "integrity": "sha1-sEM9C06chH7xiGik7xb9X8gnHEg=", "dev": true}, "clone": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="}, "cls-bluebird": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/cls-bluebird/download/cls-bluebird-2.1.0.tgz", "integrity": "sha1-N+8eCAqP+1XC9BZPU28ZGeeWiu4=", "requires": {"is-bluebird": "^1.0.2", "shimmer": "^1.1.0"}}, "co-request": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz", "integrity": "sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=", "requires": {"request": "*"}}, "color": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/color/download/color-3.2.1.tgz", "integrity": "sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=", "requires": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "color-string": {"version": "1.9.1", "resolved": "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz", "integrity": "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=", "requires": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "colorspace": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/colorspace/download/colorspace-1.1.4.tgz", "integrity": "sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=", "requires": {"color": "^3.1.3", "text-hex": "1.0.x"}}, "combined-stream": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.15.1", "resolved": "http://r.npm.sankuai.com/commander/download/commander-2.15.1.tgz", "integrity": "sha1-30boZ9D8Kuxmo0ZitAapzK//Ww8=", "dev": true}, "compress-commons": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/compress-commons/download/compress-commons-2.1.1.tgz", "integrity": "sha1-lBDZpTTPhDXj+7t8bOSN4twvBhA=", "requires": {"buffer-crc32": "^0.2.13", "crc32-stream": "^3.0.1", "normalize-path": "^3.0.0", "readable-stream": "^2.3.6"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "concat-map": {"version": "0.0.1", "resolved": "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "core-util-is": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "crc": {"version": "3.8.0", "resolved": "http://r.npm.sankuai.com/crc/download/crc-3.8.0.tgz", "integrity": "sha1-rWAmnCyFb4wpnixMwN5FVpFAVsY=", "requires": {"buffer": "^5.1.0"}}, "crc32-stream": {"version": "3.0.1", "resolved": "http://r.npm.sankuai.com/crc32-stream/download/crc32-stream-3.0.1.tgz", "integrity": "sha1-yubu7QA7DkTXOdJ53lrmOxcbToU=", "requires": {"crc": "^3.4.4", "readable-stream": "^3.4.0"}}, "cross-spawn": {"version": "6.0.6", "resolved": "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.6.tgz", "integrity": "sha1-MNDvoHEt2361p24ehyG/+vprXVc=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true}}}, "dashdash": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "data-view-buffer": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz", "integrity": "sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=", "dev": true, "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}}, "data-view-byte-length": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz", "integrity": "sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=", "dev": true, "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}}, "data-view-byte-offset": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz", "integrity": "sha1-BoMH+bcat2274QKROJ4CCFZgYZE=", "dev": true, "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}}, "date-format": {"version": "4.0.14", "resolved": "http://r.npm.sankuai.com/date-format/download/date-format-4.0.14.tgz", "integrity": "sha1-eo5YRDT7FppSHIt6pIHzVYENlAA="}, "dayjs": {"version": "1.11.13", "resolved": "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw="}, "debug": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "requires": {"ms": "^2.1.3"}}, "decode-uri-component": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz", "integrity": "sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek="}, "deep-is": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true}, "defaults": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz", "integrity": "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=", "requires": {"clone": "^1.0.2"}}, "define-data-property": {"version": "1.1.4", "resolved": "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "dev": true, "requires": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}}, "define-properties": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "dev": true, "requires": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "denque": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/denque/download/denque-2.1.0.tgz", "integrity": "sha1-6T4aZWn7XmbxajwqKWRhfTSdarE="}, "diff": {"version": "3.5.0", "resolved": "http://r.npm.sankuai.com/diff/download/diff-3.5.0.tgz", "integrity": "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=", "dev": true}, "doctrine": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "requires": {"esutils": "^2.0.2"}}, "dottie": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/dottie/download/dottie-2.0.6.tgz", "integrity": "sha1-NFZOv8bsXldyJy1GZCStW2lkhNQ="}, "dunder-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "requires": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true}, "enabled": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/enabled/download/enabled-2.0.0.tgz", "integrity": "sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I="}, "end-of-stream": {"version": "1.4.4", "resolved": "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "requires": {"once": "^1.4.0"}}, "es-abstract": {"version": "1.23.10", "resolved": "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.10.tgz", "integrity": "sha1-hHksFS/yiY7HPv4zwcEyOj39h/g=", "dev": true, "requires": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-regex": "^1.2.1", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}}, "es-define-property": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo="}, "es-errors": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8="}, "es-object-atoms": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "requires": {"es-errors": "^1.3.0"}}, "es-set-tostringtag": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "requires": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "es-shim-unscopables": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz", "integrity": "sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=", "dev": true, "requires": {"hasown": "^2.0.2"}}, "es-to-primitive": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz", "integrity": "sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=", "dev": true, "requires": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "eslint": {"version": "5.16.0", "resolved": "http://r.npm.sankuai.com/eslint/download/eslint-5.16.0.tgz", "integrity": "sha1-oeOsGq5KP72Clvz496tzFMu2q+o=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.9.1", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^4.0.3", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.1", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^6.2.2", "js-yaml": "^3.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.11", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^5.2.3", "text-table": "^0.2.0"}, "dependencies": {"semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true}}}, "eslint-config-standard": {"version": "12.0.0", "resolved": "http://r.npm.sankuai.com/eslint-config-standard/download/eslint-config-standard-12.0.0.tgz", "integrity": "sha1-Y4tMZdsL1aQTGflruh8V3a0hB9k=", "dev": true, "requires": {}}, "eslint-import-resolver-node": {"version": "0.3.9", "resolved": "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz", "integrity": "sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=", "dev": true, "requires": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-module-utils": {"version": "2.12.0", "resolved": "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.12.0.tgz", "integrity": "sha1-/kz7lI1h9JID17CIcZgrZbmvCws=", "dev": true, "requires": {"debug": "^3.2.7"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-plugin-es": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz", "integrity": "sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=", "dev": true, "requires": {"eslint-utils": "^1.4.2", "regexpp": "^2.0.1"}}, "eslint-plugin-import": {"version": "2.31.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.31.0.tgz", "integrity": "sha1-MQzn5yDKHZwLs/aa39HGvdfZ4Oc=", "dev": true, "requires": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.8", "array.prototype.findlastindex": "^1.2.5", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.0", "hasown": "^2.0.2", "is-core-module": "^2.15.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.0", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.8", "tsconfig-paths": "^3.15.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "requires": {"ms": "^2.1.1"}}, "doctrine": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "dev": true, "requires": {"esutils": "^2.0.2"}}}}, "eslint-plugin-node": {"version": "8.0.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-node/download/eslint-plugin-node-8.0.1.tgz", "integrity": "sha1-Va41YAIoY9FB+noReZUyNApoWWQ=", "dev": true, "requires": {"eslint-plugin-es": "^1.3.1", "eslint-utils": "^1.3.1", "ignore": "^5.0.2", "minimatch": "^3.0.4", "resolve": "^1.8.1", "semver": "^5.5.0"}, "dependencies": {"ignore": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true}, "semver": {"version": "5.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true}}}, "eslint-plugin-promise": {"version": "4.3.1", "resolved": "http://r.npm.sankuai.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz", "integrity": "sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=", "dev": true}, "eslint-plugin-standard": {"version": "4.1.0", "resolved": "http://r.npm.sankuai.com/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz", "integrity": "sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=", "dev": true, "requires": {}}, "eslint-scope": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "1.4.3", "resolved": "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-1.4.3.tgz", "integrity": "sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=", "dev": true, "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true}, "espree": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/espree/download/espree-5.0.1.tgz", "integrity": "sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=", "dev": true, "requires": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}}, "esprima": {"version": "4.0.1", "resolved": "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="}, "esquery": {"version": "1.6.0", "resolved": "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true}}}, "esrecurse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true}}}, "estraverse": {"version": "4.3.0", "resolved": "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true}, "extend": {"version": "3.0.2", "resolved": "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="}, "external-editor": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "dev": true, "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "extsprintf": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "fecha": {"version": "4.2.3", "resolved": "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz", "integrity": "sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0="}, "figures": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz", "integrity": "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=", "dev": true, "requires": {"flat-cache": "^2.0.1"}}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="}, "filter-obj": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/filter-obj/download/filter-obj-1.1.0.tgz", "integrity": "sha1-mzERErxsYSehbgFsbF1/GeCAXFs="}, "flat-cache": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/flat-cache/download/flat-cache-2.0.1.tgz", "integrity": "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=", "dev": true, "requires": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}, "dependencies": {"rimraf": {"version": "2.6.3", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-2.6.3.tgz", "integrity": "sha1-stEE/g2Psnz54KHNqCYt04M8bKs=", "dev": true, "requires": {"glob": "^7.1.3"}}}}, "flatted": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/flatted/download/flatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=", "dev": true}, "fn.name": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/fn.name/download/fn.name-1.1.0.tgz", "integrity": "sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw="}, "follow-redirects": {"version": "1.15.9", "resolved": "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE="}, "for-each": {"version": "0.3.5", "resolved": "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "dev": true, "requires": {"is-callable": "^1.2.7"}}, "forever-agent": {"version": "0.6.1", "resolved": "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "fs-constants": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="}, "fs-extra": {"version": "8.1.0", "resolved": "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz", "integrity": "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=", "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-minipass": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "requires": {"yallist": "^4.0.0"}}}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "function-bind": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw="}, "function.prototype.name": {"version": "1.1.8", "resolved": "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz", "integrity": "sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "functions-have-names": {"version": "1.2.3", "resolved": "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=", "dev": true}, "generate-function": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/generate-function/download/generate-function-2.3.1.tgz", "integrity": "sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=", "requires": {"is-property": "^1.0.2"}}, "get-intrinsic": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "requires": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}}, "get-proto": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "requires": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}}, "get-symbol-description": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz", "integrity": "sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=", "dev": true, "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}}, "getpass": {"version": "0.1.7", "resolved": "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "git-up": {"version": "4.0.5", "resolved": "http://r.npm.sankuai.com/git-up/download/git-up-4.0.5.tgz", "integrity": "sha1-57twmBo36i+4/gSWaYAKH5oB11k=", "requires": {"is-ssh": "^1.3.0", "parse-url": "^6.0.0"}}, "git-url-parse": {"version": "11.1.2", "resolved": "http://r.npm.sankuai.com/git-url-parse/download/git-url-parse-11.1.2.tgz", "integrity": "sha1-r/Gol8NsyTaZJwWHvqPby7uV3mc=", "requires": {"git-up": "^4.0.0"}}, "glob": {"version": "7.2.3", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "globals": {"version": "11.12.0", "resolved": "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "globalthis": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz", "integrity": "sha1-dDDtOpddl7+1m8zkH1yruvplEjY=", "dev": true, "requires": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}}, "gopd": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE="}, "graceful-fs": {"version": "4.2.11", "resolved": "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM="}, "growl": {"version": "1.10.5", "resolved": "http://r.npm.sankuai.com/growl/download/growl-1.10.5.tgz", "integrity": "sha1-8nNdwig2dPpnR4sQGBBZNVw2nl4=", "dev": true}, "har-schema": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.1.5", "resolved": "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz", "integrity": "sha1-HwgDufjLIMD6E4It8ezds2veHv0=", "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has-bigints": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz", "integrity": "sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-property-descriptors": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "dev": true, "requires": {"es-define-property": "^1.0.0"}}, "has-proto": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz", "integrity": "sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=", "dev": true, "requires": {"dunder-proto": "^1.0.0"}}, "has-symbols": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg="}, "has-tostringtag": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "requires": {"has-symbols": "^1.0.3"}}, "hasown": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "requires": {"function-bind": "^1.1.2"}}, "he": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/he/download/he-1.1.1.tgz", "integrity": "sha1-k0EP0hsAlzUVH4howvJx80J+I/0=", "dev": true}, "http-signature": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="}, "ignore": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true}, "import-fresh": {"version": "3.3.1", "resolved": "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "indent-string": {"version": "3.2.0", "resolved": "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz", "integrity": "sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok="}, "inflection": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/inflection/download/inflection-1.13.4.tgz", "integrity": "sha1-ZappbE4tpiJbFI16FUxEk2ZjOjI="}, "inflight": {"version": "1.0.6", "resolved": "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "inquirer": {"version": "6.5.2", "resolved": "http://r.npm.sankuai.com/inquirer/download/inquirer-6.5.2.tgz", "integrity": "sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=", "dev": true, "requires": {"ansi-escapes": "^3.2.0", "chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^2.0.0", "lodash": "^4.17.12", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.4.0", "string-width": "^2.1.0", "strip-ansi": "^5.1.0", "through": "^2.3.6"}, "dependencies": {"ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true}, "strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}}}, "internal-slot": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz", "integrity": "sha1-HqyRdilH0vcFa8g42T4TsulgSWE=", "dev": true, "requires": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}}, "ip": {"version": "1.1.9", "resolved": "http://r.npm.sankuai.com/ip/download/ip-1.1.9.tgz", "integrity": "sha1-jfvMmadU0H9CUxC4aplUaxFR45Y="}, "is-array-buffer": {"version": "3.0.5", "resolved": "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz", "integrity": "sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}}, "is-arrayish": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "integrity": "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="}, "is-async-function": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.1.tgz", "integrity": "sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=", "dev": true, "requires": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}}, "is-bigint": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz", "integrity": "sha1-3aejRF31ekJYPbQihoLrp8QXBnI=", "dev": true, "requires": {"has-bigints": "^1.0.2"}}, "is-bluebird": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-bluebird/download/is-bluebird-1.0.2.tgz", "integrity": "sha1-CWQ5Bg9KpBGr7hkUOoTWpVNG1uI="}, "is-boolean-object": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.2.tgz", "integrity": "sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=", "dev": true, "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-callable": {"version": "1.2.7", "resolved": "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "dev": true}, "is-core-module": {"version": "2.16.1", "resolved": "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "requires": {"hasown": "^2.0.2"}}, "is-data-view": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz", "integrity": "sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=", "dev": true, "requires": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}}, "is-date-object": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz", "integrity": "sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=", "dev": true, "requires": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}}, "is-extglob": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-finalizationregistry": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz", "integrity": "sha1-7v3NxslN3QZ02chYh7+T+USpfJA=", "dev": true, "requires": {"call-bound": "^1.0.3"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "is-generator-function": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "dev": true, "requires": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}}, "is-glob": {"version": "4.0.3", "resolved": "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-map": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz", "integrity": "sha1-7elrf+HicLPERl46RlZYdkkm1i4=", "dev": true}, "is-number-object": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz", "integrity": "sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=", "dev": true, "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-property": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/is-property/download/is-property-1.0.2.tgz", "integrity": "sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ="}, "is-regex": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "dev": true, "requires": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "is-set": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz", "integrity": "sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=", "dev": true}, "is-shared-array-buffer": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz", "integrity": "sha1-m2eES9m38ka6BwjDqT40Jpx3T28=", "dev": true, "requires": {"call-bound": "^1.0.3"}}, "is-ssh": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/is-ssh/download/is-ssh-1.4.1.tgz", "integrity": "sha1-dt4c2+j5KouQXRoXK2vAlwTCA5Y=", "requires": {"protocols": "^2.0.1"}}, "is-stream": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="}, "is-string": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz", "integrity": "sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=", "dev": true, "requires": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}}, "is-symbol": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz", "integrity": "sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=", "dev": true, "requires": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}}, "is-typed-array": {"version": "1.1.15", "resolved": "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "dev": true, "requires": {"which-typed-array": "^1.1.16"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-weakmap": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz", "integrity": "sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=", "dev": true}, "is-weakref": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.1.tgz", "integrity": "sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=", "dev": true, "requires": {"call-bound": "^1.0.3"}}, "is-weakset": {"version": "2.0.4", "resolved": "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz", "integrity": "sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=", "dev": true, "requires": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}}, "isarray": {"version": "2.0.5", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isstream": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "joycon": {"version": "2.2.5", "resolved": "http://r.npm.sankuai.com/joycon/download/joycon-2.2.5.tgz", "integrity": "sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU="}, "js-tokens": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "js-yaml": {"version": "3.14.1", "resolved": "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="}, "json-schema": {"version": "0.4.0", "resolved": "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz", "integrity": "sha1-995M9u+rg4666zI2R0y7paGTCrU="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "json5": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz", "integrity": "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=", "dev": true, "requires": {"minimist": "^1.2.0"}}, "jsonfile": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "jsprim": {"version": "1.4.2", "resolved": "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz", "integrity": "sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "kuler": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/kuler/download/kuler-2.0.0.tgz", "integrity": "sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM="}, "lazystream": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/lazystream/download/lazystream-1.0.1.tgz", "integrity": "sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=", "requires": {"readable-stream": "^2.0.5"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "readable-stream": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "levn": {"version": "0.3.0", "resolved": "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "lodash": {"version": "4.17.21", "resolved": "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.defaults": {"version": "4.2.0", "resolved": "http://r.npm.sankuai.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz", "integrity": "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw="}, "lodash.difference": {"version": "4.5.0", "resolved": "http://r.npm.sankuai.com/lodash.difference/download/lodash.difference-4.5.0.tgz", "integrity": "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw="}, "lodash.flatten": {"version": "4.4.0", "resolved": "http://r.npm.sankuai.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="}, "lodash.isplainobject": {"version": "4.0.6", "resolved": "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="}, "lodash.union": {"version": "4.6.0", "resolved": "http://r.npm.sankuai.com/lodash.union/download/lodash.union-4.6.0.tgz", "integrity": "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg="}, "log-symbols": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz", "integrity": "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=", "requires": {"chalk": "^2.0.1"}}, "log4js": {"version": "6.9.1", "resolved": "http://r.npm.sankuai.com/log4js/download/log4js-6.9.1.tgz", "integrity": "sha1-q6Wj/054cq40+LTFM3BnU3CeOLY=", "requires": {"date-format": "^4.0.14", "debug": "^4.3.4", "flatted": "^3.2.7", "rfdc": "^1.3.0", "streamroller": "^3.1.5"}, "dependencies": {"flatted": {"version": "3.3.3", "resolved": "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g="}}}, "logform": {"version": "2.7.0", "resolved": "http://r.npm.sankuai.com/logform/download/logform-2.7.0.tgz", "integrity": "sha1-z8qXUo7ykPLhJaCDloBQArLQYNE=", "requires": {"@colors/colors": "1.6.0", "@types/triple-beam": "^1.3.2", "fecha": "^4.2.0", "ms": "^2.1.1", "safe-stable-stringify": "^2.3.1", "triple-beam": "^1.3.0"}}, "long": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/long/download/long-4.0.0.tgz", "integrity": "sha1-mntxz7fTYaGU6lVSQckvdGjVvyg="}, "lru-cache": {"version": "5.1.1", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "requires": {"yallist": "^3.0.2"}, "dependencies": {"yallist": {"version": "3.1.1", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="}}}, "lru.min": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/lru.min/download/lru.min-1.1.2.tgz", "integrity": "sha1-Ac4dcsxQx/r4vR+Anr8F1DMQIes="}, "math-intrinsics": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k="}, "mime-db": {"version": "1.52.0", "resolved": "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="}, "mime-types": {"version": "2.1.35", "resolved": "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="}, "minimatch": {"version": "3.1.2", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw="}, "minimost": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/minimost/download/minimost-1.2.0.tgz", "integrity": "sha1-o3+R1gOV/AAxgNIIyp4DFrzE46I=", "requires": {"@types/minimist": "^1.2.0", "minimist": "^1.2.0"}}, "minipass": {"version": "5.0.0", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz", "integrity": "sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0="}, "minizlib": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "resolved": "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "requires": {"yallist": "^4.0.0"}}}}, "mkdirp": {"version": "0.5.6", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "requires": {"minimist": "^1.2.6"}}, "mocha": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/mocha/download/mocha-5.2.0.tgz", "integrity": "sha1-bYrlCPWRZ/lA8rWzxKYSrlDJCuY=", "dev": true, "requires": {"browser-stdout": "1.3.1", "commander": "2.15.1", "debug": "3.1.0", "diff": "3.5.0", "escape-string-regexp": "1.0.5", "glob": "7.1.2", "growl": "1.10.5", "he": "1.1.1", "minimatch": "3.0.4", "mkdirp": "0.5.1", "supports-color": "5.4.0"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "dev": true, "requires": {"ms": "2.0.0"}}, "glob": {"version": "7.1.2", "resolved": "http://r.npm.sankuai.com/glob/download/glob-7.1.2.tgz", "integrity": "sha1-wZyd+aAocC1nhhI4SmVSQExjbRU=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "minimatch": {"version": "3.0.4", "resolved": "http://r.npm.sankuai.com/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "resolved": "http://r.npm.sankuai.com/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "mkdirp": {"version": "0.5.1", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "dev": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "supports-color": {"version": "5.4.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.4.0.tgz", "integrity": "sha1-HGszdALCE3YF7+GfEP7DkPb6q1Q=", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "moment": {"version": "2.30.1", "resolved": "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4="}, "moment-timezone": {"version": "0.5.48", "resolved": "http://r.npm.sankuai.com/moment-timezone/download/moment-timezone-0.5.48.tgz", "integrity": "sha1-ERcnuydHNKUYrhVLXKWJKD8FiWc=", "requires": {"moment": "^2.29.4"}}, "ms": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}, "mute-stream": {"version": "0.0.7", "resolved": "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=", "dev": true}, "mysql2": {"version": "3.14.1", "resolved": "http://r.npm.sankuai.com/mysql2/download/mysql2-3.14.1.tgz", "integrity": "sha1-d4YWCr8Ib9J54CU+FuNMBbSrOz4=", "requires": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "long": {"version": "5.3.2", "resolved": "http://r.npm.sankuai.com/long/download/long-5.3.2.tgz", "integrity": "sha1-HYRGMJWZkmLX17f4v9SozFUWf4M="}}}, "named-placeholders": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/named-placeholders/download/named-placeholders-1.1.3.tgz", "integrity": "sha1-31lXmaNmVNpV3aYVK6ehN60dk1E=", "requires": {"lru-cache": "^7.14.1"}, "dependencies": {"lru-cache": {"version": "7.18.3", "resolved": "http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz", "integrity": "sha1-95OJbg/Q6VSlnf3YLwdzgI32qok="}}}, "natural-compare": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node-addon-api": {"version": "3.2.1", "resolved": "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-3.2.1.tgz", "integrity": "sha1-gTJeCiEXeJwBKNq2Xn448HzroWE="}, "node-stream-zip": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz", "integrity": "sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o="}, "normalize-path": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="}, "normalize-url": {"version": "6.1.0", "resolved": "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz", "integrity": "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo="}, "oauth-sign": {"version": "0.9.0", "resolved": "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="}, "object-inspect": {"version": "1.13.4", "resolved": "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM="}, "object-keys": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true}, "object.assign": {"version": "4.1.7", "resolved": "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}}, "object.fromentries": {"version": "2.0.8", "resolved": "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz", "integrity": "sha1-9xldipuXvZXLwZmeqTns0aKwDGU=", "dev": true, "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}}, "object.groupby": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/object.groupby/download/object.groupby-1.0.3.tgz", "integrity": "sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=", "dev": true, "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}}, "object.values": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz", "integrity": "sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}}, "once": {"version": "1.4.0", "resolved": "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "one-time": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/one-time/download/one-time-1.0.0.tgz", "integrity": "sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=", "requires": {"fn.name": "1.x.x"}}, "onetime": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "requires": {"mimic-fn": "^1.0.0"}}, "optionator": {"version": "0.8.3", "resolved": "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz", "integrity": "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "ora": {"version": "3.4.0", "resolved": "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz", "integrity": "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=", "requires": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "dependencies": {"ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0="}, "strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "own-keys": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz", "integrity": "sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=", "dev": true, "requires": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}}, "parent-module": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parse-path": {"version": "4.0.4", "resolved": "http://r.npm.sankuai.com/parse-path/download/parse-path-4.0.4.tgz", "integrity": "sha1-S/Qk5rdD+wgIMfA7U2r5/EPw/+o=", "requires": {"is-ssh": "^1.3.0", "protocols": "^1.4.0", "qs": "^6.9.4", "query-string": "^6.13.8"}, "dependencies": {"protocols": {"version": "1.4.8", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-1.4.8.tgz", "integrity": "sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg="}, "qs": {"version": "6.14.0", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "requires": {"side-channel": "^1.1.0"}}}}, "parse-url": {"version": "6.0.5", "resolved": "http://r.npm.sankuai.com/parse-url/download/parse-url-6.0.5.tgz", "integrity": "sha1-Ssq4mCzvGEag+GdfpobO8ksvb5s=", "requires": {"is-ssh": "^1.3.0", "normalize-url": "^6.1.0", "parse-path": "^4.0.0", "protocols": "^1.4.0"}, "dependencies": {"protocols": {"version": "1.4.8", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-1.4.8.tgz", "integrity": "sha1-SO6i2PWNlkSkoyyq5dXbKQoHXOg="}}}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/path-is-inside/download/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "path-parse": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true}, "performance-now": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pg-connection-string": {"version": "2.9.0", "resolved": "http://r.npm.sankuai.com/pg-connection-string/download/pg-connection-string-2.9.0.tgz", "integrity": "sha1-914GWR/dQux2Nv4sagP+vu2+yb8="}, "picocolors": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true}, "possible-typed-array-names": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "dev": true}, "prelude-ls": {"version": "1.1.2", "resolved": "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "progress": {"version": "2.0.3", "resolved": "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "dev": true}, "properties-parser": {"version": "0.3.1", "resolved": "http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz", "integrity": "sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=", "requires": {"string.prototype.codepointat": "^0.2.0"}}, "protocols": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/protocols/download/protocols-2.0.2.tgz", "integrity": "sha1-gi6Pzcs99TVlOLPpG/2JCwZ/0KQ="}, "proxy-from-env": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I="}, "psl": {"version": "1.15.0", "resolved": "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz", "integrity": "sha1-vazjGJbx2XzsannoIkiYzpPZdMY=", "requires": {"punycode": "^2.3.1"}}, "punycode": {"version": "2.3.1", "resolved": "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU="}, "qs": {"version": "6.5.3", "resolved": "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz", "integrity": "sha1-Ou7/yRln7241wOSI70b7KWq3aq0="}, "query-string": {"version": "6.14.1", "resolved": "http://r.npm.sankuai.com/query-string/download/query-string-6.14.1.tgz", "integrity": "sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=", "requires": {"decode-uri-component": "^0.2.0", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}}, "readable-stream": {"version": "3.6.2", "resolved": "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "redent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz", "integrity": "sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=", "requires": {"indent-string": "^3.0.0", "strip-indent": "^2.0.0"}}, "reflect.getprototypeof": {"version": "1.0.10", "resolved": "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz", "integrity": "sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=", "dev": true, "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}}, "regexp.prototype.flags": {"version": "1.5.4", "resolved": "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=", "dev": true, "requires": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}}, "regexpp": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/regexpp/download/regexpp-2.0.1.tgz", "integrity": "sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=", "dev": true}, "request": {"version": "2.88.2", "resolved": "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz", "integrity": "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "resolve": {"version": "1.22.10", "resolved": "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "requires": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true}, "restore-cursor": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "retry-as-promised": {"version": "7.1.1", "resolved": "http://r.npm.sankuai.com/retry-as-promised/download/retry-as-promised-7.1.1.tgz", "integrity": "sha1-NiYkbwTBlB/xDOvPo98Fd/2Kstc="}, "rfdc": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz", "integrity": "sha1-d492xPtzHZNBTo+SX77PZMzn9so="}, "rimraf": {"version": "2.7.1", "resolved": "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "requires": {"glob": "^7.1.3"}}, "run-async": {"version": "2.4.1", "resolved": "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=", "dev": true}, "rxjs": {"version": "6.6.7", "resolved": "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz", "integrity": "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=", "dev": true, "requires": {"tslib": "^1.9.0"}}, "safe-array-concat": {"version": "1.1.3", "resolved": "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz", "integrity": "sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}, "safe-push-apply": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz", "integrity": "sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=", "dev": true, "requires": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}}, "safe-regex-test": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "dev": true, "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}}, "safe-stable-stringify": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/safe-stable-stringify/download/safe-stable-stringify-2.5.0.tgz", "integrity": "sha1-TKL444XygxxDKnGbEIo7969Cod0="}, "safer-buffer": {"version": "2.1.2", "resolved": "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "sax": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/sax/download/sax-1.4.1.tgz", "integrity": "sha1-RMyJiDd/EmME07P8EBDHM7kp7w8="}, "semver": {"version": "6.3.1", "resolved": "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ="}, "seq-queue": {"version": "0.0.5", "resolved": "http://r.npm.sankuai.com/seq-queue/download/seq-queue-0.0.5.tgz", "integrity": "sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4="}, "sequelize": {"version": "6.37.7", "resolved": "http://r.npm.sankuai.com/sequelize/download/sequelize-6.37.7.tgz", "integrity": "sha1-Vab4VVrnbB+9S852sqxfzAoebrY=", "requires": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.6", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.1", "retry-as-promised": "^7.0.4", "semver": "^7.5.4", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}, "dependencies": {"semver": {"version": "7.7.2", "resolved": "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g="}, "sequelize-pool": {"version": "7.1.0", "resolved": "http://r.npm.sankuai.com/sequelize-pool/download/sequelize-pool-7.1.0.tgz", "integrity": "sha1-IQs5GvQAJ2L4IxiP1uz8dBMCB2g="}, "uuid": {"version": "8.3.2", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}}}, "sequelize-pool": {"version": "2.3.0", "resolved": "http://r.npm.sankuai.com/sequelize-pool/download/sequelize-pool-2.3.0.tgz", "integrity": "sha1-ZPH+h0QigXLEdPUwYEthM75kmT0="}, "set-function-length": {"version": "1.2.2", "resolved": "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "dev": true, "requires": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}}, "set-function-name": {"version": "2.0.2", "resolved": "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz", "integrity": "sha1-FqcFxaDcL15jjKltiozU4cK5CYU=", "dev": true, "requires": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}}, "set-proto": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz", "integrity": "sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=", "dev": true, "requires": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}}, "shebang-command": {"version": "1.2.0", "resolved": "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "shimmer": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/shimmer/download/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc="}, "side-channel": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "requires": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}}, "side-channel-list": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "requires": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}}, "side-channel-map": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}}, "side-channel-weakmap": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "requires": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}}, "signal-exit": {"version": "3.0.7", "resolved": "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk="}, "simple-swizzle": {"version": "0.2.2", "resolved": "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "requires": {"is-arrayish": "^0.3.1"}}, "slice-ansi": {"version": "2.1.0", "resolved": "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz", "integrity": "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=", "dev": true, "requires": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}}, "split-on-first": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/split-on-first/download/split-on-first-1.1.0.tgz", "integrity": "sha1-9hCv7uOxK84dDDBCXnY5i3gkml8="}, "sprintf-js": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "sqlstring": {"version": "2.3.3", "resolved": "http://r.npm.sankuai.com/sqlstring/download/sqlstring-2.3.3.tgz", "integrity": "sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw="}, "sshpk": {"version": "1.18.0", "resolved": "http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz", "integrity": "sha1-FmPlXN301oi4aka3fw1f42OroCg=", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "stack-trace": {"version": "0.0.10", "resolved": "http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA="}, "streamroller": {"version": "3.1.5", "resolved": "http://r.npm.sankuai.com/streamroller/download/streamroller-3.1.5.tgz", "integrity": "sha1-EmMYIymkXe8f+u9Y0xsV0T0u5/8=", "requires": {"date-format": "^4.0.14", "debug": "^4.3.4", "fs-extra": "^8.1.0"}}, "strict-uri-encode": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz", "integrity": "sha1-ucczDHBChi9rFC3CdLvMWGbONUY="}, "string_decoder": {"version": "1.3.0", "resolved": "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "requires": {"safe-buffer": "~5.2.0"}}, "string-width": {"version": "2.1.1", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "string.prototype.codepointat": {"version": "0.2.1", "resolved": "http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz", "integrity": "sha1-AErUTIr8cnUnsQjNRitNlxzUabw="}, "string.prototype.trim": {"version": "1.2.10", "resolved": "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz", "integrity": "sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}}, "string.prototype.trimend": {"version": "1.0.9", "resolved": "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz", "integrity": "sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=", "dev": true, "requires": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}}, "string.prototype.trimstart": {"version": "1.0.8", "resolved": "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz", "integrity": "sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=", "dev": true, "requires": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true}, "strip-indent": {"version": "2.0.0", "resolved": "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g="}, "strip-json-comments": {"version": "2.0.1", "resolved": "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true}, "table": {"version": "5.4.6", "resolved": "http://r.npm.sankuai.com/table/download/table-5.4.6.tgz", "integrity": "sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=", "dev": true, "requires": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}, "dependencies": {"ansi-regex": {"version": "4.1.1", "resolved": "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true}, "string-width": {"version": "3.1.0", "resolved": "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}}}, "tar": {"version": "6.2.1", "resolved": "http://r.npm.sankuai.com/tar/download/tar-6.2.1.tgz", "integrity": "sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=", "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dependencies": {"mkdirp": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34="}}}, "tar-stream": {"version": "2.2.0", "resolved": "http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz", "integrity": "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=", "requires": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}, "text-hex": {"version": "1.0.0", "resolved": "http://r.npm.sankuai.com/text-hex/download/text-hex-1.0.0.tgz", "integrity": "sha1-adycGxdEbueakr9biEu0uRJ1BvU="}, "text-table": {"version": "0.2.0", "resolved": "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="}, "through": {"version": "2.3.8", "resolved": "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=", "dev": true}, "tmp": {"version": "0.0.33", "resolved": "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "dev": true, "requires": {"os-tmpdir": "~1.0.2"}}, "toposort-class": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/toposort-class/download/toposort-class-1.0.1.tgz", "integrity": "sha1-f/0feMi+KMO6Rc1OGj9e4ZO9mYg="}, "tough-cookie": {"version": "2.5.0", "resolved": "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "triple-beam": {"version": "1.4.1", "resolved": "http://r.npm.sankuai.com/triple-beam/download/triple-beam-1.4.1.tgz", "integrity": "sha1-b95wJx3G5dc8oMOyTi2Sr7dEGYQ="}, "tsconfig-paths": {"version": "3.15.0", "resolved": "http://r.npm.sankuai.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz", "integrity": "sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=", "dev": true, "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "tslib": {"version": "1.14.1", "resolved": "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=", "dev": true}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="}, "type-check": {"version": "0.3.2", "resolved": "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}, "typed-array-buffer": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz", "integrity": "sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=", "dev": true, "requires": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}}, "typed-array-byte-length": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz", "integrity": "sha1-hAegT314aE89JSqhoUPSt3tBYM4=", "dev": true, "requires": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}}, "typed-array-byte-offset": {"version": "1.0.4", "resolved": "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz", "integrity": "sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=", "dev": true, "requires": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}}, "typed-array-length": {"version": "1.0.7", "resolved": "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz", "integrity": "sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=", "dev": true, "requires": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}}, "unbox-primitive": {"version": "1.1.0", "resolved": "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz", "integrity": "sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=", "dev": true, "requires": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}}, "undici-types": {"version": "6.21.0", "resolved": "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss="}, "universalify": {"version": "0.1.2", "resolved": "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="}, "uri-js": {"version": "4.4.1", "resolved": "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "requires": {"punycode": "^2.1.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.4.0", "resolved": "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="}, "validator": {"version": "13.15.0", "resolved": "http://r.npm.sankuai.com/validator/download/validator-13.15.0.tgz", "integrity": "sha1-LcfOBX51E6VVhRCe7CmyyOjBrv0="}, "verror": {"version": "1.10.0", "resolved": "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "wcwidth": {"version": "1.0.1", "resolved": "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "requires": {"defaults": "^1.0.3"}}, "which": {"version": "1.3.1", "resolved": "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.1.1", "resolved": "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz", "integrity": "sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=", "dev": true, "requires": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}}, "which-builtin-type": {"version": "1.2.1", "resolved": "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz", "integrity": "sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=", "dev": true, "requires": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}}, "which-collection": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz", "integrity": "sha1-Yn73YkOSChB+fOjpYZHevksWwqA=", "dev": true, "requires": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}}, "which-typed-array": {"version": "1.1.19", "resolved": "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "dev": true, "requires": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}}, "winston": {"version": "3.12.0", "resolved": "http://r.npm.sankuai.com/winston/download/winston-3.12.0.tgz", "integrity": "sha1-pdllpB09wxvlQI+MZuknlYhGwNA=", "requires": {"@colors/colors": "^1.6.0", "@dabh/diagnostics": "^2.0.2", "async": "^3.2.3", "is-stream": "^2.0.0", "logform": "^2.4.0", "one-time": "^1.0.0", "readable-stream": "^3.4.0", "safe-stable-stringify": "^2.3.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.7.0"}, "dependencies": {"async": {"version": "3.2.6", "resolved": "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4="}}}, "winston-transport": {"version": "4.9.0", "resolved": "http://r.npm.sankuai.com/winston-transport/download/winston-transport-4.9.0.tgz", "integrity": "sha1-O7o0XeECl2VOpvM1GUJFYAA7O/k=", "requires": {"logform": "^2.7.0", "readable-stream": "^3.6.2", "triple-beam": "^1.3.0"}}, "wkx": {"version": "0.5.0", "resolved": "http://r.npm.sankuai.com/wkx/download/wkx-0.5.0.tgz", "integrity": "sha1-xsNwGaz0DlF8xrlGV6JaPUqjPow=", "requires": {"@types/node": "*"}}, "word-wrap": {"version": "1.2.5", "resolved": "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true}, "wrappy": {"version": "1.0.2", "resolved": "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "1.0.3", "resolved": "http://r.npm.sankuai.com/write/download/write-1.0.3.tgz", "integrity": "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=", "dev": true, "requires": {"mkdirp": "^0.5.1"}}, "xml2js": {"version": "0.4.23", "resolved": "http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz", "integrity": "sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=", "requires": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}}, "xmlbuilder": {"version": "11.0.1", "resolved": "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz", "integrity": "sha1-vpuuHIoEbnazESdyY0fQrXACvrM="}, "yallist": {"version": "4.0.0", "resolved": "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="}, "zip-stream": {"version": "2.1.3", "resolved": "http://r.npm.sankuai.com/zip-stream/download/zip-stream-2.1.3.tgz", "integrity": "sha1-JsxL25NkGoWQ3QcRLh93rxdYhls=", "requires": {"archiver-utils": "^2.1.0", "compress-commons": "^2.1.1", "readable-stream": "^3.4.0"}}}}