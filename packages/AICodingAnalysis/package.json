{"name": "nest-example", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "mocha --recursive test/*-spec.js", "lint": "eslint --ext .js 'index.js'", "start": "export INF_BOM_ENV=test && nest run --watch", "build": "echo 'build'"}, "repository": {"type": "git"}, "author": "{{ author }}", "license": "ISC", "dependencies": {"@bfe/zebra-proxy-sequelize": "^1.0.0", "@fdfe/era-cloud-uploader": "^0.11.2", "@nibfe/talos-public-api": "^1.2.15", "axios": "^1.9.0", "mysql2": "^3.14.1", "sequelize": "^6.37.7"}, "devDependencies": {"eslint": "^5.11.1", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "mocha": "^5.0.0"}}