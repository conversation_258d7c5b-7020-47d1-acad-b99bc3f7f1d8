/**
 * Talos流水线配置
 * 使用方法：修改此文件中的参数后部署
 * 或设置环境变量TALOS_APP_ID, TALOS_CONFIG_ID, TALOS_OP_MIS覆盖这些默认值
 */

module.exports = {
  // 应用ID，必填，在Talos平台获取
  TALOS_APP_ID: 23037,
  
  // 发布模板ID，必填，在发布模板页面获取
  TALOS_CONFIG_ID: 173627,
  
  // 默认发布人MIS账号
  TALOS_OP_MIS: 'yaoyan03',
  
  // Talos API客户端ID，必填
  CLIENT_ID: 'GFE-AICodingAnalysis',
  
  // Talos API客户端密钥，必填
  CLIENT_SECRET: '011c2fbaa473448286660e805da7e8b3',
  
  // 模拟模式配置
  MOCK: {
    // 是否启用模拟模式，启用后不会实际调用Talos API，而是返回模拟数据
    ENABLED: false, // 关闭模拟模式，使用实际API
    // 模拟的流水线ID
    FLOW_ID: 12345,
    // 模拟的响应消息
    MESSAGE: '模拟模式: 流水线已触发'
  }
}; 