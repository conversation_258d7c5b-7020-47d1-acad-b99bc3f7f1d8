/**
 * 测试所有函数的CORS配置
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysis, AICodingAnalysisResult, AICodingDashboard } = require('./index.js');

async function testAllCORS() {
  console.log('🧪 测试所有函数的CORS配置...\n');

  const tests = [
    {
      name: '1. AICodingAnalysis - OPTIONS 预检请求',
      func: AICodingAnalysis,
      event: {
        httpMethod: 'OPTIONS',
        extensions: {
          request: {
            method: 'OPTIONS',
            url: '/',
            path: '/'
          }
        },
        headers: {
          'Origin': 'https://example.com',
          'Access-Control-Request-Method': 'POST'
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '2. AICodingAnalysis - POST 请求',
      func: AICodingAnalysis,
      event: {
        httpMethod: 'POST',
        extensions: {
          request: {
            method: 'POST',
            url: '/',
            path: '/',
            body: {
              merge_request_id: '12345',
              user_name: 'test_user',
              title: 'Test PR',
              source: { ssh_url: 'ssh://***************/test/repo.git' },
              from: 'refs/heads/feature',
              to: 'refs/heads/master'
            }
          }
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '3. AICodingAnalysisResult - OPTIONS 预检请求',
      func: AICodingAnalysisResult,
      event: {
        httpMethod: 'OPTIONS',
        extensions: {
          request: {
            method: 'OPTIONS',
            url: '/',
            path: '/'
          }
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '4. AICodingAnalysisResult - GET 请求',
      func: AICodingAnalysisResult,
      event: {
        httpMethod: 'GET',
        extensions: {
          request: {
            method: 'GET',
            url: '/?page=1&pageSize=10',
            path: '/'
          }
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '5. AICodingDashboard - OPTIONS 预检请求',
      func: AICodingDashboard,
      event: {
        httpMethod: 'OPTIONS',
        extensions: {
          request: {
            method: 'OPTIONS',
            url: '/',
            path: '/'
          }
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '6. AICodingDashboard - GET 请求',
      func: AICodingDashboard,
      event: {
        httpMethod: 'GET',
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=7d',
            path: '/'
          }
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('函数:', test.func.name);
    console.log('请求方法:', test.event.httpMethod || test.event.extensions?.request?.method || 'GET');

    try {
      const result = await test.func(test.event, {});

      console.log('响应状态码:', result.statusCode);
      console.log('响应成功:', result.body?.success !== undefined ? result.body.success : '无body');

      // 检查状态码
      if (result.statusCode === test.expectedStatusCode) {
        console.log('✅ 状态码正确');
      } else {
        console.log(`❌ 状态码错误，期望: ${test.expectedStatusCode}, 实际: ${result.statusCode}`);
      }

      // 检查 CORS 头
      if (test.checkCorsHeaders && result.headers) {
        console.log('\nCORS 头检查:');
        
        const corsHeaders = [
          'Access-Control-Allow-Origin',
          'Access-Control-Allow-Methods',
          'Access-Control-Allow-Headers',
          'Access-Control-Max-Age'
        ];

        let corsHeadersPresent = 0;
        corsHeaders.forEach(header => {
          if (result.headers[header]) {
            console.log(`✅ ${header}: ${result.headers[header]}`);
            corsHeadersPresent++;
          } else {
            console.log(`❌ ${header}: 缺失`);
          }
        });

        if (corsHeadersPresent >= 3) {
          console.log('✅ CORS 头配置正确');
          passedTests++;
        } else {
          console.log('❌ CORS 头配置不完整');
        }
      } else {
        console.log('✅ 请求处理正常');
        passedTests++;
      }

      // 对于 OPTIONS 请求，检查是否返回空 body
      if (test.event.httpMethod === 'OPTIONS') {
        if (result.body === '' || result.body === null || result.body === undefined) {
          console.log('✅ OPTIONS 请求返回空 body');
        } else {
          console.log('❌ OPTIONS 请求应该返回空 body');
        }
      }

    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有函数CORS配置测试成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 部分函数CORS配置测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testAllCORS().then((result) => {
    if (result.success) {
      console.log('\n✅ 所有函数CORS配置验证成功！');
      console.log('\n🌐 CORS支持总结:');
      console.log('- ✅ AICodingAnalysis: OPTIONS + POST 请求支持');
      console.log('- ✅ AICodingAnalysisResult: OPTIONS + GET/POST 请求支持');
      console.log('- ✅ AICodingDashboard: OPTIONS + GET 请求支持');
      console.log('- ✅ 所有响应都包含完整的CORS头');
      console.log('- ✅ 支持任意域名跨域访问');
      process.exit(0);
    } else {
      console.log('\n❌ CORS配置验证失败，需要进一步修复！');
      process.exit(1);
    }
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testAllCORS };
