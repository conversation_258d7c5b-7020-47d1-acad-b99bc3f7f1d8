/**
 * 测试清理后的模型
 */

// 模拟生产环境
process.env.USER = 'root';

async function testCleanModels() {
  console.log('🧪 测试清理后的模型...\n');

  try {
    // 测试清理后的模型
    const modelsModule = require('./models/AICodingAnalysisModels-clean.js');
    
    console.log('1. 初始化数据库...');
    await modelsModule.initializeDatabase();
    console.log('数据库状态:', modelsModule.getDatabaseStatus());
    
    console.log('\n2. 获取模型对象...');
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;
    
    console.log('AiCodeAnalysisReports 类型:', typeof AiCodeAnalysisReports);
    console.log('sequelize.query 类型:', typeof AiCodeAnalysisReports.sequelize.query);
    
    console.log('\n3. 测试查询...');
    const queryResult = await AiCodeAnalysisReports.findAndCountAll({ limit: 3 });
    console.log('查询结果总数:', queryResult.count);
    console.log('返回记录数:', queryResult.rows.length);
    
    console.log('\n4. 测试插入...');
    const testData = {
      pr_global_id: `CLEAN_TEST_${Date.now()}`,
      pr_title: '清理版本测试',
      git_repository: 'https://git.example.com/clean-test.git',
      git_branch: 'clean-test',
      mis_number: 'clean_test_user',
      detection_method: '清理版本测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    const insertResult = await AiCodeAnalysisReports.create(testData);
    console.log('插入成功，ID:', insertResult.id);
    console.log('插入成功，PR ID:', insertResult.pr_global_id);
    
    console.log('\n5. 测试关联查询...');
    const associationResult = await AiCodeAnalysisReports.findAndCountAll({
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      limit: 2
    });
    console.log('关联查询成功，记录数:', associationResult.count);

    return { success: true };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  testCleanModels().then((result) => {
    if (result.success) {
      console.log('\n✅ 清理版本测试成功！');
      console.log('\n📝 清理完成：');
      console.log('- ❌ 删除了所有模拟数据库相关代码');
      console.log('- ❌ 删除了本地环境检测逻辑');
      console.log('- ❌ 删除了模拟模型创建函数');
      console.log('- ✅ 保留了真实数据库连接');
      console.log('- ✅ 保留了真实模型定义');
      console.log('- ✅ 保留了所有业务功能');
      
      console.log('\n🚀 可以更新 index.js 使用清理版本！');
    } else {
      console.log('\n❌ 清理版本测试失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testCleanModels };
