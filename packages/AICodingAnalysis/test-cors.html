<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI代码分析 API 跨域测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>AI代码分析 API 跨域测试</h1>
    
    <div class="test-section">
        <h2>API 配置</h2>
        <label for="apiUrl">API URL:</label>
        <input type="text" id="apiUrl" value="https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c" style="width: 100%; padding: 5px; margin: 5px 0;">
    </div>

    <div class="test-section">
        <h2>GET 请求测试</h2>
        <button onclick="testGet()">测试 GET 请求</button>
        <button onclick="testGetWithParams()">测试 GET 请求（带参数）</button>
        <div id="getResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>POST 请求测试</h2>
        <button onclick="testPost()">测试 POST 请求</button>
        <div id="postResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>OPTIONS 预检请求测试</h2>
        <button onclick="testOptions()">测试 OPTIONS 请求</button>
        <div id="optionsResult" class="result"></div>
    </div>

    <script>
        function getApiUrl() {
            return document.getElementById('apiUrl').value;
        }

        function displayResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        async function testGet() {
            try {
                const response = await fetch(getApiUrl() + '?page=1&pageSize=5', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                displayResult('getResult', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                }, response.ok);
            } catch (error) {
                displayResult('getResult', {
                    error: error.message
                }, false);
            }
        }

        async function testGetWithParams() {
            try {
                const params = new URLSearchParams({
                    page: '1',
                    pageSize: '10',
                    misNumber: 'test',
                    startTime: '2024-01-01T00:00:00.000Z',
                    endTime: '2024-12-31T23:59:59.999Z'
                });
                
                const response = await fetch(getApiUrl() + '?' + params, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                displayResult('getResult', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                }, response.ok);
            } catch (error) {
                displayResult('getResult', {
                    error: error.message
                }, false);
            }
        }

        async function testPost() {
            try {
                const testData = {
                    pr_global_id: "TEST-" + Date.now(),
                    pr_title: "跨域测试PR",
                    git_repository: "https://git.example.com/cors-test.git",
                    git_branch: "feature/cors-test",
                    mis_number: "corstest",
                    detection_method: "CORS_TEST",
                    analysis_timestamp: new Date().toISOString(),
                    total_files: 1,
                    total_lines: 100,
                    changed_lines: 50,
                    ai_generated_lines: 25,
                    ai_code_ratio: 0.25,
                    file_details: [
                        {
                            file_name: "test/cors.js",
                            file_type: "js",
                            total_lines: 100,
                            changed_lines: 50,
                            ai_matched_lines: 25,
                            ai_code_ratio: 0.25,
                            detection_method: "CORS_TEST"
                        }
                    ]
                };

                const response = await fetch(getApiUrl(), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                displayResult('postResult', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                }, response.ok);
            } catch (error) {
                displayResult('postResult', {
                    error: error.message
                }, false);
            }
        }

        async function testOptions() {
            try {
                const response = await fetch(getApiUrl(), {
                    method: 'OPTIONS',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                displayResult('optionsResult', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                }, response.ok);
            } catch (error) {
                displayResult('optionsResult', {
                    error: error.message
                }, false);
            }
        }
    </script>
</body>
</html>
