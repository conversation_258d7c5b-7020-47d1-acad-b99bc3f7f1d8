/**
 * 测试 CORS 跨域配置
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingDashboard } = require('./index.js');

async function testCORS() {
  console.log('🧪 测试 CORS 跨域配置...\n');

  const tests = [
    {
      name: '1. OPTIONS 预检请求',
      event: {
        httpMethod: 'OPTIONS',
        extensions: {
          request: {
            method: 'OPTIONS',
            url: '/',
            path: '/'
          }
        },
        headers: {
          'Origin': 'https://example.com',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '2. GET 请求 - 验证 CORS 头',
      event: {
        httpMethod: 'GET',
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=7d',
            path: '/'
          }
        },
        headers: {
          'Origin': 'https://example.com'
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    },
    {
      name: '3. 跨域请求模拟',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=30d',
            path: '/'
          }
        },
        headers: {
          'Origin': 'https://ai-dashboard.example.com',
          'Referer': 'https://ai-dashboard.example.com/dashboard'
        }
      },
      expectedStatusCode: 200,
      checkCorsHeaders: true
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('请求方法:', test.event.httpMethod || test.event.extensions?.request?.method || 'GET');
    console.log('Origin:', test.event.headers?.Origin || '无');

    try {
      const result = await AICodingDashboard(test.event, {});

      console.log('响应状态码:', result.statusCode);
      console.log('响应成功:', result.body?.success !== undefined ? result.body.success : '无body');

      // 检查状态码
      if (result.statusCode === test.expectedStatusCode) {
        console.log('✅ 状态码正确');
      } else {
        console.log(`❌ 状态码错误，期望: ${test.expectedStatusCode}, 实际: ${result.statusCode}`);
      }

      // 检查 CORS 头
      if (test.checkCorsHeaders && result.headers) {
        console.log('\nCORS 头检查:');
        
        const corsHeaders = [
          'Access-Control-Allow-Origin',
          'Access-Control-Allow-Methods',
          'Access-Control-Allow-Headers',
          'Access-Control-Max-Age'
        ];

        let corsHeadersPresent = 0;
        corsHeaders.forEach(header => {
          if (result.headers[header]) {
            console.log(`✅ ${header}: ${result.headers[header]}`);
            corsHeadersPresent++;
          } else {
            console.log(`❌ ${header}: 缺失`);
          }
        });

        if (corsHeadersPresent >= 3) {
          console.log('✅ CORS 头配置正确');
          passedTests++;
        } else {
          console.log('❌ CORS 头配置不完整');
        }
      } else {
        console.log('✅ 请求处理正常');
        passedTests++;
      }

      // 对于 OPTIONS 请求，检查是否返回空 body
      if (test.event.httpMethod === 'OPTIONS') {
        if (result.body === '' || result.body === null || result.body === undefined) {
          console.log('✅ OPTIONS 请求返回空 body');
        } else {
          console.log('❌ OPTIONS 请求应该返回空 body');
        }
      }

      // 对于 GET 请求，检查是否有数据
      if ((test.event.httpMethod === 'GET' || !test.event.httpMethod) && result.body?.success) {
        if (result.body.data) {
          console.log('✅ GET 请求返回数据');
          console.log('数据结构:', Object.keys(result.body.data));
        } else {
          console.log('❌ GET 请求缺少数据');
        }
      }

    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 CORS 跨域配置测试成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ CORS 跨域配置测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

// 额外测试：验证 getCorsHeaders 函数
function testGetCorsHeaders() {
  console.log('🔧 测试 getCorsHeaders 函数...\n');
  
  try {
    // 需要从 index.js 中导入 getCorsHeaders 函数
    // 由于它不是导出的，我们通过其他方式测试
    
    // 创建一个简单的 OPTIONS 请求来测试
    const testEvent = {
      httpMethod: 'OPTIONS',
      extensions: {
        request: {
          method: 'OPTIONS',
          url: '/',
          path: '/'
        }
      }
    };
    
    return AICodingDashboard(testEvent, {}).then(result => {
      console.log('CORS 头测试结果:');
      if (result.headers) {
        Object.keys(result.headers).forEach(key => {
          if (key.toLowerCase().includes('access-control')) {
            console.log(`${key}: ${result.headers[key]}`);
          }
        });
        console.log('✅ getCorsHeaders 函数工作正常');
        return true;
      } else {
        console.log('❌ 没有找到 CORS 头');
        return false;
      }
    });
  } catch (error) {
    console.log('❌ getCorsHeaders 测试失败:', error.message);
    return false;
  }
}

if (require.main === module) {
  Promise.all([
    testCORS(),
    testGetCorsHeaders()
  ]).then(([corsResult, headersResult]) => {
    if (corsResult.success && headersResult) {
      console.log('\n✅ CORS 配置验证成功！可以部署了！');
      console.log('\n🌐 跨域访问支持:');
      console.log('- ✅ OPTIONS 预检请求');
      console.log('- ✅ GET 请求 CORS 头');
      console.log('- ✅ 任意域名访问');
      console.log('- ✅ 常用 HTTP 方法');
      console.log('- ✅ 自定义请求头');
      process.exit(0);
    } else {
      console.log('\n❌ CORS 配置验证失败，需要进一步调试！');
      process.exit(1);
    }
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testCORS };
