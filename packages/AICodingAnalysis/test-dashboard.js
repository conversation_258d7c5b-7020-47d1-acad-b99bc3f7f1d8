/**
 * 测试 AI代码分析大盘功能
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingDashboard } = require('./index.js');

async function testDashboard() {
  console.log('🧪 测试 AI代码分析大盘功能...\n');

  const tests = [
    {
      name: '1. 基础大盘数据获取 (默认30天)',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '2. 指定时间范围 (最近7天)',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=7d',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '3. 自定义时间范围',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=custom&startDate=2025-05-20&endDate=2025-05-26',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '4. 按仓库筛选',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?repository=ai-coding&timeRange=30d',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '5. 按开发者筛选',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?developer=yaoyan&timeRange=30d',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '6. 按文件类型筛选',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?fileType=tsx&timeRange=30d',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '7. 组合筛选条件',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?timeRange=30d&repository=ai-coding&developer=yaoyan&fileType=tsx',
            path: '/'
          }
        }
      },
      expectedSuccess: true
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('请求 URL:', test.event.extensions.request.url);

    try {
      const result = await AICodingDashboard(test.event, {});

      if (result.statusCode === 200 && result.body.success) {
        console.log('✅ 请求成功');
        
        const data = result.body.data;
        
        // 验证数据结构
        console.log('数据结构验证:');
        console.log(`- overview: ${data.overview ? '✅' : '❌'}`);
        console.log(`- trends: ${data.trends ? '✅' : '❌'}`);
        console.log(`- rankings: ${data.rankings ? '✅' : '❌'}`);
        console.log(`- distributions: ${data.distributions ? '✅' : '❌'}`);
        console.log(`- recentActivities: ${data.recentActivities ? '✅' : '❌'}`);
        console.log(`- metadata: ${data.metadata ? '✅' : '❌'}`);
        
        // 验证总览数据
        if (data.overview) {
          console.log('总览数据:');
          console.log(`  - 总PR数量: ${data.overview.totalPRs}`);
          console.log(`  - 平均AI占比: ${data.overview.avgAIRatio}%`);
          console.log(`  - 活跃仓库数: ${data.overview.activeRepos}`);
          console.log(`  - 活跃开发者数: ${data.overview.activeDevelopers}`);
          console.log(`  - PR增长率: ${data.overview.trends?.prGrowth}%`);
          console.log(`  - AI占比增长率: ${data.overview.trends?.aiRatioGrowth}%`);
        }
        
        // 验证趋势数据
        if (data.trends && data.trends.daily) {
          console.log(`趋势数据: ${data.trends.daily.length} 个数据点`);
          if (data.trends.daily.length > 0) {
            const latest = data.trends.daily[data.trends.daily.length - 1];
            console.log(`  - 最新数据: ${latest.date}, AI占比: ${latest.aiRatio}%, PR数量: ${latest.prCount}`);
          }
        }
        
        // 验证排行榜数据
        if (data.rankings) {
          console.log('排行榜数据:');
          console.log(`  - 仓库排行: ${data.rankings.repositories?.length || 0} 个`);
          console.log(`  - 开发者排行: ${data.rankings.developers?.length || 0} 个`);
          console.log(`  - 文件类型排行: ${data.rankings.fileTypes?.length || 0} 个`);
        }
        
        // 验证分布数据
        if (data.distributions) {
          console.log('分布数据:');
          console.log(`  - AI占比区间: ${data.distributions.aiRatioRanges?.length || 0} 个区间`);
          console.log(`  - 检测方法: ${data.distributions.detectionMethods?.length || 0} 种`);
          console.log(`  - AI来源: ${data.distributions.aiSources?.length || 0} 种`);
        }
        
        // 验证最近活动
        if (data.recentActivities) {
          console.log(`最近活动: ${data.recentActivities.length} 条记录`);
        }
        
        passedTests++;
      } else {
        console.log(`❌ 请求失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
        if (result.body.error) {
          console.log(`错误信息: ${result.body.error}`);
        }
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
      console.error('错误详情:', error.stack);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 AI代码分析大盘功能测试成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ AI代码分析大盘功能测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testDashboard().then((result) => {
    if (result.success) {
      console.log('\n✅ 大盘功能验证成功！可以部署了！');
    } else {
      console.log('\n❌ 大盘功能验证失败，需要进一步调试！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testDashboard };
