/**
 * 测试动态模型获取
 */

// 模拟生产环境
process.env.USER = 'root';

async function testDynamicModels() {
  console.log('🧪 测试动态模型获取...\n');

  try {
    // 模拟 index.js 中的逻辑
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    
    console.log('1. 初始化前的模型类型:');
    console.log('AiCodeAnalysisReports 类型:', typeof modelsModule.AiCodeAnalysisReports);
    
    // 初始化数据库
    console.log('\n2. 初始化数据库...');
    await modelsModule.initializeDatabase();
    console.log('数据库状态:', modelsModule.getDatabaseStatus());
    
    console.log('\n3. 初始化后的模型类型:');
    console.log('AiCodeAnalysisReports 类型:', typeof modelsModule.AiCodeAnalysisReports);
    
    // 动态获取模型对象（模拟 index.js 中的逻辑）
    console.log('\n4. 动态获取模型对象...');
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;
    
    console.log('动态获取的 AiCodeAnalysisReports 类型:', typeof AiCodeAnalysisReports);
    console.log('sequelize.query 类型:', typeof AiCodeAnalysisReports.sequelize.query);
    
    // 测试插入
    console.log('\n5. 测试插入...');
    const testData = {
      pr_global_id: `DYNAMIC_TEST_${Date.now()}`,
      pr_title: '动态模型测试',
      git_repository: 'https://git.example.com/dynamic-test.git',
      git_branch: 'dynamic-test',
      mis_number: 'dynamic_test',
      detection_method: '动态模型测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    const insertResult = await AiCodeAnalysisReports.create(testData);
    console.log('插入结果 ID:', insertResult.id);
    console.log('插入结果 PR ID:', insertResult.pr_global_id);
    
    // 测试查询
    console.log('\n6. 测试查询...');
    const queryResult = await AiCodeAnalysisReports.findAndCountAll({
      order: [['created_at', 'DESC']],
      limit: 5
    });
    console.log('查询结果总数:', queryResult.count);
    console.log('查询结果记录数:', queryResult.rows.length);
    
    if (queryResult.rows.length > 0) {
      console.log('最新记录:', {
        id: queryResult.rows[0].id,
        pr_global_id: queryResult.rows[0].pr_global_id,
        pr_title: queryResult.rows[0].pr_title
      });
    }
    
    // 测试关联查询
    console.log('\n7. 测试关联查询...');
    const associationResult = await AiCodeAnalysisReports.findAndCountAll({
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      order: [['created_at', 'DESC']],
      limit: 3
    });
    console.log('关联查询结果总数:', associationResult.count);
    console.log('关联查询记录数:', associationResult.rows.length);

    return { success: true };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  testDynamicModels().then((result) => {
    if (result.success) {
      console.log('\n✅ 动态模型测试成功！');
      console.log('\n🚀 现在可以部署了！');
    } else {
      console.log('\n❌ 动态模型测试失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testDynamicModels };
