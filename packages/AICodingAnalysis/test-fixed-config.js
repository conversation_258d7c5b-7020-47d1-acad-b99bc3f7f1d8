/**
 * 测试修复后的配置
 */

// 模拟生产环境
process.env.USER = 'root';

const { AiCodeAnalysisReports, AiCodeAnalysisFileDetails, initializeDatabase, getDatabaseStatus } = require('./models/AICodingAnalysisModels.js');

async function testFixedConfig() {
  console.log('🧪 测试修复后的配置...\n');

  try {
    // 初始化数据库
    console.log('1. 初始化数据库连接...');
    await initializeDatabase();
    
    // 检查数据库状态
    const dbStatus = getDatabaseStatus();
    console.log('2. 数据库状态:', dbStatus);
    
    if (dbStatus === 'MOCK_DATABASE') {
      console.warn('⚠️  警告：当前使用模拟数据库，数据不会真正保存！');
      return { success: false, reason: 'Using mock database' };
    }
    
    console.log('✅ 使用真实数据库');

    // 测试插入一条新数据
    console.log('\n3. 测试插入新数据...');
    const testData = {
      pr_global_id: `TEST_FIXED_${Date.now()}`,
      pr_title: '测试修复后的配置',
      git_repository: 'https://git.example.com/test-fixed.git',
      git_branch: 'test-fixed',
      mis_number: 'test_fixed',
      detection_method: '修复测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    console.log('准备插入数据:', testData.pr_global_id);

    // 开始事务
    const transaction = await AiCodeAnalysisReports.sequelize.transaction();
    
    try {
      const newReport = await AiCodeAnalysisReports.create(testData, { transaction });
      console.log('✅ 主报告记录创建成功，ID:', newReport.id);

      // 插入文件详情
      const fileDetailData = {
        report_id: newReport.id,
        pr_global_id: testData.pr_global_id,
        file_name: 'test/fixed-config.js',
        file_type: 'js',
        total_lines: 100,
        changed_lines: 50,
        ai_matched_lines: 25,
        ai_code_ratio: 0.25,
        detection_method: '修复测试'
      };

      const newFileDetail = await AiCodeAnalysisFileDetails.create(fileDetailData, { transaction });
      console.log('✅ 文件详情记录创建成功，ID:', newFileDetail.id);

      // 提交事务
      await transaction.commit();
      console.log('✅ 事务提交成功');

      // 验证数据是否真正保存
      console.log('\n4. 验证数据保存...');
      const savedReport = await AiCodeAnalysisReports.findByPk(newReport.id, {
        include: [{
          model: AiCodeAnalysisFileDetails,
          as: 'fileDetails'
        }]
      });

      if (savedReport) {
        console.log('✅ 数据验证成功:');
        console.log(`  - 主记录 ID: ${savedReport.id}`);
        console.log(`  - PR ID: ${savedReport.pr_global_id}`);
        console.log(`  - 文件详情数量: ${savedReport.fileDetails ? savedReport.fileDetails.length : 0}`);
        
        return { 
          success: true, 
          reportId: savedReport.id,
          prGlobalId: savedReport.pr_global_id
        };
      } else {
        console.log('❌ 数据验证失败，记录未找到');
        return { success: false, reason: 'Data not found after insert' };
      }

    } catch (error) {
      await transaction.rollback();
      console.log('❌ 事务回滚');
      throw error;
    }

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

// 测试重复插入检查
async function testDuplicateCheck() {
  console.log('\n🔍 测试重复插入检查...\n');

  try {
    await initializeDatabase();
    
    const duplicateData = {
      pr_global_id: `TEST_DUPLICATE_${Date.now()}`,
      pr_title: '测试重复检查',
      git_repository: 'https://git.example.com/test-duplicate.git',
      git_branch: 'test-duplicate',
      mis_number: 'test_duplicate',
      detection_method: '重复测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    // 第一次插入
    console.log('第一次插入:', duplicateData.pr_global_id);
    const firstReport = await AiCodeAnalysisReports.create(duplicateData);
    console.log('✅ 第一次插入成功，ID:', firstReport.id);

    // 检查是否存在
    const existingReport = await AiCodeAnalysisReports.findOne({
      where: { pr_global_id: duplicateData.pr_global_id }
    });

    if (existingReport) {
      console.log('✅ 重复检查正常，找到已存在的记录:', existingReport.id);
    } else {
      console.log('❌ 重复检查失败，未找到已存在的记录');
    }

    // 尝试第二次插入（应该失败）
    try {
      console.log('尝试第二次插入相同的 PR ID...');
      await AiCodeAnalysisReports.create(duplicateData);
      console.log('❌ 第二次插入成功了，这不应该发生！');
    } catch (duplicateError) {
      console.log('✅ 第二次插入正确失败:', duplicateError.message.substring(0, 100) + '...');
    }

  } catch (error) {
    console.error('❌ 重复检查测试失败:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 开始完整测试...\n');

  // 测试1：基本功能
  const basicTest = await testFixedConfig();
  
  // 测试2：重复检查
  await testDuplicateCheck();

  console.log('\n📊 测试结果汇总:');
  console.log('- 基本功能测试:', basicTest.success ? '✅ 成功' : '❌ 失败');

  if (basicTest.success) {
    console.log('\n🎉 配置修复成功！');
    console.log('\n📝 下一步操作:');
    console.log('1. 部署: nest deploy -e test');
    console.log('2. 测试真实的 POST 请求');
    console.log('3. 验证数据是否正确保存');
    
    console.log('\n🧪 建议的测试 POST 请求:');
    console.log(`curl --location --request POST 'https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c' \\
--header 'Content-Type: application/json' \\
--data '{
  "pr_global_id": "TEST_DEPLOY_${Date.now()}",
  "pr_title": "部署后测试",
  "git_repository": "https://git.example.com/deploy-test.git",
  "git_branch": "deploy-test",
  "mis_number": "deploy_user",
  "detection_method": "部署测试",
  "analysis_timestamp": "${new Date().toISOString()}",
  "total_files": 1,
  "total_lines": 100,
  "changed_lines": 50,
  "ai_generated_lines": 25,
  "ai_code_ratio": 0.25
}'`);
  } else {
    console.log('\n❌ 配置仍有问题，需要进一步调试。');
    console.log('失败原因:', basicTest.reason || basicTest.error);
  }
}

if (require.main === module) {
  runAllTests().then(() => {
    console.log('\n🏁 测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testFixedConfig, testDuplicateCheck };
