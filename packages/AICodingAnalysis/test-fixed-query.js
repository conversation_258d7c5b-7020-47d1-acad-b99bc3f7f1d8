/**
 * 测试修复后的查询条件
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testFixedQuery() {
  console.log('🧪 测试修复后的查询条件...\n');

  const tests = [
    {
      name: '1. gitBranch=4324 (应该返回空)',
      params: {
        gitBranch: '4324'
      },
      expectedEmpty: true
    },
    {
      name: '2. gitBranch=CreatePage (应该有结果)',
      params: {
        gitBranch: 'CreatePage'
      },
      expectedEmpty: false
    },
    {
      name: '3. misNumber=4234 (应该返回空)',
      params: {
        misNumber: '4234'
      },
      expectedEmpty: true
    },
    {
      name: '4. misNumber=yaoyan (应该有结果)',
      params: {
        misNumber: 'yaoyan'
      },
      expectedEmpty: false
    },
    {
      name: '5. 组合查询 (应该返回空)',
      params: {
        misNumber: '4234',
        gitBranch: '4324',
        prId: '4324'
      },
      expectedEmpty: true
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('查询参数:', test.params);

    try {
      const mockEvent = {
        extensions: {
          request: {
            method: 'GET',
            url: `/?${new URLSearchParams(test.params).toString()}`,
            path: '/'
          }
        },
        queryStringParameters: test.params
      };

      const result = await AICodingAnalysisResult(mockEvent, {});

      if (result.statusCode === 200 && result.body.success) {
        const data = result.body.data;
        const isEmpty = data.pagination.totalCount === 0;
        
        console.log(`结果: 总记录=${data.pagination.totalCount}, 返回=${data.list.length}`);
        
        if (isEmpty === test.expectedEmpty) {
          console.log('✅ 符合预期');
          passedTests++;
        } else {
          console.log(`❌ 不符合预期 (期望${test.expectedEmpty ? '空' : '有结果'})`);
          
          if (!isEmpty && data.list.length > 0) {
            const sample = data.list[0];
            console.log(`   返回的记录: PR=${sample.pr_global_id}, MIS=${sample.mis_number}, 分支=${sample.git_branch}`);
            
            // 检查是否真的匹配
            if (test.params.gitBranch) {
              console.log(`   分支是否包含 '${test.params.gitBranch}': ${sample.git_branch.includes(test.params.gitBranch)}`);
            }
            if (test.params.misNumber) {
              console.log(`   MIS是否包含 '${test.params.misNumber}': ${sample.mis_number && sample.mis_number.includes(test.params.misNumber)}`);
            }
            if (test.params.prId) {
              console.log(`   PR是否包含 '${test.params.prId}': ${sample.pr_global_id.includes(test.params.prId)}`);
            }
          }
        }
      } else {
        console.log(`❌ 请求失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！查询条件修复成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 部分测试失败！查询条件仍有问题！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testFixedQuery().then((result) => {
    if (result.success) {
      console.log('\n✅ 修复验证成功！可以部署了！');
    } else {
      console.log('\n❌ 修复验证失败，需要进一步调试！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testFixedQuery };
