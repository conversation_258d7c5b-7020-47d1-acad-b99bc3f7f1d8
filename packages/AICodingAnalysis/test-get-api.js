/**
 * 测试 GET API 接口
 */

// 模拟生产环境
process.env.USER = 'root';

const { AiCodeAnalysisReports, AiCodeAnalysisFileDetails, initializeDatabase, getDatabaseStatus } = require('./models/AICodingAnalysisModels-working.js');

async function testGetAPI() {
  console.log('🧪 测试 GET API 接口...\n');

  try {
    // 初始化数据库
    await initializeDatabase();
    console.log('数据库状态:', getDatabaseStatus());

    // 1. 测试基本查询（无条件）
    console.log('\n1. 测试基本查询（无条件）...');
    const basicResult = await AiCodeAnalysisReports.findAndCountAll({
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      order: [['analysis_timestamp', 'DESC']],
      offset: 0,
      limit: 20
    });

    console.log('基本查询结果:');
    console.log('- 总数:', basicResult.count);
    console.log('- 返回记录数:', basicResult.rows.length);

    if (basicResult.rows.length > 0) {
      console.log('- 第一条记录:', {
        id: basicResult.rows[0].id,
        pr_global_id: basicResult.rows[0].pr_global_id,
        pr_title: basicResult.rows[0].pr_title,
        fileDetails: basicResult.rows[0].fileDetails ? basicResult.rows[0].fileDetails.length : 'undefined'
      });
    }

    // 2. 测试 Sequelize Op 操作符
    console.log('\n2. 测试 Sequelize Op 操作符...');
    try {
      const Op = AiCodeAnalysisReports.sequelize.Sequelize.Op;
      console.log('Op 对象:', Object.keys(Op));

      // 测试带条件的查询
      const conditionalResult = await AiCodeAnalysisReports.findAndCountAll({
        where: {
          pr_global_id: {
            [Op.like]: '%TEST%'
          }
        },
        include: [{
          model: AiCodeAnalysisFileDetails,
          as: 'fileDetails',
          required: false
        }],
        order: [['analysis_timestamp', 'DESC']],
        offset: 0,
        limit: 20
      });

      console.log('条件查询结果:');
      console.log('- 总数:', conditionalResult.count);
      console.log('- 返回记录数:', conditionalResult.rows.length);

    } catch (opError) {
      console.error('Op 操作符测试失败:', opError.message);
    }

    // 3. 模拟完整的 GET 请求处理
    console.log('\n3. 模拟完整的 GET 请求处理...');

    const mockEvent = {
      queryStringParameters: {
        page: '1',
        pageSize: '10'
      }
    };

    const result = await simulateGetRequest(mockEvent);
    console.log('模拟 GET 请求结果:');
    console.log('- 状态码:', result.statusCode);
    console.log('- 成功:', result.body.success);
    console.log('- 消息:', result.body.message);
    console.log('- 数据列表长度:', result.body.data?.list?.length || 0);
    console.log('- 总数:', result.body.data?.pagination?.totalCount || 0);

    return { success: true };

  } catch (error) {
    console.error('❌ GET API 测试失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

// 模拟 GET 请求处理函数
async function simulateGetRequest(event) {
  try {
    // 解析查询参数
    const queryParams = event.queryStringParameters || {};
    const {
      page = 1,
      pageSize = 20,
      analysisTime,
      misNumber,
      prId,
      gitRepository,
      gitBranch,
      startTime,
      endTime
    } = queryParams;

    // 构建查询条件
    const whereConditions = {};

    if (analysisTime) {
      whereConditions.analysis_timestamp = analysisTime;
    }

    // 检查 Op 是否可用
    let Op;
    try {
      Op = AiCodeAnalysisReports.sequelize.Sequelize.Op;
    } catch (opError) {
      console.warn('无法获取 Sequelize Op，使用简单查询');
    }

    if (Op) {
      if (startTime && endTime) {
        whereConditions.analysis_timestamp = {
          [Op.between]: [startTime, endTime]
        };
      } else if (startTime) {
        whereConditions.analysis_timestamp = {
          [Op.gte]: startTime
        };
      } else if (endTime) {
        whereConditions.analysis_timestamp = {
          [Op.lte]: endTime
        };
      }

      if (misNumber) {
        whereConditions.mis_number = {
          [Op.like]: `%${misNumber}%`
        };
      }

      if (prId) {
        whereConditions.pr_global_id = {
          [Op.like]: `%${prId}%`
        };
      }

      if (gitRepository) {
        whereConditions.git_repository = {
          [Op.like]: `%${gitRepository}%`
        };
      }

      if (gitBranch) {
        whereConditions.git_branch = {
          [Op.like]: `%${gitBranch}%`
        };
      }
    }

    // 计算分页参数
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const limit = parseInt(pageSize);

    console.log('查询条件:', whereConditions);
    console.log('分页参数:', { offset, limit });

    // 执行查询
    const result = await AiCodeAnalysisReports.findAndCountAll({
      where: whereConditions,
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      order: [['analysis_timestamp', 'DESC']],
      offset: offset,
      limit: limit
    });

    // 计算分页信息
    const totalCount = result.count;
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = parseInt(page);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },
      body: {
        success: true,
        message: '查询成功',
        data: {
          list: result.rows,
          pagination: {
            currentPage: currentPage,
            pageSize: limit,
            totalCount: totalCount,
            totalPages: totalPages,
            hasNext: currentPage < totalPages,
            hasPrev: currentPage > 1
          }
        }
      }
    };
  } catch (error) {
    console.error('模拟 GET 请求失败:', error);
    throw error;
  }
}

if (require.main === module) {
  testGetAPI().then((result) => {
    if (result.success) {
      console.log('\n✅ GET API 测试成功！');
    } else {
      console.log('\n❌ GET API 测试失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testGetAPI, simulateGetRequest };
