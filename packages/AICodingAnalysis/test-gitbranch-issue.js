/**
 * 测试 gitBranch 查询问题
 */

// 模拟生产环境
process.env.USER = 'root';

async function testGitBranchIssue() {
  console.log('🔍 测试 gitBranch 查询问题...\n');

  try {
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    await modelsModule.initializeDatabase();
    
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;

    // 1. 查看数据库中的所有分支
    console.log('1. 查看数据库中的所有分支:');
    const allRecords = await AiCodeAnalysisReports.findAll({
      attributes: ['id', 'pr_global_id', 'git_branch'],
      order: [['id', 'ASC']]
    });
    
    console.log('数据库中的所有记录:');
    allRecords.forEach(record => {
      console.log(`- ID: ${record.id}, PR: ${record.pr_global_id}, 分支: ${record.git_branch}`);
    });

    // 2. 测试 gitBranch=4324 的查询
    console.log('\n2. 测试 gitBranch=4324 的查询:');
    const Op = AiCodeAnalysisReports.sequelize.Sequelize.Op;
    
    const queryCondition = {
      git_branch: {
        [Op.like]: `%4324%`
      }
    };
    
    console.log('查询条件:', JSON.stringify(queryCondition, null, 2));
    
    const result = await AiCodeAnalysisReports.findAndCountAll({
      where: queryCondition,
      attributes: ['id', 'pr_global_id', 'git_branch']
    });
    
    console.log('查询结果:');
    console.log('- 总数:', result.count);
    console.log('- 记录数:', result.rows.length);
    
    if (result.rows.length > 0) {
      console.log('- 返回的记录:');
      result.rows.forEach(record => {
        console.log(`  ID: ${record.id}, PR: ${record.pr_global_id}, 分支: ${record.git_branch}`);
        console.log(`  分支是否包含 '4324': ${record.git_branch.includes('4324')}`);
      });
    }

    // 3. 测试直接 SQL 查询
    console.log('\n3. 测试直接 SQL 查询:');
    const [sqlResults] = await AiCodeAnalysisReports.sequelize.query(
      "SELECT id, pr_global_id, git_branch FROM ai_code_analysis_reports WHERE git_branch LIKE '%4324%'"
    );
    
    console.log('直接 SQL 查询结果:');
    console.log('- 记录数:', sqlResults.length);
    if (sqlResults.length > 0) {
      sqlResults.forEach(record => {
        console.log(`  ID: ${record.id}, PR: ${record.pr_global_id}, 分支: ${record.git_branch}`);
      });
    }

    // 4. 测试存在的分支查询
    console.log('\n4. 测试存在的分支查询 (CreatePage):');
    const existingBranchResult = await AiCodeAnalysisReports.findAndCountAll({
      where: {
        git_branch: {
          [Op.like]: `%CreatePage%`
        }
      },
      attributes: ['id', 'pr_global_id', 'git_branch']
    });
    
    console.log('CreatePage 查询结果:');
    console.log('- 总数:', existingBranchResult.count);
    console.log('- 记录数:', existingBranchResult.rows.length);
    
    if (existingBranchResult.rows.length > 0) {
      existingBranchResult.rows.forEach(record => {
        console.log(`  ID: ${record.id}, PR: ${record.pr_global_id}, 分支: ${record.git_branch}`);
      });
    }

    // 5. 验证查询逻辑
    console.log('\n5. 验证查询逻辑:');
    const testBranch = 'feature/CreatePage-YAOYAN-0523';
    console.log(`测试分支: ${testBranch}`);
    console.log(`包含 '4324': ${testBranch.includes('4324')}`);
    console.log(`包含 'CreatePage': ${testBranch.includes('CreatePage')}`);
    console.log(`包含 'YAOYAN': ${testBranch.includes('YAOYAN')}`);

    // 6. 模拟完整的 GET 请求
    console.log('\n6. 模拟完整的 GET 请求 (gitBranch=4324):');
    const { AICodingAnalysisResult } = require('./index.js');
    
    const mockEvent = {
      extensions: {
        request: {
          method: 'GET',
          url: '/?gitBranch=4324',
          path: '/'
        }
      },
      queryStringParameters: {
        gitBranch: '4324'
      }
    };

    const apiResult = await AICodingAnalysisResult(mockEvent, {});
    console.log('API 返回状态码:', apiResult.statusCode);
    console.log('API 返回成功:', apiResult.body.success);
    console.log('API 返回总数:', apiResult.body.data?.pagination?.totalCount || 0);
    console.log('API 返回记录数:', apiResult.body.data?.list?.length || 0);
    
    if (apiResult.body.data?.list?.length > 0) {
      console.log('API 返回的记录:');
      apiResult.body.data.list.forEach(record => {
        console.log(`  ID: ${record.id}, PR: ${record.pr_global_id}, 分支: ${record.git_branch}`);
        console.log(`  分支是否包含 '4324': ${record.git_branch.includes('4324')}`);
      });
    }

    return { success: true };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  testGitBranchIssue().then((result) => {
    console.log('\n' + '='.repeat(60));
    if (result.success) {
      console.log('✅ 测试完成！');
    } else {
      console.log('❌ 测试失败');
    }
    console.log('='.repeat(60));
    process.exit(0);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testGitBranchIssue };
