/**
 * 本地数据库连接测试脚本
 * 用于在本地环境测试不同的数据库配置
 */

const {
  ZebraSequelizeFactory,
  DataTypes
} = require('@bfe/zebra-proxy-sequelize');

// 数据库配置选项
const configs = [
  {
    name: '配置1 - 当前配置',
    appName: 'com.sankuai.dzufebiz.manage',
    refKey: 'dzfrontendshanghai_swift_test',
    env: 'test'
  },
  {
    name: '配置2 - 带.man后缀',
    appName: 'com.sankuai.dzufebiz.manage',
    refKey: 'dzfrontendshanghai_swift_test.man',
    env: 'test'
  },
  {
    name: '配置3 - 点分隔符',
    appName: 'com.sankuai.dzufebiz.manage',
    refKey: 'dzfrontendshanghai.swift.test',
    env: 'test'
  },
  {
    name: '配置4 - 带.db后缀',
    appName: 'com.sankuai.dzufebiz.manage',
    refKey: 'dzfrontendshanghai_swift_test.db',
    env: 'test'
  }
];

async function testDatabaseConfig(config) {
  console.log(`\n=== 测试 ${config.name} ===`);
  console.log('配置:', {
    appName: config.appName,
    refKey: config.refKey,
    env: config.env
  });

  try {
    // 创建 zebra-proxy 连接
    const options = {
      appName: config.appName,
      refKey: config.refKey,
      dbType: "zebra-proxy",
      env: config.env,
      pool: {
        max: 5,
        min: 1,
      },
      dialectOptions: {
        dateStrings: true,
      }
    };

    console.log('创建 Zebra-proxy 连接...');
    const sequelize = ZebraSequelizeFactory.create(options);
    console.log('✅ Zebra-proxy 连接创建成功');

    // 测试连接
    console.log('测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接测试成功');

    // 获取数据库信息
    try {
      const [results] = await sequelize.query('SELECT DATABASE() as current_database, VERSION() as version');
      console.log('✅ 数据库信息:', {
        database: results[0]?.current_database || '未知',
        version: results[0]?.version || '未知'
      });
    } catch (error) {
      console.log('❌ 获取数据库信息失败:', error.message);
    }

    // 检查表是否存在
    try {
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'ai_code_analysis%'");
      console.log('✅ 相关表数量:', tables.length);
      if (tables.length > 0) {
        tables.forEach(table => {
          console.log('  -', Object.values(table)[0]);
        });
      }
    } catch (error) {
      console.log('❌ 检查表失败:', error.message);
    }

    // 尝试创建测试表
    try {
      const TestModel = sequelize.define('ai_code_test_' + Date.now(), {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        test_field: {
          type: DataTypes.STRING(50),
          allowNull: false
        }
      }, {
        freezeTableName: true,
        timestamps: false
      });

      await TestModel.sync({ force: false });
      console.log('✅ 测试表创建成功');

      // 插入测试数据
      const testRecord = await TestModel.create({
        test_field: 'test_value_' + Date.now()
      });
      console.log('✅ 测试数据插入成功，ID:', testRecord.id);

      // 查询测试数据
      const count = await TestModel.count();
      console.log('✅ 测试表记录数:', count);

      // 删除测试表
      await TestModel.drop();
      console.log('✅ 测试表删除成功');

    } catch (error) {
      console.log('❌ 表操作失败:', error.message);
    }

    // 关闭连接
    await sequelize.close();
    console.log('✅ 数据库连接已关闭');

    return { success: true, config: config.name };

  } catch (error) {
    console.error('❌ 配置测试失败:', error.message);
    console.error('错误详情:', {
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, config: config.name, error: error.message };
  }
}

async function testAllConfigs() {
  console.log('🚀 开始测试所有数据库配置...\n');

  const results = [];

  for (const config of configs) {
    const result = await testDatabaseConfig(config);
    results.push(result);
    
    // 等待一下再测试下一个配置
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n=== 测试结果汇总 ===');
  results.forEach(result => {
    if (result.success) {
      console.log(`✅ ${result.config}: 成功`);
    } else {
      console.log(`❌ ${result.config}: 失败 - ${result.error}`);
    }
  });

  const successCount = results.filter(r => r.success).length;
  console.log(`\n总计: ${successCount}/${results.length} 个配置成功`);

  if (successCount > 0) {
    console.log('\n🎉 找到可用的数据库配置！');
    const successConfigs = results.filter(r => r.success);
    console.log('建议使用:', successConfigs[0].config);
  } else {
    console.log('\n😞 所有配置都失败了，可能需要：');
    console.log('1. 检查网络连接');
    console.log('2. 确认数据库权限');
    console.log('3. 联系数据库管理员');
  }
}

// 运行测试
if (require.main === module) {
  testAllConfigs().then(() => {
    console.log('\n🏁 测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 测试过程出错:', error);
    process.exit(1);
  });
}

module.exports = { testAllConfigs, testDatabaseConfig };
