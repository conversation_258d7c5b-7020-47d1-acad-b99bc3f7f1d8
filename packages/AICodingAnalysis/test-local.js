/**
 * 本地测试脚本
 */

// 设置环境变量
process.env.NODE_ENV = 'development';

const { AICodingAnalysisResult } = require('./index.js');

// 模拟 GET 请求事件
const mockGetEvent = {
  httpMethod: 'GET',
  queryStringParameters: {
    page: '1',
    pageSize: '10',
    misNumber: 'test'
  }
};

// 模拟 POST 请求事件
const mockPostEvent = {
  httpMethod: 'POST',
  body: JSON.stringify({
    pr_global_id: 'PR-2024-TEST-001',
    pr_title: '测试PR',
    git_repository: 'https://git.example.com/test.git',
    git_branch: 'feature/test',
    mis_number: 'testuser',
    detection_method: 'AI_DETECTION',
    analysis_timestamp: new Date().toISOString(),
    total_files: 5,
    total_lines: 1000,
    changed_lines: 200,
    ai_generated_lines: 50,
    ai_code_ratio: 0.25,
    file_details: [
      {
        file_name: 'src/test.js',
        file_type: 'js',
        total_lines: 100,
        changed_lines: 20,
        ai_matched_lines: 5,
        ai_code_ratio: 0.25
      }
    ]
  })
};

async function testAPI() {
  console.log('开始测试 AICodingAnalysisResult API...\n');
  
  try {
    // 等待数据库初始化
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('=== 测试 POST 请求 ===');
    const postResult = await AICodingAnalysisResult(mockPostEvent, {});
    console.log('POST 结果:', JSON.stringify(postResult, null, 2));
    
    console.log('\n=== 测试 GET 请求 ===');
    const getResult = await AICodingAnalysisResult(mockGetEvent, {});
    console.log('GET 结果:', JSON.stringify(getResult, null, 2));
    
    console.log('\n=== 测试重复 POST 请求 ===');
    const duplicatePostResult = await AICodingAnalysisResult(mockPostEvent, {});
    console.log('重复 POST 结果:', JSON.stringify(duplicatePostResult, null, 2));
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testAPI();
