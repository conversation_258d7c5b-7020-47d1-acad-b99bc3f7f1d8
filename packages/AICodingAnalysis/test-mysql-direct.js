/**
 * 使用原生 MySQL 连接测试数据库
 */

const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: '*************',
  port: 5002,
  user: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'dzufebiz', // 尝试不同的数据库名
  connectTimeout: 10000,
  acquireTimeout: 10000,
  timeout: 10000
};

async function testMySQLConnection() {
  console.log('🚀 开始测试 MySQL 直连...\n');
  
  console.log('📋 连接配置:');
  console.log({
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    password: '***' + dbConfig.password.slice(-4),
    database: dbConfig.database
  });

  let connection;
  
  try {
    console.log('\n🔗 正在建立连接...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功！');

    // 测试基本查询
    console.log('\n📊 获取数据库信息...');
    const [rows] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as version, NOW() as current_time');
    console.log('数据库信息:', rows[0]);

    // 查看所有数据库
    console.log('\n📋 查看所有数据库...');
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('可用数据库:');
    databases.forEach(db => {
      console.log(`  - ${Object.values(db)[0]}`);
    });

    // 查看当前数据库的表
    console.log('\n📋 查看当前数据库的表...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`当前数据库 (${dbConfig.database}) 的表:`, tables.length, '个');
    if (tables.length > 0) {
      tables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('  (没有表)');
    }

    // 查找 AI 相关的表
    console.log('\n🔍 查找 AI 相关的表...');
    const [aiTables] = await connection.execute("SHOW TABLES LIKE '%ai%'");
    if (aiTables.length > 0) {
      console.log('找到 AI 相关的表:');
      aiTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('没有找到 AI 相关的表');
    }

    // 尝试创建测试表
    console.log('\n🧪 测试创建表权限...');
    const testTableName = `test_table_${Date.now()}`;
    
    try {
      await connection.execute(`
        CREATE TABLE ${testTableName} (
          id INT AUTO_INCREMENT PRIMARY KEY,
          test_field VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('✅ 创建表成功');

      // 插入测试数据
      await connection.execute(`INSERT INTO ${testTableName} (test_field) VALUES (?)`, ['test_value']);
      console.log('✅ 插入数据成功');

      // 查询测试数据
      const [testRows] = await connection.execute(`SELECT * FROM ${testTableName}`);
      console.log('✅ 查询数据成功:', testRows.length, '条记录');

      // 删除测试表
      await connection.execute(`DROP TABLE ${testTableName}`);
      console.log('✅ 删除测试表成功');

    } catch (tableError) {
      console.log('❌ 表操作失败:', tableError.message);
    }

    console.log('\n🎉 MySQL 直连测试完成！');
    return { success: true, config: dbConfig };

  } catch (error) {
    console.error('\n❌ MySQL 连接失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', {
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
    return { success: false, error: error.message };

  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 测试不同的数据库名
async function testDifferentDatabases() {
  const databases = [
    'dzufebiz',
    'redalert', // 参考项目使用的数据库名
    'manage',
    'test',
    null // 不指定数据库
  ];

  console.log('🔍 测试不同的数据库名...\n');

  for (const dbName of databases) {
    console.log(`\n--- 测试数据库: ${dbName || '(不指定)'} ---`);
    
    const config = { ...dbConfig };
    if (dbName) {
      config.database = dbName;
    } else {
      delete config.database;
    }

    try {
      const connection = await mysql.createConnection(config);
      console.log('✅ 连接成功');
      
      const [rows] = await connection.execute('SELECT DATABASE() as current_db');
      console.log('当前数据库:', rows[0].current_db || '(未选择)');
      
      await connection.end();
    } catch (error) {
      console.log('❌ 连接失败:', error.message);
    }
  }
}

// 运行测试
async function runAllTests() {
  try {
    // 测试主配置
    const result = await testMySQLConnection();
    
    if (result.success) {
      console.log('\n✅ 主要测试成功！可以使用这个配置。');
    } else {
      console.log('\n❌ 主要测试失败，尝试其他数据库名...');
      await testDifferentDatabases();
    }

    console.log('\n📝 如果连接成功，可以更新 AICodingAnalysisModels.js:');
    console.log(`
// 使用直连 MySQL 替代 zebra-proxy
const mysql = require('mysql2/promise');

const dbConfig = {
  host: '${dbConfig.host}',
  port: ${dbConfig.port},
  user: '${dbConfig.user}',
  password: '${dbConfig.password}',
  database: '${dbConfig.database}',
  pool: {
    max: 20,
    min: 1
  }
};

// 创建连接池
const pool = mysql.createPool(dbConfig);
    `);

  } catch (error) {
    console.error('测试过程出错:', error);
  }
}

if (require.main === module) {
  runAllTests().then(() => {
    console.log('\n🏁 所有测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = { testMySQLConnection, testDifferentDatabases };
