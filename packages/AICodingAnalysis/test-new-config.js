/**
 * 基于参考项目配置的数据库连接测试
 */

const {
  ZebraSequelizeFactory,
  DataTypes
} = require('@bfe/zebra-proxy-sequelize');

// 基于参考项目的配置格式
const configs = [

  {
    name: '新配置5 - 原格式但加database',
    appName: 'com.sankuai.dzufebiz.manage',
    refKey: 'dzfrontendshanghai_swift_test',
    database: 'dzufebiz'
  }
];

async function testConfig(config) {
  console.log(`\n🔍 测试 ${config.name}`);
  console.log('配置:', {
    appName: config.appName,
    refKey: config.refKey,
    database: config.database
  });

  try {
    const options = {
      appName: config.appName,
      refKey: config.refKey,
      dbType: "zebra-proxy",
      database: config.database,
      pool: {
        max: 5,
        min: 1,
      },
      dialectOptions: {
        dateStrings: true,
      },
      logging: false
    };

    console.log('创建连接...');
    const sequelize = ZebraSequelizeFactory.create(options);
    
    console.log('测试连接...');
    await sequelize.authenticate();
    
    console.log('✅ 连接成功！');
    
    // 获取数据库信息
    try {
      const [results] = await sequelize.query('SELECT DATABASE() as db, VERSION() as version');
      console.log('📊 数据库信息:', {
        database: results[0]?.db,
        version: results[0]?.version?.substring(0, 30) + '...'
      });
    } catch (e) {
      console.log('⚠️  无法获取数据库信息:', e.message);
    }
    
    // 检查表
    try {
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'ai_code_analysis%'");
      console.log('📋 相关表数量:', tables.length);
    } catch (e) {
      console.log('⚠️  无法检查表:', e.message);
    }
    
    await sequelize.close();
    return { success: true, config: config.name };
    
  } catch (error) {
    console.log('❌ 失败:', error.message.substring(0, 150) + '...');
    return { success: false, config: config.name, error: error.message };
  }
}

async function testAllNewConfigs() {
  console.log('🚀 基于参考项目配置测试数据库连接...\n');
  
  const results = [];
  
  for (const config of configs) {
    const result = await testConfig(config);
    results.push(result);
    
    if (result.success) {
      console.log(`\n🎉 找到可用配置: ${config.name}`);
      console.log('🔧 建议更新代码中的配置:');
      console.log(`const refKey = '${config.refKey}';`);
      console.log(`database: '${config.database}'`);
      break; // 找到第一个可用的就停止
    }
    
    // 等待一下再测试下一个
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n=== 测试结果汇总 ===');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  if (successful.length > 0) {
    console.log('✅ 成功的配置:');
    successful.forEach(r => console.log(`  - ${r.config}`));
    
    console.log('\n📝 下一步操作:');
    console.log('1. 更新 models/AICodingAnalysisModels.js 中的配置');
    console.log('2. 部署: nest deploy -e test');
    console.log('3. 测试 POST 请求');
    console.log('4. 验证数据是否真正保存');
    
  } else {
    console.log('❌ 所有配置都失败了');
    console.log('\n🔍 错误分析:');
    
    const errorPatterns = {
      'deprecated': 0,
      'not found': 0,
      'permission': 0,
      'network': 0,
      'other': 0
    };
    
    failed.forEach(r => {
      const error = r.error.toLowerCase();
      if (error.includes('deprecated')) errorPatterns.deprecated++;
      else if (error.includes('not found') || error.includes('null')) errorPatterns['not found']++;
      else if (error.includes('permission') || error.includes('access')) errorPatterns.permission++;
      else if (error.includes('connect') || error.includes('network')) errorPatterns.network++;
      else errorPatterns.other++;
    });
    
    Object.entries(errorPatterns).forEach(([type, count]) => {
      if (count > 0) console.log(`  - ${type}: ${count} 次`);
    });
    
    console.log('\n💡 建议:');
    console.log('1. 联系团队确认正确的 refKey 格式');
    console.log('2. 确认测试环境数据库是否存在');
    console.log('3. 检查应用的数据库访问权限');
  }
}

// 运行测试
if (require.main === module) {
  testAllNewConfigs().then(() => {
    console.log('\n🏁 测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  });
}

module.exports = { testAllNewConfigs };
