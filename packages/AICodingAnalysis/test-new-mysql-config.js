/**
 * 测试新的 MySQL 配置
 */

const { AiCodeAnalysisReports, AiCodeAnalysisFileDetails, initializeDatabase, getDatabaseStatus } = require('./models/AICodingAnalysisModels-mysql.js');

async function testNewMySQLConfig() {
  console.log('🚀 测试新的 MySQL 配置...\n');

  try {
    // 初始化数据库
    console.log('1. 初始化数据库连接...');
    await initializeDatabase();
    
    // 检查数据库状态
    const dbStatus = getDatabaseStatus();
    console.log('2. 数据库状态:', dbStatus);
    
    if (dbStatus === 'MOCK_DATABASE') {
      console.warn('⚠️  警告：当前使用模拟数据库，数据不会真正保存！');
      return { success: false, reason: 'Using mock database' };
    }
    
    console.log('✅ 使用真实数据库');

    // 测试查询
    console.log('\n3. 测试查询功能...');
    const result = await AiCodeAnalysisReports.findAndCountAll({
      limit: 5,
      offset: 0,
      order: [['created_at', 'DESC']]
    });
    
    console.log('✅ 查询成功，记录数:', result.count);
    if (result.rows.length > 0) {
      console.log('最新记录:', {
        id: result.rows[0].id,
        pr_global_id: result.rows[0].pr_global_id,
        pr_title: result.rows[0].pr_title?.substring(0, 50) + '...'
      });
    }

    // 测试插入
    console.log('\n4. 测试插入功能...');
    const testData = {
      pr_global_id: `TEST_MYSQL_${Date.now()}`,
      pr_title: '测试MySQL直连',
      git_repository: 'https://git.example.com/test-mysql.git',
      git_branch: 'test-mysql-branch',
      mis_number: 'test_mysql',
      detection_method: 'MySQL直连测试',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    const newReport = await AiCodeAnalysisReports.create(testData);
    console.log('✅ 插入成功，ID:', newReport.id);

    // 测试文件详情插入
    console.log('\n5. 测试文件详情插入...');
    const fileDetailData = {
      report_id: newReport.id,
      pr_global_id: testData.pr_global_id,
      file_name: 'test/mysql-direct.js',
      file_type: 'js',
      total_lines: 100,
      changed_lines: 50,
      ai_matched_lines: 25,
      ai_code_ratio: 0.25,
      detection_method: 'MySQL直连测试'
    };

    const newFileDetail = await AiCodeAnalysisFileDetails.create(fileDetailData);
    console.log('✅ 文件详情插入成功，ID:', newFileDetail.id);

    // 测试关联查询
    console.log('\n6. 测试关联查询...');
    const reportWithDetails = await AiCodeAnalysisReports.findByPk(newReport.id, {
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails'
      }]
    });
    
    if (reportWithDetails && reportWithDetails.fileDetails) {
      console.log('✅ 关联查询成功，文件详情数量:', reportWithDetails.fileDetails.length);
    } else {
      console.log('⚠️  关联查询返回空结果');
    }

    // 清理测试数据
    console.log('\n7. 清理测试数据...');
    await AiCodeAnalysisFileDetails.destroy({ where: { report_id: newReport.id } });
    await AiCodeAnalysisReports.destroy({ where: { id: newReport.id } });
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 MySQL 直连配置测试完全成功！');
    return { success: true };

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

// 模拟 POST 请求处理
async function testPostRequest() {
  console.log('\n🧪 模拟 POST 请求处理...\n');

  const mockRequestBody = {
    pr_global_id: `MOCK_POST_${Date.now()}`,
    pr_title: '模拟POST请求测试',
    git_repository: 'https://git.example.com/mock-post.git',
    git_branch: 'mock-post-branch',
    mis_number: 'mock_user',
    detection_method: '模拟POST测试',
    analysis_timestamp: new Date().toISOString(),
    total_files: 2,
    total_lines: 200,
    changed_lines: 100,
    ai_generated_lines: 50,
    ai_code_ratio: 0.25,
    file_details: [
      {
        file_name: 'src/test1.js',
        file_type: 'js',
        total_lines: 100,
        changed_lines: 50,
        ai_matched_lines: 25,
        ai_code_ratio: 0.25,
        detection_method: '模拟POST测试'
      },
      {
        file_name: 'src/test2.ts',
        file_type: 'ts',
        total_lines: 100,
        changed_lines: 50,
        ai_matched_lines: 25,
        ai_code_ratio: 0.25,
        detection_method: '模拟POST测试'
      }
    ]
  };

  try {
    // 初始化数据库
    await initializeDatabase();
    
    const dbStatus = getDatabaseStatus();
    console.log('数据库状态:', dbStatus);
    
    if (dbStatus === 'MOCK_DATABASE') {
      console.warn('⚠️  警告：当前使用模拟数据库，数据不会真正保存！');
    }

    // 检查是否已存在
    const existingReport = await AiCodeAnalysisReports.findOne({
      where: { pr_global_id: mockRequestBody.pr_global_id }
    });

    if (existingReport) {
      console.log('❌ PR ID 已存在，模拟返回 409 错误');
      return { success: false, statusCode: 409, message: 'PR ID already exists' };
    }

    // 开始事务
    console.log('开始数据库事务...');
    const transaction = await AiCodeAnalysisReports.sequelize.transaction();

    try {
      // 创建主报告记录
      const reportData = {
        pr_global_id: mockRequestBody.pr_global_id,
        pr_title: mockRequestBody.pr_title,
        git_repository: mockRequestBody.git_repository,
        git_branch: mockRequestBody.git_branch,
        mis_number: mockRequestBody.mis_number || null,
        detection_method: mockRequestBody.detection_method,
        analysis_timestamp: new Date(mockRequestBody.analysis_timestamp),
        total_files: mockRequestBody.total_files || 0,
        total_lines: mockRequestBody.total_lines || 0,
        changed_lines: mockRequestBody.changed_lines || 0,
        ai_generated_lines: mockRequestBody.ai_generated_lines || 0,
        ai_code_ratio: mockRequestBody.ai_code_ratio || 0.0
      };

      console.log('准备创建主报告记录...');
      const newReport = await AiCodeAnalysisReports.create(reportData, { transaction });
      console.log('✅ 主报告记录创建成功，ID:', newReport.id);

      // 如果有文件详情数据，创建详情记录
      if (mockRequestBody.file_details && Array.isArray(mockRequestBody.file_details)) {
        console.log('准备创建文件详情记录，数量:', mockRequestBody.file_details.length);
        const fileDetailsData = mockRequestBody.file_details.map(detail => ({
          report_id: newReport.id,
          pr_global_id: mockRequestBody.pr_global_id,
          file_name: detail.file_name,
          file_type: detail.file_type,
          total_lines: detail.total_lines || 0,
          changed_lines: detail.changed_lines || 0,
          ai_matched_lines: detail.ai_matched_lines || 0,
          ai_code_ratio: detail.ai_code_ratio || 0.0,
          detection_method: detail.detection_method || mockRequestBody.detection_method
        }));

        await AiCodeAnalysisFileDetails.bulkCreate(fileDetailsData, { transaction });
        console.log('✅ 文件详情记录创建成功');
      }

      // 提交事务
      console.log('提交数据库事务...');
      await transaction.commit();
      console.log('✅ 数据库事务提交成功');

      console.log('\n🎉 模拟 POST 请求处理成功！');
      return { 
        success: true, 
        statusCode: 201, 
        data: { 
          id: newReport.id, 
          pr_global_id: newReport.pr_global_id 
        } 
      };

    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.log('❌ 数据库事务已回滚');
      throw error;
    }

  } catch (error) {
    console.error('❌ 模拟 POST 请求失败:', error.message);
    return { success: false, error: error.message };
  }
}

async function runAllTests() {
  console.log('🔬 开始完整测试...\n');

  // 测试1：基本功能
  const basicTest = await testNewMySQLConfig();
  
  // 测试2：POST 请求处理
  const postTest = await testPostRequest();

  console.log('\n📊 测试结果汇总:');
  console.log('- 基本功能测试:', basicTest.success ? '✅ 成功' : '❌ 失败');
  console.log('- POST 请求测试:', postTest.success ? '✅ 成功' : '❌ 失败');

  if (basicTest.success && postTest.success) {
    console.log('\n🎉 所有测试通过！可以部署到生产环境。');
    console.log('\n📝 下一步操作:');
    console.log('1. 部署: nest deploy -e test');
    console.log('2. 测试真实的 POST 请求');
    console.log('3. 验证数据是否正确保存');
  } else {
    console.log('\n❌ 部分测试失败，需要进一步调试。');
  }
}

if (require.main === module) {
  runAllTests().then(() => {
    console.log('\n🏁 测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testNewMySQLConfig, testPostRequest };
