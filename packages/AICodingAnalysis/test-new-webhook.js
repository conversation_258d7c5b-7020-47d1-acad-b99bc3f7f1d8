/**
 * 测试新 Webhook 数据结构
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysis } = require('./index.js');

async function testNewWebhook() {
  console.log('🧪 测试新 Webhook 数据结构...\n');

  const tests = [
    {
      name: '1. 新数据结构 - 完整数据',
      event: {
        extensions: {
          request: {
            method: 'POST',
            body: {
              "merge_request_id": 13079654,
              "user_name": "yaoyan03",
              "operator": "yaoyan03",
              "title": "feat: 添加AI代码分析功能",
              "description": "* feat: 添加AI代码分析功能",
              "source": {
                "name": "ai-coding-admin",
                "ssh_url": "ssh://*******************/gfe/ai-coding-admin.git",
                "namespace": "GFE"
              },
              "target": {
                "name": "ai-coding-admin",
                "ssh_url": "ssh://*******************/gfe/ai-coding-admin.git",
                "namespace": "GFE"
              },
              "user_mail": "<EMAIL>",
              "from": "refs/heads/feature/ai-analysis",
              "to": "refs/heads/master",
              "message_id": "test-message-id"
            }
          }
        }
      },
      expectedSuccess: true
    },
    {
      name: '2. 缺少 merge_request_id',
      event: {
        extensions: {
          request: {
            method: 'POST',
            body: {
              "user_name": "yaoyan03",
              "title": "test"
            }
          }
        }
      },
      expectedSuccess: false
    },
    {
      name: '3. 缺少必要字段',
      event: {
        extensions: {
          request: {
            method: 'POST',
            body: {
              "merge_request_id": 123456
              // 缺少其他必要字段
            }
          }
        }
      },
      expectedSuccess: false
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);

    try {
      const result = await AICodingAnalysis(test.event, {});

      console.log('响应状态:', result.body.success ? '成功' : '失败');
      console.log('响应消息:', result.body.message);

      if (test.expectedSuccess) {
        if (result.body.success) {
          console.log('✅ 符合预期 - 成功处理');
          console.log('merge_request_id:', result.body.merge_request_id);
          
          // 检查提取的数据
          if (result.body.data && result.body.data.webhookData) {
            const webhookData = result.body.data.webhookData;
            console.log('提取的关键字段:');
            console.log(`- user_name: ${webhookData.user_name}`);
            console.log(`- title: ${webhookData.title}`);
            console.log(`- from: ${webhookData.from}`);
            console.log(`- to: ${webhookData.to}`);
          }
          
          passedTests++;
        } else {
          console.log('❌ 不符合预期 - 应该成功但失败了');
          console.log('错误信息:', result.body.error);
        }
      } else {
        if (!result.body.success) {
          console.log('✅ 符合预期 - 正确拒绝无效数据');
          passedTests++;
        } else {
          console.log('❌ 不符合预期 - 应该失败但成功了');
        }
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
      if (!test.expectedSuccess) {
        console.log('✅ 符合预期 - 正确抛出异常');
        passedTests++;
      }
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 新 Webhook 数据结构测试成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 新 Webhook 数据结构测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testNewWebhook().then((result) => {
    if (result.success) {
      console.log('\n✅ 新数据结构验证成功！可以部署了！');
    } else {
      console.log('\n❌ 新数据结构验证失败，需要进一步调试！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testNewWebhook };
