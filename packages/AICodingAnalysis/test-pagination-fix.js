/**
 * 测试分页和排序修复
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testPaginationFix() {
  console.log('🧪 测试分页和排序修复...\n');

  const tests = [
    {
      name: '1. 基础查询 - 验证 totalCount 和 fileDetails 排序',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?page=1&pageSize=20',
            path: '/'
          }
        },
        queryStringParameters: {
          page: '1',
          pageSize: '20'
        }
      }
    },
    {
      name: '2. 分页查询 - 验证分页逻辑',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?page=1&pageSize=1',
            path: '/'
          }
        },
        queryStringParameters: {
          page: '1',
          pageSize: '1'
        }
      }
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);

    try {
      const result = await AICodingAnalysisResult(test.event, {});

      if (result.statusCode === 200 && result.body.success) {
        console.log('✅ 请求成功');
        
        const data = result.body.data;
        const pagination = data.pagination;
        
        console.log('分页信息:');
        console.log(`- 当前页: ${pagination.currentPage}`);
        console.log(`- 页大小: ${pagination.pageSize}`);
        console.log(`- 总记录数: ${pagination.totalCount}`);
        console.log(`- 总页数: ${pagination.totalPages}`);
        console.log(`- 返回记录数: ${data.list.length}`);
        
        // 验证 totalCount 是否正确（应该是主记录数，不是关联记录数）
        if (pagination.totalCount === data.list.length || pagination.totalCount === 1) {
          console.log('✅ totalCount 修复成功');
        } else {
          console.log(`❌ totalCount 仍有问题: 期望1，实际${pagination.totalCount}`);
        }
        
        // 验证 fileDetails 排序
        if (data.list.length > 0) {
          const record = data.list[0];
          if (record.fileDetails && record.fileDetails.length > 0) {
            console.log('\nfileDetails 排序验证:');
            let isSorted = true;
            let prevRatio = 1.0; // 从最高开始
            
            record.fileDetails.forEach((file, index) => {
              const currentRatio = parseFloat(file.ai_code_ratio);
              console.log(`  ${index + 1}. ${file.file_name} - AI占比: ${(currentRatio * 100).toFixed(2)}%`);
              
              if (currentRatio > prevRatio) {
                isSorted = false;
              }
              prevRatio = currentRatio;
            });
            
            if (isSorted) {
              console.log('✅ fileDetails 按 AI 占比从高到低排序正确');
            } else {
              console.log('❌ fileDetails 排序不正确');
            }
          }
        }
        
        passedTests++;
      } else {
        console.log(`❌ 请求失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
      console.error('错误详情:', error.stack);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 分页和排序修复成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 分页和排序修复失败！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testPaginationFix().then((result) => {
    if (result.success) {
      console.log('\n✅ 修复验证成功！可以部署了！');
    } else {
      console.log('\n❌ 修复验证失败，需要进一步调试！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testPaginationFix };
