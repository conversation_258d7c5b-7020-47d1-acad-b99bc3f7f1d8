/**
 * 本地测试 POST 和 GET 请求
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testPostRequest() {
  console.log('🧪 测试 POST 请求...\n');

  const mockPostEvent = {
    extensions: {
      request: {
        method: 'POST',
        url: '/',
        path: '/',
        body: {
          pr_global_id: `LOCAL_POST_TEST_${Date.now()}`,
          pr_title: '本地POST测试',
          git_repository: 'https://git.example.com/local-post-test.git',
          git_branch: 'local-post-test',
          mis_number: 'local_test_user',
          detection_method: '本地POST测试',
          analysis_timestamp: new Date().toISOString(),
          total_files: 2,
          total_lines: 200,
          changed_lines: 100,
          ai_generated_lines: 50,
          ai_code_ratio: 0.25,
          file_details: [
            {
              file_name: 'src/local-test-1.js',
              file_type: 'js',
              total_lines: 100,
              changed_lines: 50,
              ai_matched_lines: 25,
              ai_code_ratio: 0.25,
              detection_method: '本地POST测试'
            },
            {
              file_name: 'src/local-test-2.tsx',
              file_type: 'tsx',
              total_lines: 100,
              changed_lines: 50,
              ai_matched_lines: 25,
              ai_code_ratio: 0.25,
              detection_method: '本地POST测试'
            }
          ]
        }
      }
    }
  };

  try {
    const result = await AICodingAnalysisResult(mockPostEvent, {});
    
    console.log('POST 请求结果:');
    console.log('- 状态码:', result.statusCode);
    console.log('- 成功:', result.body.success);
    console.log('- 消息:', result.body.message);
    
    if (result.body.data && result.body.data.report) {
      console.log('- 报告 ID:', result.body.data.report.id);
      console.log('- PR ID:', result.body.data.report.pr_global_id);
      console.log('- 文件详情数量:', result.body.data.fileDetails ? result.body.data.fileDetails.length : 0);
    }
    
    return {
      success: result.body.success,
      reportId: result.body.data?.report?.id,
      prGlobalId: result.body.data?.report?.pr_global_id
    };
    
  } catch (error) {
    console.error('❌ POST 请求失败:', error.message);
    return { success: false, error: error.message };
  }
}

async function testGetRequest() {
  console.log('\n🧪 测试 GET 请求...\n');

  const mockGetEvent = {
    extensions: {
      request: {
        method: 'GET',
        url: '/?page=1&pageSize=5',
        path: '/'
      }
    },
    queryStringParameters: {
      page: '1',
      pageSize: '5'
    }
  };

  try {
    const result = await AICodingAnalysisResult(mockGetEvent, {});
    
    console.log('GET 请求结果:');
    console.log('- 状态码:', result.statusCode);
    console.log('- 成功:', result.body.success);
    console.log('- 消息:', result.body.message);
    
    if (result.body.data) {
      console.log('- 总记录数:', result.body.data.pagination.totalCount);
      console.log('- 当前页记录数:', result.body.data.list.length);
      console.log('- 当前页:', result.body.data.pagination.currentPage);
      console.log('- 总页数:', result.body.data.pagination.totalPages);
      
      if (result.body.data.list.length > 0) {
        console.log('\n最新的记录:');
        const latest = result.body.data.list[0];
        console.log('- ID:', latest.id);
        console.log('- PR ID:', latest.pr_global_id);
        console.log('- 标题:', latest.pr_title);
        console.log('- AI占比:', latest.ai_code_ratio);
        console.log('- 文件详情数量:', latest.fileDetails ? latest.fileDetails.length : 0);
      }
    }
    
    return {
      success: result.body.success,
      totalCount: result.body.data?.pagination?.totalCount || 0,
      listLength: result.body.data?.list?.length || 0
    };
    
  } catch (error) {
    console.error('❌ GET 请求失败:', error.message);
    return { success: false, error: error.message };
  }
}

async function testGetWithFilters() {
  console.log('\n🧪 测试带过滤条件的 GET 请求...\n');

  const mockGetEventWithFilters = {
    extensions: {
      request: {
        method: 'GET',
        url: '/?page=1&pageSize=10&misNumber=local_test',
        path: '/'
      }
    },
    queryStringParameters: {
      page: '1',
      pageSize: '10',
      misNumber: 'local_test'
    }
  };

  try {
    const result = await AICodingAnalysisResult(mockGetEventWithFilters, {});
    
    console.log('带过滤条件的 GET 请求结果:');
    console.log('- 状态码:', result.statusCode);
    console.log('- 成功:', result.body.success);
    console.log('- 过滤后记录数:', result.body.data?.pagination?.totalCount || 0);
    console.log('- 返回记录数:', result.body.data?.list?.length || 0);
    
    return {
      success: result.body.success,
      filteredCount: result.body.data?.pagination?.totalCount || 0
    };
    
  } catch (error) {
    console.error('❌ 带过滤条件的 GET 请求失败:', error.message);
    return { success: false, error: error.message };
  }
}

async function runAllTests() {
  console.log('🚀 开始本地 POST/GET 测试...\n');
  console.log('=' * 60);

  let allTestsPass = true;

  // 1. 测试 POST 请求
  console.log('1️⃣ POST 请求测试');
  console.log('-' * 30);
  const postResult = await testPostRequest();
  
  if (postResult.success) {
    console.log('✅ POST 请求测试通过');
  } else {
    console.log('❌ POST 请求测试失败');
    allTestsPass = false;
  }

  // 2. 测试 GET 请求
  console.log('\n2️⃣ GET 请求测试');
  console.log('-' * 30);
  const getResult = await testGetRequest();
  
  if (getResult.success && getResult.totalCount > 0) {
    console.log('✅ GET 请求测试通过');
  } else {
    console.log('❌ GET 请求测试失败');
    allTestsPass = false;
  }

  // 3. 测试带过滤条件的 GET 请求
  console.log('\n3️⃣ 带过滤条件的 GET 请求测试');
  console.log('-' * 30);
  const filterResult = await testGetWithFilters();
  
  if (filterResult.success) {
    console.log('✅ 带过滤条件的 GET 请求测试通过');
  } else {
    console.log('❌ 带过滤条件的 GET 请求测试失败');
    allTestsPass = false;
  }

  // 最终结果
  console.log('\n' + '=' * 60);
  if (allTestsPass) {
    console.log('🎉 所有本地测试通过！可以部署到生产环境！');
    
    console.log('\n📊 测试总结:');
    console.log(`✅ POST 请求: ${postResult.success ? '成功' : '失败'}`);
    console.log(`✅ GET 请求: ${getResult.success ? '成功' : '失败'} (总记录: ${getResult.totalCount})`);
    console.log(`✅ 过滤查询: ${filterResult.success ? '成功' : '失败'} (过滤记录: ${filterResult.filteredCount})`);
    
    console.log('\n🚀 部署命令:');
    console.log('nest deploy -e test');
    
  } else {
    console.log('❌ 部分测试失败，请检查问题后再部署！');
  }
  console.log('=' * 60);

  return allTestsPass;
}

if (require.main === module) {
  runAllTests().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testPostRequest, testGetRequest, testGetWithFilters, runAllTests };
