/**
 * 生产环境测试脚本
 */

// 模拟生产环境
delete process.env.NODE_ENV;
delete process.env.USER;

const { AICodingAnalysisResult } = require('./index.js');

// 模拟 GET 请求事件
const mockGetEvent = {
  httpMethod: 'GET',
  queryStringParameters: {
    page: '1',
    pageSize: '10'
  }
};

// 模拟 POST 请求事件
const mockPostEvent = {
  httpMethod: 'POST',
  body: JSON.stringify({
    pr_global_id: 'PR-2024-PROD-001',
    pr_title: '生产环境测试PR',
    git_repository: 'https://git.example.com/prod-test.git',
    git_branch: 'feature/prod-test',
    mis_number: 'produser',
    detection_method: 'AI_DETECTION',
    analysis_timestamp: new Date().toISOString(),
    total_files: 3,
    total_lines: 500,
    changed_lines: 100,
    ai_generated_lines: 25,
    ai_code_ratio: 0.25
  })
};

async function testProductionAPI() {
  console.log('开始测试生产环境 AICodingAnalysisResult API...\n');
  
  try {
    // 等待初始化
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('=== 测试 GET 请求 ===');
    const getResult = await AICodingAnalysisResult(mockGetEvent, {});
    console.log('GET 结果:', JSON.stringify(getResult, null, 2));
    
    console.log('\n=== 测试 POST 请求 ===');
    const postResult = await AICodingAnalysisResult(mockPostEvent, {});
    console.log('POST 结果:', JSON.stringify(postResult, null, 2));
    
    console.log('\n✅ 生产环境测试完成');
    
  } catch (error) {
    console.error('❌ 生产环境测试失败:', error);
  }
}

// 运行测试
testProductionAPI();
