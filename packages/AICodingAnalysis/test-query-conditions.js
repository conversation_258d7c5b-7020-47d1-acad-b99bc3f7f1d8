/**
 * 测试查询条件是否正常工作
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testQueryConditions() {
  console.log('🧪 测试查询条件...\n');

  const tests = [
    {
      name: '1. 基本查询（无条件）',
      params: {
        page: '1',
        pageSize: '5'
      }
    },
    {
      name: '2. 按 misNumber 查询',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'local_test'
      }
    },
    {
      name: '3. 按 prId 查询',
      params: {
        page: '1',
        pageSize: '10',
        prId: 'LOCAL_POST'
      }
    },
    {
      name: '4. 按 gitRepository 查询',
      params: {
        page: '1',
        pageSize: '10',
        gitRepository: 'local-post-test'
      }
    },
    {
      name: '5. 按 gitBranch 查询',
      params: {
        page: '1',
        pageSize: '10',
        gitBranch: 'local-post-test'
      }
    },
    {
      name: '6. 按时间范围查询',
      params: {
        page: '1',
        pageSize: '10',
        startTime: '2025-05-24T00:00:00.000Z',
        endTime: '2025-05-25T23:59:59.999Z'
      }
    },
    {
      name: '7. 按开始时间查询',
      params: {
        page: '1',
        pageSize: '10',
        startTime: '2025-05-24T16:00:00.000Z'
      }
    },
    {
      name: '8. 按结束时间查询',
      params: {
        page: '1',
        pageSize: '10',
        endTime: '2025-05-25T00:30:00.000Z'
      }
    },
    {
      name: '9. 组合查询（misNumber + gitBranch）',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'test',
        gitBranch: 'test'
      }
    },
    {
      name: '10. 不存在的条件查询',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'NOT_EXISTS_USER_12345'
      }
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('查询参数:', test.params);

    try {
      const mockEvent = {
        extensions: {
          request: {
            method: 'GET',
            url: `/?${new URLSearchParams(test.params).toString()}`,
            path: '/'
          }
        },
        queryStringParameters: test.params
      };

      const result = await AICodingAnalysisResult(mockEvent, {});

      if (result.statusCode === 200 && result.body.success) {
        const data = result.body.data;
        console.log(`✅ 成功 - 总记录: ${data.pagination.totalCount}, 返回: ${data.list.length}`);
        
        if (data.list.length > 0) {
          const sample = data.list[0];
          console.log(`   示例记录: ID=${sample.id}, PR=${sample.pr_global_id}, MIS=${sample.mis_number}`);
        }
        
        passedTests++;
      } else {
        console.log(`❌ 失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
        console.log(`   错误信息: ${result.body.message}`);
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有查询条件测试通过！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 部分查询条件测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

// 额外测试：验证查询条件的 SQL 生成
async function testSQLGeneration() {
  console.log('\n🔍 验证查询条件的 SQL 生成...\n');

  try {
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    await modelsModule.initializeDatabase();
    
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;

    // 测试各种查询条件
    const testConditions = [
      {
        name: 'misNumber LIKE 查询',
        where: {
          mis_number: {
            [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: '%test%'
          }
        }
      },
      {
        name: 'prId LIKE 查询',
        where: {
          pr_global_id: {
            [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: '%LOCAL%'
          }
        }
      },
      {
        name: '时间范围查询',
        where: {
          analysis_timestamp: {
            [AiCodeAnalysisReports.sequelize.Sequelize.Op.between]: [
              '2025-05-24T00:00:00.000Z',
              '2025-05-25T23:59:59.999Z'
            ]
          }
        }
      }
    ];

    for (const condition of testConditions) {
      console.log(`${condition.name}:`);
      try {
        const result = await AiCodeAnalysisReports.findAndCountAll({
          where: condition.where,
          limit: 1
        });
        console.log(`✅ 成功 - 找到 ${result.count} 条记录`);
      } catch (error) {
        console.log(`❌ 失败 - ${error.message}`);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('❌ SQL 生成测试失败:', error.message);
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  (async () => {
    const queryResult = await testQueryConditions();
    const sqlResult = await testSQLGeneration();
    
    console.log('\n' + '='.repeat(60));
    if (queryResult.success && sqlResult.success) {
      console.log('🎉 所有测试通过！查询条件工作正常！');
      process.exit(0);
    } else {
      console.log('❌ 测试失败，需要修复查询条件！');
      process.exit(1);
    }
  })().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testQueryConditions, testSQLGeneration };
