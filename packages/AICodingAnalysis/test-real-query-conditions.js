/**
 * 使用真实数据测试查询条件
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testRealQueryConditions() {
  console.log('🧪 使用真实数据测试查询条件...\n');

  const tests = [
    {
      name: '1. 基本查询（无条件）',
      params: {
        page: '1',
        pageSize: '10'
      },
      expected: '应该返回所有记录'
    },
    {
      name: '2. 按真实 misNumber 查询',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'yaoyan03'
      },
      expected: '应该找到 yaoyan03 的记录'
    },
    {
      name: '3. 按 misNumber 模糊查询',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'yaoyan'
      },
      expected: '应该找到包含 yaoyan 的记录'
    },
    {
      name: '4. 按真实 prId 查询',
      params: {
        page: '1',
        pageSize: '10',
        prId: '13073644'
      },
      expected: '应该找到 PR 13073644'
    },
    {
      name: '5. 按 prId 模糊查询',
      params: {
        page: '1',
        pageSize: '10',
        prId: '1307'
      },
      expected: '应该找到包含 1307 的 PR'
    },
    {
      name: '6. 按真实 gitRepository 查询',
      params: {
        page: '1',
        pageSize: '10',
        gitRepository: 'ai-coding-admin'
      },
      expected: '应该找到 ai-coding-admin 仓库'
    },
    {
      name: '7. 按真实 gitBranch 查询',
      params: {
        page: '1',
        pageSize: '10',
        gitBranch: 'CreatePage-YAOYAN'
      },
      expected: '应该找到 CreatePage-YAOYAN 分支'
    },
    {
      name: '8. 按真实时间范围查询',
      params: {
        page: '1',
        pageSize: '10',
        startTime: '2025-05-24T16:00:00.000Z',
        endTime: '2025-05-24T17:00:00.000Z'
      },
      expected: '应该找到 2025-05-24 16-17点的记录'
    },
    {
      name: '9. 组合查询（真实数据）',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'yaoyan',
        gitBranch: 'CreatePage'
      },
      expected: '应该找到 yaoyan 用户的 CreatePage 分支'
    },
    {
      name: '10. 不存在的条件查询',
      params: {
        page: '1',
        pageSize: '10',
        misNumber: 'NOT_EXISTS_USER'
      },
      expected: '应该返回 0 条记录'
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('查询参数:', test.params);
    console.log('预期结果:', test.expected);

    try {
      const mockEvent = {
        extensions: {
          request: {
            method: 'GET',
            url: `/?${new URLSearchParams(test.params).toString()}`,
            path: '/'
          }
        },
        queryStringParameters: test.params
      };

      const result = await AICodingAnalysisResult(mockEvent, {});

      if (result.statusCode === 200 && result.body.success) {
        const data = result.body.data;
        console.log(`✅ 成功 - 总记录: ${data.pagination.totalCount}, 返回: ${data.list.length}`);
        
        if (data.list.length > 0) {
          const sample = data.list[0];
          console.log(`   示例记录: ID=${sample.id}, PR=${sample.pr_global_id}, MIS=${sample.mis_number}`);
          console.log(`   仓库: ${sample.git_repository}`);
          console.log(`   分支: ${sample.git_branch}`);
          console.log(`   时间: ${sample.analysis_timestamp}`);
        }
        
        // 验证查询结果是否符合预期
        let isExpected = false;
        if (test.params.misNumber && data.list.length > 0) {
          isExpected = data.list[0].mis_number && data.list[0].mis_number.includes(test.params.misNumber);
        } else if (test.params.prId && data.list.length > 0) {
          isExpected = data.list[0].pr_global_id && data.list[0].pr_global_id.includes(test.params.prId);
        } else if (test.params.gitRepository && data.list.length > 0) {
          isExpected = data.list[0].git_repository && data.list[0].git_repository.includes(test.params.gitRepository);
        } else if (test.params.gitBranch && data.list.length > 0) {
          isExpected = data.list[0].git_branch && data.list[0].git_branch.includes(test.params.gitBranch);
        } else if (test.params.misNumber === 'NOT_EXISTS_USER') {
          isExpected = data.list.length === 0;
        } else {
          isExpected = true; // 基本查询或时间查询
        }
        
        if (isExpected) {
          console.log('   ✅ 查询结果符合预期');
          passedTests++;
        } else {
          console.log('   ❌ 查询结果不符合预期');
        }
        
      } else {
        console.log(`❌ 失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
        console.log(`   错误信息: ${result.body.message}`);
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有查询条件测试通过！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ 部分查询条件测试失败！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testRealQueryConditions().then((result) => {
    if (result.success) {
      console.log('\n✅ 查询条件功能正常！');
      console.log('\n📝 总结:');
      console.log('- misNumber: 支持模糊查询 (LIKE %value%)');
      console.log('- prId: 支持模糊查询 (LIKE %value%)');
      console.log('- gitRepository: 支持模糊查询 (LIKE %value%)');
      console.log('- gitBranch: 支持模糊查询 (LIKE %value%)');
      console.log('- startTime/endTime: 支持时间范围查询');
      console.log('- 组合查询: 支持多条件同时查询');
    } else {
      console.log('\n❌ 查询条件存在问题，需要修复！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testRealQueryConditions };
