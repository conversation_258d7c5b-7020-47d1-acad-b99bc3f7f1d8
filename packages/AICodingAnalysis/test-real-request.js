/**
 * 模拟真实的 POST 请求测试
 */

// 设置环境变量
process.env.NODE_ENV = 'development';

const { AICodingAnalysisResult } = require('./index.js');

// 模拟你的真实 POST 请求
const realPostEvent = {
  body: JSON.stringify({
    "pr_global_id": "13073502",
    "pr_title": "【不要合并，只是测试webhook】",
    "git_repository": "ssh://*******************/dzfe/medical-home-page.git",
    "git_branch": "feature/fedo-213908",
    "mis_number": "yaoyan03",
    "detection_method": "行级匹配",
    "analysis_timestamp": "2025-05-24T12:37:01.729Z",
    "total_files": 5,
    "total_lines": 2141,
    "changed_lines": 2141,
    "ai_generated_lines": 971,
    "ai_code_ratio": 0.4535263895375993,
    "file_details": [
      {
        "file_name": "src/components/layout/index.tsx",
        "file_type": "tsx",
        "total_lines": 30,
        "changed_lines": 30,
        "ai_matched_lines": 5,
        "ai_code_ratio": 0.16666666666666666,
        "detection_method": "行级匹配"
      },
      {
        "file_name": "src/pages/main/styles/app.module.scss",
        "file_type": "scss",
        "total_lines": 290,
        "changed_lines": 290,
        "ai_matched_lines": 223,
        "ai_code_ratio": 0.7689655172413793,
        "detection_method": "行级匹配"
      },
      {
        "file_name": "src/pages/main/views/ai-analysis/index.tsx",
        "file_type": "tsx",
        "total_lines": 566,
        "changed_lines": 566,
        "ai_matched_lines": 128,
        "ai_code_ratio": 0.22614840989399293,
        "detection_method": "行级匹配"
      },
      {
        "file_name": "src/pages/main/views/data-management/index.tsx",
        "file_type": "tsx",
        "total_lines": 573,
        "changed_lines": 573,
        "ai_matched_lines": 171,
        "ai_code_ratio": 0.29842931937172773,
        "detection_method": "行级匹配"
      },
      {
        "file_name": "src/pages/main/views/home/<USER>",
        "file_type": "tsx",
        "total_lines": 498,
        "changed_lines": 498,
        "ai_matched_lines": 444,
        "ai_code_ratio": 0.891566265060241,
        "detection_method": "行级匹配"
      }
    ]
  })
};

// 模拟不同的 event 结构来测试 HTTP 方法检测
const testCases = [
  {
    name: "带 httpMethod 的事件",
    event: {
      ...realPostEvent,
      httpMethod: 'POST'
    }
  },
  {
    name: "带 method 的事件",
    event: {
      ...realPostEvent,
      method: 'POST'
    }
  },
  {
    name: "带 extensions.request.method 的事件",
    event: {
      ...realPostEvent,
      extensions: {
        request: {
          method: 'POST',
          body: JSON.parse(realPostEvent.body)
        }
      }
    }
  },
  {
    name: "只有 body 的事件（自动检测）",
    event: realPostEvent
  }
];

async function testRealRequest() {
  console.log('开始测试真实 POST 请求...\n');
  
  for (const testCase of testCases) {
    console.log(`=== ${testCase.name} ===`);
    
    try {
      const result = await AICodingAnalysisResult(testCase.event, {});
      console.log('结果:', JSON.stringify(result, null, 2));
      console.log('');
    } catch (error) {
      console.error('测试失败:', error);
      console.log('');
    }
  }
}

// 运行测试
testRealRequest();
