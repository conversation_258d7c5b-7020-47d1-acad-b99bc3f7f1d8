/**
 * 简化的数据库连接测试
 * 测试常见的 refKey 格式
 */

const {
  ZebraSequelizeFactory
} = require('@bfe/zebra-proxy-sequelize');

// 常见的 refKey 格式
const refKeys = [
  'dzfrontendshanghai_swift_test',
  'dzfrontendshanghai_swift_test.man',
  'dzfrontendshanghai.swift.test',
  'dzfrontendshanghai_swift_test.db',
  'dzfrontendshanghai_swift',
  'dzfrontendshanghai.swift',
  'dzfrontendshanghai_test',
  'dzfrontendshanghai.test',
  'swift_test',
  'swift.test'
];

async function testRefKey(refKey) {
  console.log(`\n🔍 测试 refKey: ${refKey}`);
  
  try {
    const options = {
      appName: 'com.sankuai.dzufebiz.manage',
      refKey: refKey,
      dbType: "zebra-proxy",
      env: "test",
      pool: {
        max: 2,
        min: 1,
      },
      dialectOptions: {
        dateStrings: true,
      }
    };

    console.log('创建连接...');
    const sequelize = ZebraSequelizeFactory.create(options);
    
    console.log('测试连接...');
    await sequelize.authenticate();
    
    console.log('✅ 成功！');
    
    // 获取数据库信息
    try {
      const [results] = await sequelize.query('SELECT DATABASE() as db, VERSION() as version');
      console.log('📊 数据库信息:', {
        database: results[0]?.db,
        version: results[0]?.version?.substring(0, 20) + '...'
      });
    } catch (e) {
      console.log('⚠️  无法获取数据库信息');
    }
    
    await sequelize.close();
    return { success: true, refKey };
    
  } catch (error) {
    console.log('❌ 失败:', error.message.substring(0, 100) + '...');
    return { success: false, refKey, error: error.message };
  }
}

async function findWorkingRefKey() {
  console.log('🚀 开始寻找可用的 refKey...\n');
  
  const results = [];
  
  for (const refKey of refKeys) {
    const result = await testRefKey(refKey);
    results.push(result);
    
    if (result.success) {
      console.log(`\n🎉 找到可用的 refKey: ${refKey}`);
      break; // 找到第一个可用的就停止
    }
    
    // 等待一下再测试下一个
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n=== 测试结果 ===');
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  if (successful.length > 0) {
    console.log('✅ 可用的 refKey:');
    successful.forEach(r => console.log(`  - ${r.refKey}`));
    
    console.log('\n📝 建议更新配置:');
    console.log(`const refKey = '${successful[0].refKey}';`);
    
  } else {
    console.log('❌ 没有找到可用的 refKey');
    console.log('\n🔍 常见错误类型:');
    
    const errorTypes = {};
    failed.forEach(r => {
      const errorType = r.error.includes('deprecated') ? 'deprecated' :
                       r.error.includes('Invalid environment') ? 'env_error' :
                       r.error.includes('获取lion配置信息失败') ? 'config_not_found' :
                       'other';
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });
    
    Object.entries(errorTypes).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} 次`);
    });
    
    console.log('\n💡 建议:');
    console.log('1. 联系数据库管理员确认正确的 refKey');
    console.log('2. 检查应用是否有数据库访问权限');
    console.log('3. 确认测试环境数据库是否存在');
  }
}

// 运行测试
if (require.main === module) {
  findWorkingRefKey().then(() => {
    console.log('\n🏁 测试完成');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  });
}

module.exports = { findWorkingRefKey, testRefKey };
