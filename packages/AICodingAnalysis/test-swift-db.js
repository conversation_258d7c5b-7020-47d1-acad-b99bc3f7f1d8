/**
 * 测试连接到 swift 数据库
 */

const mysql = require('mysql2/promise');

// 正确的数据库配置
const dbConfig = {
  host: '*************',
  port: 5002,
  user: 'rds_Analysis',
  password: 'rouNBgzs(14%d&',
  database: 'swift', // 使用用户有权限的数据库
  connectTimeout: 10000
};

async function testSwiftDatabase() {
  console.log('🚀 测试连接到 swift 数据库...\n');

  console.log('📋 连接配置:');
  console.log({
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    password: '***' + dbConfig.password.slice(-4),
    database: dbConfig.database
  });

  let connection;

  try {
    console.log('\n🔗 正在建立连接...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功！');

    // 测试基本查询
    console.log('\n📊 获取数据库信息...');
    const [rows] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as version, NOW() as current_time_val');
    console.log('数据库信息:', rows[0]);

    // 查看所有表
    console.log('\n📋 查看数据库中的表...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`数据库中共有 ${tables.length} 个表:`);
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // 查找 AI 相关的表
    console.log('\n🔍 查找 AI 相关的表...');
    const [aiTables] = await connection.execute("SHOW TABLES LIKE '%ai%'");
    if (aiTables.length > 0) {
      console.log('找到 AI 相关的表:');
      aiTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('没有找到 AI 相关的表');
    }

    // 查找 analysis 相关的表
    console.log('\n🔍 查找 analysis 相关的表...');
    const [analysisTables] = await connection.execute("SHOW TABLES LIKE '%analysis%'");
    if (analysisTables.length > 0) {
      console.log('找到 analysis 相关的表:');
      analysisTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('没有找到 analysis 相关的表');
    }

    // 测试创建我们需要的表
    console.log('\n🧪 测试创建 AI 代码分析表...');

    const createMainTableSQL = `
      CREATE TABLE IF NOT EXISTS ai_code_analysis_reports (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        pr_global_id VARCHAR(50) NOT NULL UNIQUE COMMENT 'PR全局ID',
        pr_title VARCHAR(500) NOT NULL COMMENT 'PR标题',
        git_repository VARCHAR(500) NOT NULL COMMENT 'Git仓库地址',
        git_branch VARCHAR(200) NOT NULL COMMENT 'Git分支名',
        mis_number VARCHAR(50) COMMENT 'MIS号/作者',
        detection_method VARCHAR(50) NOT NULL COMMENT '检测方法',
        analysis_timestamp DATETIME NOT NULL COMMENT '分析时间戳',
        total_files INT NOT NULL DEFAULT 0 COMMENT '总文件数',
        total_lines INT NOT NULL DEFAULT 0 COMMENT '总行数',
        changed_lines INT NOT NULL DEFAULT 0 COMMENT '变更行数',
        ai_generated_lines INT NOT NULL DEFAULT 0 COMMENT 'AI生成行数',
        ai_code_ratio DECIMAL(10,8) NOT NULL DEFAULT 0.00000000 COMMENT 'AI代码占比',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_git_repository (git_repository(255)),
        INDEX idx_git_branch (git_branch),
        INDEX idx_mis_number (mis_number),
        INDEX idx_analysis_timestamp (analysis_timestamp),
        INDEX idx_ai_code_ratio (ai_code_ratio)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI代码分析报告主表'
    `;

    const createDetailTableSQL = `
      CREATE TABLE IF NOT EXISTS ai_code_analysis_file_details (
        id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
        report_id BIGINT NOT NULL COMMENT '报告ID，关联ai_code_analysis_reports.id',
        pr_global_id VARCHAR(50) NOT NULL COMMENT 'PR全局ID，冗余字段便于查询',
        file_name VARCHAR(1000) NOT NULL COMMENT '文件名/路径',
        file_type VARCHAR(20) NOT NULL COMMENT '文件类型',
        total_lines INT NOT NULL DEFAULT 0 COMMENT '文件总行数',
        changed_lines INT NOT NULL DEFAULT 0 COMMENT '文件变更行数',
        ai_matched_lines INT NOT NULL DEFAULT 0 COMMENT 'AI匹配行数',
        ai_code_ratio DECIMAL(10,8) NOT NULL DEFAULT 0.00000000 COMMENT '文件AI代码占比',
        detection_method VARCHAR(50) NOT NULL COMMENT '文件检测方法',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_report_id (report_id),
        INDEX idx_pr_global_id (pr_global_id),
        INDEX idx_file_type (file_type),
        INDEX idx_ai_code_ratio (ai_code_ratio),
        INDEX idx_detection_method (detection_method)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI代码分析文件详情表'
    `;

    try {
      await connection.execute(createMainTableSQL);
      console.log('✅ 主表创建成功');

      await connection.execute(createDetailTableSQL);
      console.log('✅ 详情表创建成功');

      // 测试插入数据
      console.log('\n🧪 测试插入数据...');
      const testData = {
        pr_global_id: `TEST_${Date.now()}`,
        pr_title: '测试PR',
        git_repository: 'https://git.example.com/test.git',
        git_branch: 'test-branch',
        mis_number: 'test_user',
        detection_method: '测试方法',
        analysis_timestamp: new Date(),
        total_files: 1,
        total_lines: 100,
        changed_lines: 50,
        ai_generated_lines: 25,
        ai_code_ratio: 0.25
      };

      const [result] = await connection.execute(`
        INSERT INTO ai_code_analysis_reports
        (pr_global_id, pr_title, git_repository, git_branch, mis_number, detection_method, analysis_timestamp, total_files, total_lines, changed_lines, ai_generated_lines, ai_code_ratio)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testData.pr_global_id, testData.pr_title, testData.git_repository, testData.git_branch,
        testData.mis_number, testData.detection_method, testData.analysis_timestamp,
        testData.total_files, testData.total_lines, testData.changed_lines,
        testData.ai_generated_lines, testData.ai_code_ratio
      ]);

      console.log('✅ 数据插入成功，ID:', result.insertId);

      // 测试查询数据
      const [queryResult] = await connection.execute('SELECT * FROM ai_code_analysis_reports WHERE id = ?', [result.insertId]);
      console.log('✅ 数据查询成功:', queryResult[0].pr_global_id);

      // 清理测试数据
      await connection.execute('DELETE FROM ai_code_analysis_reports WHERE id = ?', [result.insertId]);
      console.log('✅ 测试数据清理完成');

    } catch (tableError) {
      console.log('❌ 表操作失败:', tableError.message);
    }

    console.log('\n🎉 swift 数据库测试完成！');
    console.log('\n📝 建议的配置:');
    console.log(`
// 更新 AICodingAnalysisModels.js 中的配置
const mysql = require('mysql2/promise');

const dbConfig = {
  host: '${dbConfig.host}',
  port: ${dbConfig.port},
  user: '${dbConfig.user}',
  password: '${dbConfig.password}',
  database: '${dbConfig.database}',
  pool: {
    max: 20,
    min: 1
  }
};
    `);

    return { success: true, database: 'swift' };

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误详情:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState
    });
    return { success: false, error: error.message };

  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

if (require.main === module) {
  testSwiftDatabase().then((result) => {
    if (result.success) {
      console.log('\n✅ 测试成功！可以使用 swift 数据库');
    } else {
      console.log('\n❌ 测试失败');
    }
    process.exit(0);
  }).catch(error => {
    console.error('测试出错:', error);
    process.exit(1);
  });
}

module.exports = { testSwiftDatabase };
