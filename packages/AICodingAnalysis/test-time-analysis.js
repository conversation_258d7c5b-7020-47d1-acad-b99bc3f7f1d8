/**
 * 测试时间数据分析
 */

// 模拟生产环境
process.env.USER = 'root';

async function testTimeAnalysis() {
  console.log('🧪 测试时间数据分析...\n');

  try {
    const dashboardModelsModule = require('./models/DashboardModels.js');
    await dashboardModelsModule.initializeDashboardDatabase();
    
    const DashboardHistoricalRecords = dashboardModelsModule.DashboardHistoricalRecords;
    
    console.log('1. 分析 generation_timestamp 字段数据:');
    
    // 查看时间戳范围
    const timeRange = await DashboardHistoricalRecords.findOne({
      attributes: [
        [DashboardHistoricalRecords.sequelize.fn('MIN', DashboardHistoricalRecords.sequelize.col('generation_timestamp')), 'minTime'],
        [DashboardHistoricalRecords.sequelize.fn('MAX', DashboardHistoricalRecords.sequelize.col('generation_timestamp')), 'maxTime'],
        [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.col('generation_timestamp')), 'countWithTime']
      ],
      where: {
        generation_timestamp: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null }
      },
      raw: true
    });
    
    console.log('时间戳统计:');
    console.log(`- 最早时间: ${timeRange.minTime}`);
    console.log(`- 最晚时间: ${timeRange.maxTime}`);
    console.log(`- 有时间戳的记录数: ${timeRange.countWithTime}`);
    
    // 查看最近的记录
    const recentRecords = await DashboardHistoricalRecords.findAll({
      attributes: ['git_username', 'generation_timestamp'],
      where: {
        generation_timestamp: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null },
        git_username: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null }
      },
      order: [['generation_timestamp', 'DESC']],
      limit: 5,
      raw: true
    });
    
    console.log('\n最近的记录:');
    recentRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.git_username} - ${record.generation_timestamp}`);
    });
    
    // 测试不同时间范围的统计
    const now = new Date();
    const timeRanges = [
      { name: '最近1天', days: 1 },
      { name: '最近7天', days: 7 },
      { name: '最近30天', days: 30 },
      { name: '最近90天', days: 90 },
      { name: '最近365天', days: 365 }
    ];
    
    console.log('\n不同时间范围的活跃开发者统计:');
    for (const range of timeRanges) {
      const startTime = new Date(now.getTime() - range.days * 24 * 60 * 60 * 1000);
      
      const count = await DashboardHistoricalRecords.findOne({
        attributes: [
          [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.fn('DISTINCT', DashboardHistoricalRecords.sequelize.col('git_username'))), 'count']
        ],
        where: {
          git_username: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null },
          generation_timestamp: {
            [DashboardHistoricalRecords.sequelize.Sequelize.Op.between]: [startTime, now]
          }
        },
        raw: true
      });
      
      console.log(`- ${range.name}: ${count.count} 人`);
    }
    
    // 测试无时间限制的统计
    console.log('\n无时间限制的统计:');
    const totalUsers = await DashboardHistoricalRecords.findOne({
      attributes: [
        [DashboardHistoricalRecords.sequelize.fn('COUNT', DashboardHistoricalRecords.sequelize.fn('DISTINCT', DashboardHistoricalRecords.sequelize.col('git_username'))), 'totalUsers']
      ],
      where: {
        git_username: { [DashboardHistoricalRecords.sequelize.Sequelize.Op.ne]: null }
      },
      raw: true
    });
    
    console.log(`- 总用户数: ${totalUsers.totalUsers} 人`);
    
    // 建议的修复方案
    console.log('\n📋 建议的修复方案:');
    if (parseInt(timeRange.countWithTime) === 0) {
      console.log('❌ generation_timestamp 字段全部为 NULL');
      console.log('建议: 使用无时间限制的统计或回退到主表统计');
    } else if (parseInt(totalUsers.totalUsers) > 0) {
      console.log('✅ 有用户数据，但可能时间范围过窄');
      console.log('建议: 扩大时间范围或使用无时间限制的统计');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 时间数据分析失败:', error.message);
    return false;
  }
}

if (require.main === module) {
  testTimeAnalysis().then((result) => {
    if (result) {
      console.log('\n✅ 时间数据分析完成！');
    } else {
      console.log('\n❌ 时间数据分析失败！');
    }
    process.exit(result ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testTimeAnalysis };
