/**
 * 测试 URL 参数解析修复
 */

// 模拟生产环境
process.env.USER = 'root';

const { AICodingAnalysisResult } = require('./index.js');

async function testUrlParsing() {
  console.log('🧪 测试 URL 参数解析修复...\n');

  const tests = [
    {
      name: '1. 模拟线上环境 - 只有 URL，没有 queryStringParameters',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?page=1&pageSize=20&gitBranch=4324234&yodaReady=h5',
            path: '/'
          }
        }
        // 注意：没有 queryStringParameters
      },
      expectedParams: {
        page: '1',
        pageSize: '20',
        gitBranch: '4324234'
      }
    },
    {
      name: '2. 模拟线上环境 - 多个查询参数',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?page=1&pageSize=10&misNumber=yaoyan&gitBranch=CreatePage&prId=13073644',
            path: '/'
          }
        }
      },
      expectedParams: {
        page: '1',
        pageSize: '10',
        misNumber: 'yaoyan',
        gitBranch: 'CreatePage',
        prId: '13073644'
      }
    },
    {
      name: '3. 模拟线上环境 - 时间范围查询',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?startTime=2025-05-24T00:00:00.000Z&endTime=2025-05-26T23:59:59.999Z',
            path: '/'
          }
        }
      },
      expectedParams: {
        startTime: '2025-05-24T00:00:00.000Z',
        endTime: '2025-05-26T23:59:59.999Z'
      }
    },
    {
      name: '4. 正常情况 - 有 queryStringParameters',
      event: {
        extensions: {
          request: {
            method: 'GET',
            url: '/?gitBranch=test',
            path: '/'
          }
        },
        queryStringParameters: {
          gitBranch: 'test'
        }
      },
      expectedParams: {
        gitBranch: 'test'
      }
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    console.log(`${test.name}:`);
    console.log('输入 URL:', test.event.extensions.request.url);
    console.log('输入 queryStringParameters:', test.event.queryStringParameters || '无');

    try {
      const result = await AICodingAnalysisResult(test.event, {});

      if (result.statusCode === 200 && result.body.success) {
        console.log('✅ 请求成功');
        
        // 检查是否正确解析了参数（通过查询结果判断）
        const data = result.body.data;
        console.log(`结果: 总记录=${data.pagination.totalCount}, 返回=${data.list.length}`);
        
        // 对于有 gitBranch 参数的测试，验证是否正确过滤
        if (test.expectedParams.gitBranch) {
          if (test.expectedParams.gitBranch === '4324234') {
            // 应该返回空结果
            if (data.pagination.totalCount === 0) {
              console.log('✅ 参数解析正确 - 正确过滤了不存在的分支');
              passedTests++;
            } else {
              console.log('❌ 参数解析失败 - 应该返回空结果');
            }
          } else if (test.expectedParams.gitBranch === 'CreatePage') {
            // 应该有结果
            if (data.pagination.totalCount > 0) {
              console.log('✅ 参数解析正确 - 找到了匹配的分支');
              passedTests++;
            } else {
              console.log('❌ 参数解析失败 - 应该找到匹配的记录');
            }
          } else {
            console.log('✅ 请求处理正常');
            passedTests++;
          }
        } else {
          console.log('✅ 请求处理正常');
          passedTests++;
        }
      } else {
        console.log(`❌ 请求失败 - 状态码: ${result.statusCode}, 成功: ${result.body.success}`);
      }
    } catch (error) {
      console.log(`❌ 异常 - ${error.message}`);
    }

    console.log(''); // 空行分隔
  }

  // 总结
  console.log('='.repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 URL 参数解析修复成功！');
    return { success: true, passedTests, totalTests };
  } else {
    console.log('❌ URL 参数解析仍有问题！');
    return { success: false, passedTests, totalTests };
  }
}

if (require.main === module) {
  testUrlParsing().then((result) => {
    if (result.success) {
      console.log('\n✅ 修复验证成功！可以部署了！');
    } else {
      console.log('\n❌ 修复验证失败，需要进一步调试！');
    }
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testUrlParsing };
