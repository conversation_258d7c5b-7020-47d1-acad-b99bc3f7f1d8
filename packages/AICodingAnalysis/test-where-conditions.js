/**
 * 测试 whereConditions 构建问题
 */

// 模拟生产环境
process.env.USER = 'root';

async function testWhereConditions() {
  console.log('🔍 测试 whereConditions 构建问题...\n');

  try {
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    await modelsModule.initializeDatabase();
    
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;

    // 1. 测试空 whereConditions
    console.log('1. 测试空 whereConditions:');
    const emptyWhere = {};
    console.log('空条件对象:', JSON.stringify(emptyWhere, null, 2));
    
    const emptyResult = await AiCodeAnalysisReports.findAndCountAll({
      where: emptyWhere,
      limit: 5
    });
    console.log('空条件查询结果:', emptyResult.count, '条记录');

    // 2. 测试 gitBranch 条件构建
    console.log('\n2. 测试 gitBranch 条件构建:');
    const gitBranch = '4324';
    const whereConditions = {};
    
    console.log('gitBranch 参数:', gitBranch);
    console.log('gitBranch 是否为真值:', !!gitBranch);
    
    if (gitBranch) {
      console.log('进入 gitBranch 条件构建...');
      try {
        const Op = AiCodeAnalysisReports.sequelize.Sequelize.Op;
        console.log('Op 对象:', typeof Op);
        console.log('Op.like:', typeof Op.like);
        
        whereConditions.git_branch = {
          [Op.like]: `%${gitBranch}%`
        };
        console.log('构建的条件:', JSON.stringify(whereConditions, null, 2));
      } catch (opError) {
        console.error('Op 构建失败:', opError.message);
      }
    }

    // 3. 测试构建的条件
    console.log('\n3. 测试构建的条件:');
    const conditionResult = await AiCodeAnalysisReports.findAndCountAll({
      where: whereConditions,
      limit: 5
    });
    console.log('条件查询结果:', conditionResult.count, '条记录');

    // 4. 模拟完整的参数解析过程
    console.log('\n4. 模拟完整的参数解析过程:');
    const mockQueryParams = {
      gitBranch: '4324'
    };
    
    const {
      page = 1,
      pageSize = 20,
      analysisTime,
      misNumber,
      prId,
      gitRepository,
      gitBranch: parsedGitBranch,
      startTime,
      endTime
    } = mockQueryParams;
    
    console.log('解析后的参数:');
    console.log('- gitBranch:', parsedGitBranch);
    console.log('- 类型:', typeof parsedGitBranch);
    console.log('- 是否为真值:', !!parsedGitBranch);

    // 5. 完整的条件构建过程
    console.log('\n5. 完整的条件构建过程:');
    const fullWhereConditions = {};

    // 时间条件处理
    if (startTime && endTime) {
      fullWhereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.between]: [startTime, endTime]
      };
      console.log('添加时间范围条件');
    } else if (startTime) {
      fullWhereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.gte]: startTime
      };
      console.log('添加开始时间条件');
    } else if (endTime) {
      fullWhereConditions.analysis_timestamp = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.lte]: endTime
      };
      console.log('添加结束时间条件');
    } else if (analysisTime) {
      fullWhereConditions.analysis_timestamp = analysisTime;
      console.log('添加精确时间条件');
    }

    if (misNumber) {
      fullWhereConditions.mis_number = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${misNumber}%`
      };
      console.log('添加 misNumber 条件');
    }

    if (prId) {
      fullWhereConditions.pr_global_id = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${prId}%`
      };
      console.log('添加 prId 条件');
    }

    if (gitRepository) {
      fullWhereConditions.git_repository = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${gitRepository}%`
      };
      console.log('添加 gitRepository 条件');
    }

    if (parsedGitBranch) {
      fullWhereConditions.git_branch = {
        [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: `%${parsedGitBranch}%`
      };
      console.log('添加 gitBranch 条件');
    }

    console.log('最终条件对象:', JSON.stringify(fullWhereConditions, null, 2));

    // 6. 测试最终条件
    console.log('\n6. 测试最终条件:');
    const finalResult = await AiCodeAnalysisReports.findAndCountAll({
      where: fullWhereConditions,
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      limit: 5
    });
    console.log('最终查询结果:', finalResult.count, '条记录');

    // 7. 检查是否有异常情况
    console.log('\n7. 检查异常情况:');
    
    // 检查是否 whereConditions 被意外修改
    const testConditions = { git_branch: { [AiCodeAnalysisReports.sequelize.Sequelize.Op.like]: '%4324%' } };
    console.log('测试条件:', JSON.stringify(testConditions, null, 2));
    
    const testResult = await AiCodeAnalysisReports.findAndCountAll({
      where: testConditions,
      limit: 1
    });
    console.log('测试查询结果:', testResult.count, '条记录');

    // 8. 检查是否有缓存或其他问题
    console.log('\n8. 检查模型状态:');
    console.log('模型类型:', typeof AiCodeAnalysisReports);
    console.log('sequelize 类型:', typeof AiCodeAnalysisReports.sequelize);
    console.log('Op 类型:', typeof AiCodeAnalysisReports.sequelize.Sequelize.Op);

    return { success: true };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  testWhereConditions().then((result) => {
    console.log('\n' + '='.repeat(60));
    if (result.success) {
      console.log('✅ 测试完成！');
    } else {
      console.log('❌ 测试失败');
    }
    console.log('='.repeat(60));
    process.exit(0);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testWhereConditions };
