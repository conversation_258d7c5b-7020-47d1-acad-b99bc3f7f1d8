/**
 * 深度验证真实模型 - 确保没有模拟模型残留
 */

// 模拟生产环境
process.env.USER = 'root';

async function verifyRealModels() {
  console.log('🔍 深度验证真实模型...\n');

  try {
    // 1. 检查模型模块
    console.log('1. 检查模型模块...');
    const modelsModule = require('./models/AICodingAnalysisModels-working.js');
    
    console.log('初始化前:');
    console.log('- AiCodeAnalysisReports 类型:', typeof modelsModule.AiCodeAnalysisReports);
    console.log('- 是否有 sequelize 属性:', !!modelsModule.AiCodeAnalysisReports.sequelize);
    
    // 检查是否是模拟对象
    const initialModel = modelsModule.AiCodeAnalysisReports;
    console.log('- findAndCountAll 返回值检查...');
    const mockResult = await initialModel.findAndCountAll({ limit: 1 });
    console.log('- 初始查询结果:', mockResult);
    
    if (mockResult.count === 0 && mockResult.rows.length === 0) {
      console.log('⚠️  初始模型可能是模拟的（返回空结果）');
    }

    // 2. 初始化数据库
    console.log('\n2. 初始化数据库...');
    await modelsModule.initializeDatabase();
    console.log('数据库状态:', modelsModule.getDatabaseStatus());

    // 3. 检查初始化后的模型
    console.log('\n3. 检查初始化后的模型...');
    const AiCodeAnalysisReports = modelsModule.AiCodeAnalysisReports;
    const AiCodeAnalysisFileDetails = modelsModule.AiCodeAnalysisFileDetails;
    
    console.log('初始化后:');
    console.log('- AiCodeAnalysisReports 类型:', typeof AiCodeAnalysisReports);
    console.log('- AiCodeAnalysisFileDetails 类型:', typeof AiCodeAnalysisFileDetails);
    
    // 4. 检查 sequelize 实例
    console.log('\n4. 检查 sequelize 实例...');
    console.log('- sequelize 类型:', typeof AiCodeAnalysisReports.sequelize);
    console.log('- sequelize.query 类型:', typeof AiCodeAnalysisReports.sequelize.query);
    console.log('- sequelize.transaction 类型:', typeof AiCodeAnalysisReports.sequelize.transaction);
    
    if (typeof AiCodeAnalysisReports.sequelize.query !== 'function') {
      console.log('❌ sequelize.query 不是函数，可能还是模拟对象！');
      return { success: false, reason: 'sequelize.query 不是函数' };
    }

    // 5. 检查模型方法
    console.log('\n5. 检查模型方法...');
    console.log('- create 类型:', typeof AiCodeAnalysisReports.create);
    console.log('- findAndCountAll 类型:', typeof AiCodeAnalysisReports.findAndCountAll);
    console.log('- findOne 类型:', typeof AiCodeAnalysisReports.findOne);
    
    // 6. 测试真实数据库操作
    console.log('\n6. 测试真实数据库操作...');
    
    // 直接 SQL 查询
    console.log('6.1 直接 SQL 查询...');
    const [sqlResults] = await AiCodeAnalysisReports.sequelize.query('SELECT COUNT(*) as count FROM ai_code_analysis_reports');
    console.log('- SQL 查询结果:', sqlResults[0]);
    
    if (!sqlResults[0] || typeof sqlResults[0].count === 'undefined') {
      console.log('❌ SQL 查询失败，可能还是模拟数据库！');
      return { success: false, reason: 'SQL 查询失败' };
    }

    // 模型查询
    console.log('6.2 模型查询...');
    const modelResults = await AiCodeAnalysisReports.findAndCountAll({ limit: 1 });
    console.log('- 模型查询结果总数:', modelResults.count);
    console.log('- 模型查询返回记录数:', modelResults.rows.length);
    
    if (modelResults.count !== sqlResults[0].count) {
      console.log('❌ 模型查询和 SQL 查询结果不一致！');
      console.log(`- SQL: ${sqlResults[0].count}, 模型: ${modelResults.count}`);
      return { success: false, reason: '查询结果不一致' };
    }

    // 7. 测试插入操作
    console.log('\n7. 测试插入操作...');
    const testData = {
      pr_global_id: `VERIFY_REAL_${Date.now()}`,
      pr_title: '验证真实模型',
      git_repository: 'https://git.example.com/verify-real.git',
      git_branch: 'verify-real',
      mis_number: 'verify_user',
      detection_method: '验证真实模型',
      analysis_timestamp: new Date(),
      total_files: 1,
      total_lines: 100,
      changed_lines: 50,
      ai_generated_lines: 25,
      ai_code_ratio: 0.25
    };

    const insertResult = await AiCodeAnalysisReports.create(testData);
    console.log('- 插入结果 ID:', insertResult.id);
    console.log('- 插入结果 PR ID:', insertResult.pr_global_id);
    
    // 验证插入的数据
    const verifyResult = await AiCodeAnalysisReports.findByPk(insertResult.id);
    if (!verifyResult || verifyResult.pr_global_id !== testData.pr_global_id) {
      console.log('❌ 插入数据验证失败！');
      return { success: false, reason: '插入数据验证失败' };
    }

    // 8. 测试事务
    console.log('\n8. 测试事务...');
    const transaction = await AiCodeAnalysisReports.sequelize.transaction();
    console.log('- 事务类型:', typeof transaction);
    console.log('- commit 方法:', typeof transaction.commit);
    console.log('- rollback 方法:', typeof transaction.rollback);
    
    try {
      const transactionTestData = {
        pr_global_id: `TRANSACTION_TEST_${Date.now()}`,
        pr_title: '事务测试',
        git_repository: 'https://git.example.com/transaction-test.git',
        git_branch: 'transaction-test',
        mis_number: 'transaction_user',
        detection_method: '事务测试',
        analysis_timestamp: new Date(),
        total_files: 1,
        total_lines: 100,
        changed_lines: 50,
        ai_generated_lines: 25,
        ai_code_ratio: 0.25
      };

      const transactionResult = await AiCodeAnalysisReports.create(transactionTestData, { transaction });
      await transaction.commit();
      console.log('- 事务测试成功，ID:', transactionResult.id);
    } catch (transactionError) {
      await transaction.rollback();
      console.log('❌ 事务测试失败:', transactionError.message);
      return { success: false, reason: '事务测试失败' };
    }

    // 9. 检查关联关系
    console.log('\n9. 检查关联关系...');
    console.log('- associations:', Object.keys(AiCodeAnalysisReports.associations || {}));
    
    if (!AiCodeAnalysisReports.associations || !AiCodeAnalysisReports.associations.fileDetails) {
      console.log('❌ 关联关系缺失！');
      return { success: false, reason: '关联关系缺失' };
    }

    // 10. 测试关联查询
    console.log('\n10. 测试关联查询...');
    const associationResult = await AiCodeAnalysisReports.findAndCountAll({
      include: [{
        model: AiCodeAnalysisFileDetails,
        as: 'fileDetails',
        required: false
      }],
      limit: 1
    });
    console.log('- 关联查询成功，记录数:', associationResult.count);

    // 11. 最终验证
    console.log('\n11. 最终验证...');
    const finalCount = await AiCodeAnalysisReports.count();
    console.log('- 最终记录总数:', finalCount);
    
    if (finalCount === 0) {
      console.log('❌ 数据库中没有数据，可能还是模拟数据库！');
      return { success: false, reason: '数据库中没有数据' };
    }

    console.log('\n✅ 所有验证通过！确认使用的是真实模型和真实数据库！');
    return { 
      success: true, 
      totalRecords: finalCount,
      dbStatus: modelsModule.getDatabaseStatus()
    };

  } catch (error) {
    console.error('❌ 验证过程出错:', error.message);
    console.error('错误详情:', {
      name: error.name,
      code: error.code,
      errno: error.errno,
      sql: error.sql
    });
    return { success: false, error: error.message };
  }
}

if (require.main === module) {
  verifyRealModels().then((result) => {
    console.log('\n' + '='.repeat(60));
    if (result.success) {
      console.log('🎉 验证成功！确认使用真实模型！');
      console.log(`📊 数据库记录总数: ${result.totalRecords}`);
      console.log(`🔗 数据库状态: ${result.dbStatus}`);
      console.log('\n✅ 可以安全部署到生产环境！');
    } else {
      console.log('❌ 验证失败！');
      console.log(`❌ 失败原因: ${result.reason || result.error}`);
      console.log('\n⚠️  请检查模型配置后再部署！');
    }
    console.log('='.repeat(60));
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('验证运行失败:', error);
    process.exit(1);
  });
}

module.exports = { verifyRealModels };
