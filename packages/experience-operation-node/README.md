# experience-operation-node - Serverlesss Koa 基础模板

## 功能

- Serverless [Era serverless](http://era.sankuai.com/cloud)

## Development Setup

``` bash
# 安装依赖
mnpm install

# 本地模拟在 test 环境启动 
era-cloud serve --env test

访问 http://127.0.0.1:8080
```

## 目录结构

```bash
├── README.md               项目文档
├── test                    单元测试
├── package-lock.json
├── package.json
├── index.js                入口文件
├── .eraignore             制定部分本地调试所需的包列表，打包上传时无需上传
├─ nest.yml                nest 配置
└─ serverless.yml          serverless 配置文件，作为nest配置的降级方案以及本地运行时相关配置

```

## Serverless 发布

[3 分钟发布到 Serverless 平台](https://km.sankuai.com/page/109184700)
