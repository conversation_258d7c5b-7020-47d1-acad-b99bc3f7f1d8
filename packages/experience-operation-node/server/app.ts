import Moa from '@gfe/moa';
import { AppOption } from '@gfe/moa';
import { getAppConfig } from './config/app.config';
import KoaCors from 'koa2-cors';
import { IS_LOCAL, IS_PROD } from './config/env';
const ssoMiddleware = require('@mtfe/sso-client').KoaSSO;

export let app;
export const createServer = async () => {
  console.log('process.env.APP_ENV -> ', process.env.APP_ENV);
  const appConfig: AppOption = await getAppConfig();
  const moa = new Moa();
  app = moa;
  if (!IS_LOCAL) {
    moa.use(
      ssoMiddleware({
        clientId: 'vc-operate-web',
        secret: IS_PROD
          ? '7c2150a6e2fc47a386ad8c6b57718056'
          : 'ca8eece10f6a46029a52c2844b6af11e',
        excludedUriList:
          '/**/heartbeat,/heartbeat,/api/experience/**/*',
        callbackUri: '/api/experience/sso/callback',
        logoutUri: '/api/experience/sso/logout',
        isDebug: false,
      })
    );
  }
  const start = moa.run(appConfig);
  moa.use(
    KoaCors({
      origin: function (ctx) {
        const host = ctx.header.host
        if (host === 'mdz.test.sankuai.com') {
          return `https://${host}`
        } else {
          return 'https://mdz.sankuai.com'
        }
      },
      exposeHeaders: ['WWW-Authenticate', 'Server-Authorization'],
      maxAge: 5,
      credentials: true,
      allowMethods: ['GET', 'POST', 'DELETE'],
      allowHeaders: ['Content-Type', 'Authorization', 'Accept'],
    })
  );

  await start;
  return moa;
};
