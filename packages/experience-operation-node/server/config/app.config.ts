import { AppOption } from '@gfe/moa';
import path from 'path';
import PathInputMiddleware from '../middleware/PathInput.middleware';
import AuthMiddleware from '../middleware/auth.middleware';
import ExceptionFilter from '../exception/HttpException.filter';

import { ImageDelayAVGEntity, WeekTimeEntity, InterfaceImagesUrlEntity } from '../entities';
import { APP_NAME, JDBCRef } from '../config/constant';


const entities = [ImageDelayAVGEntity, WeekTimeEntity, InterfaceImagesUrlEntity];

export function getAppConfig() {
  console.log('JDBCRef', JDBCRef);
  const appConfig: AppOption = {
    name: APP_NAME,
    viewDir: path.resolve(__dirname, '../view'),
    controllerDir: path.resolve(__dirname, '../controller'),
    middlewares: [PathInputMiddleware, AuthMiddleware],
    catchException: ExceptionFilter,
    port: 1024,
    dbConfig: {
      name: APP_NAME,
      appName: APP_NAME,
      jdbcRef: JDBCRef,
      type: 'mysql',
      entities,
      // host:'************',
      // port: 5002,
      bigNumberStrings: false,
      supportBigNumbers: true,
      // synchronize: true,
    },
  };
  return appConfig;
}
