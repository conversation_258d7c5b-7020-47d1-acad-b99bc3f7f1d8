import { PageQueryKey } from '../constant';

export const channelPageConfig = {
  // 休娱主频道页
  dz_channel_joy_home_big_fun: {
    name: '休娱主频道页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.recomlist',
        type: 'URL@BizCode',
        name: '/dzchannel/list/v1/<EMAIL>.joy_v3', // 猜喜列表下钻接口
        showName: '休娱猜喜列表',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'joy_advance_channel_live_show', // 直播/特团
        showName: '直播/特团',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.navigation',
        type: 'URL',
        name: '/dzchannel/navigation/v1/navigation.bin', // 统一金刚位
        showName: '统一金刚位',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'joy_new_customer_coupon', // 新人专区
        showName: '新人专区',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'live_show_channel_joy_activity_operation', // RB
        showName: 'RB运营位',
      },
    ],
    terminal: [
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '70103',
        name: '/dzchannel/list/v1/dzchannelpagelist.bin', // 猜喜列表主接口
        showName: '猜喜列表主接口',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '70140',
        name: '/dzchannel/filter/v1/commonfilter.bin', // 筛选Tab
        showName: '筛选Tab',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '83226',
        name: '/dzchannel/custom/common/operations.bin', // 直播/特团
        showName: '直播/特团',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '39542',
        name: '/dzchannel/navigation/v1/navigation.bin', // 统一金刚位
        showName: '统一金刚位',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '3360',
        name: '/dzchannel/floatinglayer/v1/floatinglayer.bin', // 侧边栏
        showName: '侧边栏',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '70109',
        name: '/dzchannel/promotion/v1/promotion.bin', // 大促/顶通
        showName: '大促/顶通',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '39564',
        name: '/dzchannel/theme/v1/theme.bin', // 主题背景
        showName: '主题背景',
      },
      {
        dataType: 'CAT', // 获取数据的来源
        commandId: '39567',
        name: '/dzchannel/banner/v1/banner.bin', // banner位
        showName: 'banner位',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|homepage|mrn-gc-joyhomebigfun'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.URL,
      mrnComponent: 'mrn-gc-joyhomebigfun',
      mrnEntry: 'homepage',
      appkeys: ['com.sankuai.gcbumrn.gcbu.homepage']
    },
  },
  // 丽人频道页
  dz_channel_female_male: {
    name: '丽人主频道页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.recomlist',
        type: 'Pmf.Activity.activity_dzchannel_recomlist_list',
        name: 'beauty_mt_merge_handsome_shoplist', // 猜喜列表下钻接口
        showName: '男性猜喜列表',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.recomlist',
        type: 'Pmf.Activity.activity_dzchannel_recomlist_list',
        name: 'beauty_mt_female_recom_shop_list', // 猜喜列表下钻接口
        showName: '女性猜喜列表',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.navigation',
        type: 'Pmf.Activity.channel_navigation_activity',
        name: 'liren_man_navigation', // 统一金刚位
        showName: '男性金刚位',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.navigation',
        type: 'Pmf.Activity.channel_navigation_activity',
        name: 'liren_woman_navigation', // 统一金刚位
        showName: '女性金刚位',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'dzchannel_beauty_live_show_specialdeal', // 直播/特团
        showName: '直播/特团',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'joy_new_customer_coupon', // 新人专区
        showName: '新人专区',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'live_show_channel_joy_activity_operation', // RB
        showName: 'RB运营位',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|beauty-handsome-homepage|mrn-gc-handsomebeauty'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.URL,
      mrnComponent: 'mrn-gc-handsomebeauty',
      mrnEntry: 'beauty-handsome-homepage',
      appkeys: ['com.sankuai.gcbumrn.gcbu.beautyhandsomehomepage']
    },
  },
  // EL频道页
  dz_channel_le: {
    name: '生活服务频道页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.recomlist',
        type: 'URL@BizCode',
        name: '/dzchannel/list/v1/<EMAIL>.easy_life_v2', // 猜喜列表下钻接口
        showName: 'LE猜喜列表',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.navigation',
        type: 'URL@BizCode',
        name: '/dzchannel/navigation/v1/<EMAIL>.easy_life_v2', // 统一金刚位
        showName: '金刚位',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzchannel.operation',
        type: 'Pmf.Activity.channel_common_operation_activity',
        name: 'easy_life_v2_special_deal', // 直播/特团
        showName: '直播/特团',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|mrnjoyhome|mrn-gc-easylifehome'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.URL,
      // appkeys: ['com.sankuai.gcbumrn.gcbu.lechannel'],
      mrnEntry: 'lechannel',
      mrnComponent: 'mrn-le-easylife',
    },
  }
}