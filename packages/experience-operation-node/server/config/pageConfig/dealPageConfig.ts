import { PageQueryKey } from '../constant';

export const dealPageConfig = {
  dz_deal_detail: {
    name: '到综团单详情页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.backendforapp.generalsafaweb',
        type: 'Pearl.Process',
        name: '/mapi/mrn/operations.bin', 
        showName: 'Bff接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzu.tpbase.dztgdetailweb',
        type: 'URL',
        name: '/general/platform/dztgdetail/dzdealbase.bin', 
        showName: '团详主接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzu.tpbase.dztgdetailweb',
        type: 'URL',
        name: '/general/platform/dztgdetail/dzdealstyle.bin', 
        showName: '团详样式接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.nibmkt.default',
        type: 'URL',
        name: '/api/mpmkt/delivery/c/delivery/queryexposureresources.bin', 
        showName: '氛围条接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'gm-marketing-member-card-service',
        type: 'URL',
        name: '/mapi/marketing/discountcard/querycardinfo.bin', 
        showName: '会员卡次卡',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzu.tpbase.dztgdetailweb',
        type: 'URL',
        name: '/general/platform/dztgdetail/dzgoodreview.bin', 
        showName: '评价接口',
      },
    ],
    image: {
      pageKey: 'rn|gc|gcdealmrnmodules|gcdealdetailvc'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.URL,
      mrnEntry: '/gc/deal/detail',
      appkeys: ['com.sankuai.gcmrn.gc.gcdealmrnmodules', 'default']
    },
  },
}