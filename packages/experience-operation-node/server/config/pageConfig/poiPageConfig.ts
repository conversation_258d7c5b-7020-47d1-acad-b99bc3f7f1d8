import { PageQueryKey } from '../constant';

export const poiPageConfig = {
  // 休娱商户页
  dz_poi_joy_detail: {
    name: '休娱商户详情页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.base.multimedia',
        type: 'URL',
        name: '/mapi/mva/headpic.mp', 
        showName: '头图接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/addressandphone.bin', 
        showName: '电话地址',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpmctpoiext.info.query',
        type: 'OctoService',
        name: 'BusinessHourModuleToCGatewayService.getAggregatedInfo',
        showName: '营业时间',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/toolbar.bin',
        showName: 'toolbar底bar',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzviewscene.dealshelf',
        type: 'URL',
        name: '/api/dzviewscene/productshelf/dzdealshelf.bin',
        showName: '团购货架(包括今日特价)',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.beautycontent.launchservice',
        type: 'PigeonService',
        name: 'beauty.launch.service.review.MtShopReviewPmfService:queryShopReviewModuleV2',
        showName: '商户评价',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpcontent.feeds',
        type: 'ContentFeedsList',
        name: 'nib.general.shop_live',
        showName: '直播大卡',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|mrn-joy-poidetail|poidetail'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.APPKEY,
      appkeys: ['com.sankuai.gcbumrn.gcbu.mrnjoypoidetail']
    },
  },
  // 丽人详情页
  dz_poi_beauty_detail: {
    name: '丽医商户详情页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.base.multimedia',
        type: 'URL',
        name: '/mapi/mva/headpic.mp', 
        showName: '头图接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/addressandphone.bin', 
        showName: '电话地址',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpmctpoiext.info.query',
        type: 'OctoService',
        name: 'BusinessHourModuleToCGatewayService.getAggregatedInfo',
        showName: '营业时间',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/toolbar.bin',
        showName: 'toolbar底bar',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzviewscene.dealshelf',
        type: 'URL',
        name: '/api/dzviewscene/productshelf/dzdealshelf.bin',
        showName: '团购货架(包括今日特价)',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzviewscene.productshelf',
        type: 'URL',
        name: '/api/dzviewscene/productshelf/dzproductshelf.bin',
        showName: '预付货架',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.beautycontent.launchservice',
        type: 'PigeonService',
        name: 'beauty.launch.service.review.MtShopReviewPmfService:queryShopReviewModuleV2',
        showName: '商户评价',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpcontent.feeds',
        type: 'ContentFeedsList',
        name: 'nib.general.shop_live',
        showName: '直播大卡',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|mrn-beauty-poidetail|poidetail'
    },
    imgSize: {
      pageQueryKey: PageQueryKey.APPKEY,
      appkeys: ['com.sankuai.gcbumrn.gcbu.mrnbeautypoidetail']
    },
  },
  // 生活服务详情页
  dz_poi_le_detail: {
    name: 'LE商户详情页',
    main: [
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.base.multimedia',
        type: 'URL',
        name: '/mapi/mva/headpic.mp', 
        showName: '头图接口',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/addressandphone.bin', 
        showName: '电话地址',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpmctpoiext.info.query',
        type: 'OctoService',
        name: 'BusinessHourModuleToCGatewayService.getAggregatedInfo',
        showName: '营业时间',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.apigw.dzu.default',
        type: 'URL',
        name: '/dzgm/shopview/toolbar.bin',
        showName: 'toolbar底bar',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.dzviewscene.dealshelf',
        type: 'URL',
        name: '/api/dzviewscene/productshelf/dzdealshelf.bin',
        showName: '团购货架(包括今日特价)',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.beautycontent.launchservice',
        type: 'PigeonService',
        name: 'beauty.launch.service.review.MtShopReviewPmfService:queryShopReviewModuleV2',
        showName: '商户评价',
      },
      {
        dataType: 'MOD', // 获取数据的来源
        appKey: 'com.sankuai.mpcontent.feeds',
        type: 'ContentFeedsList',
        name: 'nib.general.shop_live',
        showName: '直播大卡',
      },
    ],
    image: {
      pageKey: 'rn|gcbu|mrn-newle-poidetail|poidetail'
    },
    imgSize: {},
  },
}