/*
 * @Author: l<PERSON><PERSON><PERSON><PERSON> lizhi<PERSON><EMAIL>
 * @Date: 2023-11-06 15:50:44
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-13 20:08:53
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/controller/transaction.controller.ts
 */
import Koa from 'koa';
import <PERSON><PERSON>, {
  Controller,
  Get,
  Post,
  BaseController,
  HttpException,
  HttpStatus,
} from '@gfe/moa';
import {
  isNecessaryParamsReq,
  setHttpResult,
  setHttpError,
  hasAppkey,
  dayjsFormat,
} from '../utils';
import { pageConfig } from '../config/pageConfig/index';
import { imagesURLConfig } from '../config/pageConfig/imagesURLConfig';
import ImageDelayService from '../service/imageDelay.service';
import { ImageQualityUploadReqDTO } from '../interface/image';
import { InterfaceImagesUrlEntity } from '../entities/interfaceImagesUrl.entity';
import { PageImageDelayDBService } from '../service/DB/ImageDelayAVGDB';
import { getImgSize, getImgPixelSize } from '../utils/getImgSize';
import { daXiangGroupPush, daXiangpush } from '../utils/daxing';
import { IS_PROD } from '../config/env';
import { includes } from 'lodash';

@Controller('/api/experience/image')
export default class ImageController extends BaseController {
  constructor(
    protected imageDelayService: ImageDelayService,
    protected PageImageDelayDBService: PageImageDelayDBService
  ) {
    super();
  }

  /**
   * @description: 获取接口图片列表
   * @param pageKey 必填 页面key，例：dz_channel_joy_home_big_fun (到综休娱频道页)
   * @param dimension 必填 分钟级、天级、 minute｜day，分钟级每五分钟返回一个值，天级每天返回一个值
   * @param dateStart 必填 开始时间
   * @param dateEnd 必填 结束时间
   * @return {*}
   */

  @Get('/query/list')
  async terminalDelayInfo(ctx: Koa.Context) {
    try {
      isNecessaryParamsReq(
        ['pageKey', 'dateStart', 'dateEnd', 'dimension'],
        ctx.query
      );

      const { pageKey, dateStart, dateEnd, imgMiniSize = 200 } = ctx.query;

      const imgConfig = pageConfig[pageKey as string]['imgSize'];

      const { pageQueryKey = '', mrnComponent = '', mrnEntry = '', appkeys } = imgConfig;

      // console.log('imgConfig', imgConfig);

      const params = {
        imgMiniSize: imgMiniSize,
        appkeys,
        pageQueryKey,
        mrnComponent,
        mrnEntry,
        start: dayjsFormat(dateStart).valueOf(),
        end: dayjsFormat(dateEnd).valueOf(),
      };

      const picList = await this.imageDelayService.queryImageQualityListByTime(
        params
      );

      for (let key in picList) {
        const findedKey = Object.keys(imagesURLConfig).find((configKey) =>
          key.includes(configKey)
        );
        console.log('key', key, findedKey);
        if (imagesURLConfig[findedKey]) {
          picList[imagesURLConfig[findedKey]] = picList[key];
          delete picList[key];
        }
      }

      const data = {
        list: picList,
      };

      return setHttpResult(data, HttpStatus.OK, 'success');
    } catch (error) {
      ctx.cat.logError('api:/api/experience/query/list', error);
      throw setHttpError(error);
    }
  }
  /**
   * @description: 保存接口图片信息
   * @param appkey 必填 页面key，例：dz_channel_joy_home_big_fun (到综休娱频道页)
   * @param api
   * @param requestTime
   * @param picList
   * @return {*}
   */
  @Post('/quality/upload')
  imageQualityUpload(ctx: Moa.Context) {
    new Promise(async (resolve, reject) => {
      try {
        isNecessaryParamsReq(
          ['api', 'appkey', 'requestTime', 'picList'],
          ctx.query
        );
        
        const query = ctx.query as unknown as ImageQualityUploadReqDTO;

        const { api, appkey, requestTime, pageUrl, requestMessage } = ctx.query;

        const dt = dayjsFormat(requestTime).format('YYYYMMDD');

        const hour = dayjsFormat(requestTime).format('HH');

        const time = dayjsFormat(requestTime).valueOf();

        let reqObjt;
        try {
          if (requestMessage) {
            reqObjt = JSON.parse(JSON.stringify(requestMessage));
          } else {
            console.error('requestMessage is undefined or null');
          }
        } catch (error) {
          console.error(error);
        }
        
        const dbReqObjt = {query: reqObjt?.query || {}, body: reqObjt?.body || {}}

        let picListDTO = query.picList.map(async (pic) => {
          const sqlQuery = {
            api: api,
            appkey: appkey,
            pageUrl: pageUrl,
            dt: dt,
            hour: hour,
            imageUrl: pic.url,
            // lock: {
            //   mode: 'pessimistic_read'
            // }
          };

          const isMatch = Object.keys(imagesURLConfig).some((key) =>
            api.includes(key)
          );
          console.log('isMatch===', isMatch);

          if (!isMatch) {
            return null;
          }

          const alreadyExistingList: any =
            await this.imageDelayService.queryOneImageQualityInfo(sqlQuery);

          console.log('alreadyExistingList', alreadyExistingList);

          if (alreadyExistingList && alreadyExistingList.id) {
            return null;
          }

          const param: InterfaceImagesUrlEntity = {
            api: api,
            appkey: appkey,
            pageUrl: pageUrl,
            dt: dt,
            hour: hour,
            time: time,
            imageUrl: pic.url,
            reqMessage: JSON.stringify(dbReqObjt),
          };

          return param;
        });

        const paramsList = await Promise.all(picListDTO);

        const picListDTOParams = paramsList.filter((item) => item !== null);

        let uniquePicListDTOParams = Array.from(
          new Set(picListDTOParams.map((item) => JSON.stringify(item)))
        ).map((item) => JSON.parse(item));

        let uniquePicListDTOParamsWithImageSize = uniquePicListDTOParams.map(
          async (param) => {
            // 图片文件大小
            const imgSize = await getImgSize(param.imageUrl)
            // 图片像素大小
            const imgConfig = await getImgPixelSize(param.imageUrl)
            

            param.imageSize = imgSize;
            param.pixelWidth = imgConfig?.imgPixelSize?.width || 0;
            param.pixelHeight = imgConfig?.imgPixelSize?.height || 0;
            param.frameNumber = imgConfig?.imgPixelSize?.frame_num || 0;
            console.log('图片宽高', imgConfig)
            if (imgSize > 200 || imgConfig?.pixelOutofSize) {
              return param;
            } else {
              return null
            }
          }
        );

        uniquePicListDTOParamsWithImageSize =
          uniquePicListDTOParamsWithImageSize.filter((item) => item !== null);

        let list: any = await Promise.all(
          uniquePicListDTOParamsWithImageSize
        );
        list = list.filter((item) => item !== null);
        
        if (list && list.length > 0) {
          console.log('list', list);
        } else {
          resolve(true);
          return;
        }

        try {
          await this.imageDelayService.imageQualityUploadDB(list);

          if (IS_PROD) {
            // 大图信息自动推送
            let daxingMsg = ''
            const imgConfigJoy = pageConfig['dz_channel_joy_home_big_fun']['imgSize'];
            const imgConfigFemale = pageConfig['dz_channel_female_male']['imgSize'];
            const imgConfigLe = pageConfig['dz_channel_le']['imgSize'];
            
            list.forEach((imgItem) => {
              if (Number(imgItem.imageSize) > 500) {
                const pageUrl = imgItem?.pageUrl || ''
                let pageName = ''
                if (pageUrl.includes(imgConfigFemale.mrnEntry) || pageUrl.includes(imgConfigFemale.mrnComponent)) {
                  // 丽人频道页
                  pageName = '丽人频道页'
                } else if (pageUrl.includes(imgConfigLe.mrnEntry) || pageUrl.includes(imgConfigLe.mrnComponent)) {
                  // LE频道页
                  pageName = 'LE频道页'
                } else if (pageUrl.includes(imgConfigJoy.mrnEntry) && pageUrl.includes(imgConfigJoy.mrnComponent)) { 
                  // 休娱频道页
                  pageName = '休娱频道页'
                }
                if (pageName && imgItem.api.indexOf('dzchannelpagelist.bin') === -1){
                  daxingMsg = daxingMsg + `${pageName}发现大图:\n${imgItem.imageUrl}\n文件大小：${imgItem.imageSize}kb\n像素大小：${imgItem.pixelWidth} * ${imgItem.pixelHeight}\n对应接口：${imgItem.api}\n请求参数：${imgItem.reqMessage}
                  \n`
                }
              }
            })
            if (daxingMsg) {
              daxingMsg = '❗❗❗❗\n' + daxingMsg + '请及时处理.'
              daXiangGroupPush('67956584648', daxingMsg)
              // daXiangpush('zengzhi02', daxingMsg)
              console.log('大图信息自动推送', daxingMsg)
            }
          }
          resolve(true);
        } catch (error) {
          reject(false);
        }
      } catch (err) {
        console.log(err);
        ctx.cat.logError('api:/api/experience/quality/upload', err);
        reject(false);
        // throw setHttpError(err);
      }
    }).catch((err) => {
      console.log(err);
    });

    return setHttpResult(true);
  }

  @Get('/error')
  throwError(ctx: Koa.Context, next: Function) {
    throw new HttpException('Error', HttpStatus.FORBIDDEN);
  }
}
