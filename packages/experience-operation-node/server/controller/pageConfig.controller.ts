/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> lizhi<PERSON><EMAIL>
 * @Date: 2023-11-06 15:50:44
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-13 20:08:53
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/controller/transaction.controller.ts
 */
import Koa from 'koa';
import <PERSON><PERSON>, {
  Controller,
  Get,
  Post,
  BaseController,
  HttpException,
  HttpStatus,
} from '@gfe/moa';
import {
  isNecessaryParamsReq,
  setHttpResult,
  setHttpError,
  hasAppkey,
  dayjsFormat,
} from '../utils';
import { pageConfig } from '../config/pageConfig/index';
import { get } from 'lodash';

@Controller('/api/experience/pageConfig')
export default class pageConfigController extends BaseController {
  constructor() {
    super();
  }

  @Get('/pageList')
  pageList(ctx: Koa.Context) {

    const pageList = {}
    Object.keys(pageConfig).forEach((key) => {
      pageList[key] = { 
        name: pageConfig[key].name,
        jumpUrl: `https://mdz.sankuai.com/quality-grail/index.html?pageKey=${key}`,
      }
    });

    return setHttpResult({
      pageList
    }, HttpStatus.OK, 'success');
  }yarn 
}
