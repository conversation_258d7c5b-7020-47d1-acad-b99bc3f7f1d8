/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>@meituan.com
 * @Date: 2023-11-06 15:50:44
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-21 15:13:24
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/controller/transaction.controller.ts
 */
import Koa from 'koa';
import {
  Controller,
  Get,
  BaseController,
  HttpException,
  HttpStatus,
  Post,
} from '@gfe/moa';
import {
  isNecessaryParamsReq,
  setHttpResult,
  setHttpError,
  hasAppkey,
  dayjsFormat,
} from '../utils';
import { pageConfig } from '../config/pageConfig/index';
import { terminal_api_config } from '../config/apiConfig';
import TransactionService from '../service/transaction.service';
import ImageDelayService from '../service/imageDelay.service';
import WeekTimeService from '../service/weektime.service'
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear'
import { forIn, values } from 'lodash';
dayjs.extend(weekOfYear)

const TYPE_MAP = {
  avg: null,
  yoy: 'day',
  qoq: 'week',
};

@Controller('/api/experience')
export default class TransactionController extends BaseController {
  constructor(
    protected transactionService: TransactionService,
        protected weekTimeService: WeekTimeService,
    protected imageDelayService: ImageDelayService
  ) {
    super();
  }

  formatDepsData({ dps, hashKey, level, boundary }) {
    const hash = {},
      res = {};
    // 天级：1天给2个点，小时级：1h两个点
    Object.keys(dps).forEach((item) => {
      const date = dayjs(+item).format(hashKey);
      const time = dayjs(+item).format(level);

      if (!hash[date]) {
        hash[date] = new Array(2).fill([]);
      }

      if (+time < boundary) {
        hash[date][0].push(+dps[item]);
      } else {
        hash[date][1].push(+dps[item]);
      }
    });

    Object.keys(hash).forEach((item) => {
      const [firstValue, secondValue] = hash[item]
        .map(
          (elem) =>
            elem.reduce(
              (accumulator, currentValue) => accumulator + currentValue,
              0
            ) / elem.length
        )
        .map((elem) => +elem.toFixed());

      if (level === 'HH') {
        res[`${item} 12:00`] = firstValue;
        res[`${item} 24:00`] = secondValue;
      } else if (level === 'mm') {
        let lastChar = item.slice(-1)
        let incrementedChar = Number(lastChar) + 1
        let newStr = item.slice(0, -1) + incrementedChar
        res[`${item}:30`] = firstValue;
        res[`${newStr}:00`] = secondValue;
      }
    });

    return res;
  }

  calTargetDate(date, dateType): any {
    if (!date) return null;
    const yestoday = TYPE_MAP[dateType]
      ? dayjsFormat(date).subtract(1, TYPE_MAP[dateType])
      : dayjsFormat(date);
    return yestoday.valueOf();
  }

  async getTransactionData(
    dateStart,
    dateEnd,
    pageKey,
    delayType,
    dimension,
    dateType,
    dataSetType
  ) {
    const start = this.calTargetDate(dateStart, dateType);
    const end = this.calTargetDate(dateEnd, dateType);
    const configList = pageConfig[pageKey as string][dataSetType];

    let allPromice = null;
    if (delayType === 'avg') {
      allPromice = configList.map((item) => {
        return this.transactionService.getTransactionAvg({
          type: item.type,
          start: start,
          end: end,
          appKey: item.appKey,
          name: item.name,
        });
      });
    } else if (delayType === 'tp90') {
      allPromice = configList.map((item) => {
        return this.transactionService.getTransactionTP90({
          type: item.type,
          start: start,
          end: end,
          appKey: item.appKey,
          name: item.name,
        });
      });
    }

    const result = await Promise.all(allPromice);
    const delay = result.map((r, index) => {
      if (r.data && r.data.length > 0) {
        const data = r.data[0];
        let params = {};

        if (dimension === 'day') {
          params = {
            dps: data.dps,
            hashKey: 'MM-DD',
            level: 'HH',
            boundary: 12,
          };
        } else {
          params = {
            dps: data.dps,
            hashKey: 'MM-DD HH',
            level: 'mm',
            boundary: 30,
          };
        }
        const hash = this.formatDepsData(params as any);
        return {
          name: configList[index].showName,
          dps: hash,
          stats: data.stats,
        };
      } else {
        return {
          name: configList[index].showName,
          dps: {},
          stats: {},
        };
      }
    });

    return {
      tag: dateType,
      delay,
    };
  }

  /**
   * @description: 获取下钻接口耗时
   * @param pageKey 
   * @param dateStart 可选
   * @param dateEnd 可选
   * @return {*}
   */    
  @Get('/transaction/main')
  async transactionMainInfo(ctx: Koa.Context) {
    try {
      isNecessaryParamsReq(['pageKey'], ctx.query)
      const { dateStart, dateEnd, pageKey, delayType = 'avg', dimension = 'min' } = ctx.query

      const hasKey = Object.keys(pageConfig).find((key) => {
        return pageKey === key;
      });

      if (!hasKey) {
        return {
          code: 400,
          massage: 'pageKey 不存在',
          data: null,
        };
      }

      const baseData = ['avg', 'yoy', 'qoq'].map((item) =>
        this.getTransactionData(
          dateStart,
          dateEnd,
          pageKey,
          delayType,
          dimension,
          item,
          'main'
        )
      );
      const delay = await Promise.all(baseData);

      return {
        code: 200,
        massage: 'success',
        data: {
          delay,
        },
      };
    } catch (error) {
      console.log(error);
      ctx.cat.logError('api:/api/experience/transaction', error);
      throw setHttpError(error);
    }
  }

  /**
     * @description: 计算周维度接口耗时并存入数仓
     * @param pageKey 
     * @param dateStart 可选
     * @param dateEnd 可选
     * @return {*}
     */    
  @Post('/transaction/week/set')
  async transactiontestInfo(ctx: Koa.Context) {
    try {
      // isNecessaryParamsReq(['pageKey'], ctx.query)
      const pageKeys = Object.keys(pageConfig)
      const dateStart = dayjs().subtract(6, 'day').valueOf()
      const dateEnd = dayjs().valueOf()
      // const dateStart = dayjs().subtract(55, 'day').valueOf()
      // const dateEnd = dayjs().subtract(49, 'day').valueOf()
      const currentWeek = dayjs(dateStart).format('YYYY-MM-DD')

      const weeks = pageKeys.map(async (pageKey) => {
        const baseData = ['avg', 'tp90'].map(async delayType => {
          const data = await this.getTransactionData(dateStart, dateEnd, pageKey, delayType, 'day', null, 'main')
          return data.delay.map(item => ({
            api_en: pageConfig[pageKey]['main'].find(i => i.showName === item.name).name,
            api: item.name,
            [delayType === 'avg' ? 'avg_totalDuration' : 'P90']: item.stats.avg || null,
            year: dayjs().year(),
            week: `W${dayjs(currentWeek).week()}`
          }))
        })
  
        const delay = await Promise.all(baseData);
        const newdelay = delay[0].map(item => ({
          ...item,
          P90: delay[1].find(i => i.api === item.api)?.P90 || null,
        }))
  
        this.weekTimeService.setWeekTimeData(newdelay)
      })

      
      return {
        code: 200,
        massage: 'success',
        data: {},
      };
    } catch (error) {
      console.log(error);
      ctx.cat.logError('api:/api/experience/transaction', error);
      throw setHttpError(error);
    }
  }

    /**
     * @description: 获取周维度接口耗时
     * @param appKey 
     * @param name
     * @param start 可选
     * @param end 可选
     * @return {*}
     */    
    @Get('/transaction/week')
    async transactionWeekInfo(ctx: Koa.Context) {
        try {
            isNecessaryParamsReq(['pageKey'], ctx.query)
            const { pageKey, delayType = 'avg' } = ctx.query

            hasAppkey(pageConfig, pageKey);
            const { main } = pageConfig[pageKey as string];
            const res = await this.weekTimeService.getWeekTimeData(delayType, main.map(t=>t.name))
            if(res) {
              return {
                code: 200,
                massage: 'success',
                data: res,
              };
            } else {
              return {
                code: 400,
                massage: 'pageKey 不存在',
                data: null
              }
            }
    } catch (error) {
      ctx.cat.logError('api:/api/experience/transaction', error);
      throw setHttpError(error);
    }
  }

  /**
   * @description: 获取主接口端到端耗时
   * @param pageKey 必填 页面key，例：dz_channel_joy_home_big_fun (到综休娱频道页)
   * @param dimension 必填 分钟级、天级、 minute｜day，分钟级每五分钟返回一个值，天级每天返回一个值
   * @param dateStart 必填 开始时间
   * @param dateEnd 必填 结束时间
   * @return {*}
   */
  @Get('/transaction/terminal/trend')
  async terminalDelayInfo(ctx: Koa.Context) {
    try {
      isNecessaryParamsReq(
        ['pageKey', 'dateStart', 'dateEnd', 'dimension'],
        ctx.query
      );

      const { pageKey, dimension, dateStart, dateEnd } = ctx.query;

      console.log('111', dayjsFormat(dateEnd).diff(dayjsFormat(dateStart), 'day'));

      if (
        dimension == 'min' &&
        dayjsFormat(dateEnd).diff(dayjsFormat(dateStart), 'day') >= 2
      ) {
        throw Error('查询分钟级数据，时间跨度不能大于两天');
      }

      hasAppkey(pageConfig, pageKey);

      const { terminal } = pageConfig[pageKey as string];

      const formatString =
        ctx.query.dimension == 'day' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH';

      const dateType = ['avg', 'yoy', 'qoq'];

      const baseData = dateType.map((type) => {
        ctx.query.start = this.calTargetDate(ctx.query.dateStart, type);
        ctx.query.end = this.calTargetDate(ctx.query.dateEnd, type);
        return this.transactionService.getTransactionTerminalDelay(
          terminal,
          ctx.query
        );
      });

      const resultInfo = await Promise.all(baseData);

      const resultInfoFormat = resultInfo.map((item, index) => {
        return {
          delay: item,
          tag: dateType[index],
        };
      });

      const data = {
        delay: resultInfoFormat,
      };

      return setHttpResult(data, HttpStatus.OK, 'success');
    } catch (error) {
      ctx.cat.logError('api:/api/experience/transaction/terminal/trend', error);
      throw setHttpError(error);
    }
  }

  /**
   * @description: 获取端到端接口耗时，周报数据
   * @param teamKey 必填 团队key，例：dz 到综
   * @return {*}
   */
  @Get('/transaction/terminal/week_get')
  async terminalDelayWeekInfoGet(ctx: Koa.Context) {
    try {
      
      isNecessaryParamsReq(
        ['teamKey'],
        ctx.query
      );
      const { teamKey, delayType = 'avg' } = ctx.query
      const teamsTerminal = terminal_api_config[teamKey as string]
      if (teamsTerminal) {
        let apis = []
        Object.keys(teamsTerminal).forEach((key) => {
          apis.push(...teamsTerminal[key].terminal.map(t=>`terminal$$${t.api}$$${t.commandId}`)) 
        })
        console.log(apis)
        const res = await this.weekTimeService.getWeekTimeData(delayType, apis.map(t=>t))

        if(res) {
          return {
            code: 200,
            massage: 'success',
            data: res,
          };
        } else {
          return {
            code: 400,
            massage: 'pageKey 不存在',
            data: null
          }
        }
      } else {
        console.log('未找到对应团队')
      }
    } catch (error) {
      ctx.cat.logError('api:/transaction/terminal/week', error);
      throw setHttpError(error);
    }
  }

  /**
   * @description: 设置端到端接口平均耗时，周报数据
   * @param teamKey 必填 团队key，例：dz 到综
   * @return {*}
   */
  @Post('/transaction/terminal/week_set')
  terminalDelayWeekInfo(ctx: Koa.Context) {
    isNecessaryParamsReq(
      ['teamKey'],
      ctx.query
    );
    try {
      const asyncPromise = async (ctxCopy) => {
        // const { teamKey }  = ctxCopy.query
        const teamKey = 'dz'
        const dateStart = dayjs().subtract(6, 'day').valueOf()
        const dateEnd = dayjs().valueOf()
        // const dateStart = dayjs().subtract(17, 'day').valueOf()
        // const dateEnd = dayjs().subtract(10, 'day').valueOf()
        const weakStart = dayjs(dateStart).format('YYYY-MM-DD')
        const weakEnd = dayjs(dateEnd).format('YYYY-MM-DD')
        const currentWeek = dayjs(dateStart).format('YYYY-MM-DD')
        const hasKey = Object.keys(terminal_api_config).find((key) => {
          return teamKey === key;
        });
  
        if (hasKey) {
          const teamApiConfig = terminal_api_config[teamKey as string]
          console.log(teamApiConfig.keys)
          const teamRequest = Object.keys(teamApiConfig).map((key) => {
            const pageInfo = teamApiConfig[key]
            const apis = pageInfo.terminal
            const requestPromise = apis.map((api) => {
              return {
                requestPromise: this.transactionService.getTransactionTerminalDelayFromCatByWeek({
                  start:weakStart,
                  end: weakEnd,
                  commandId:api.commandId
                }),
                requestTP90Promise: this.transactionService.getTransactionTerminalDelayTP90FromCatByWeek({
                  start:weakStart,
                  end: weakEnd,
                  commandId:api.commandId
                }),
              ...api
              }
            })
            return requestPromise
          })
  
          const requests:Array<any> = teamRequest.flat(1) as Array<any>
          const res = []
          const requestLoop = async (requestsArr) => {
            // 串行请求cat接口，（cat接口性能限制，必须加延迟串行请求）
            for(const req of requestsArr) {
              const r = await req.requestPromise.catch(()=> {
                return {}
              })
              console.log('请求周接口耗时', req.name)
  
              const t = await new Promise((resolve) => {
                setTimeout(resolve, 8000)
              })

              const rTP90 = await req.requestTP90Promise.catch(()=>{
                return {}
              })
              console.log('请求周接口tp90耗时', req.name)

              if (r && r.responseTimeAvg > 0 && rTP90 && rTP90.plotLines && rTP90.plotLines['90']) {
                const tp90Point = rTP90.plotLines['90']
                if (rTP90.values && rTP90.values[0] && rTP90.values[0].length > tp90Point + 1) {
                  const delayStart = rTP90.values[0][tp90Point]
                  const delayEnd = rTP90.values[0][tp90Point + 1]
                  const data = {
                    weekData: {
                      avg_totalDuration: r.responseTimeAvg,
                      year: dayjs().year(),
                      week: `W${dayjs(currentWeek).week()}`,
                      api: req.name,
                      api_en: `terminal$$${req.api}$$${req.commandId}`,
                      P90: (delayEnd + delayStart) / 2
                    },
                    commandId: req.commandId
                  }
                  res.push(data)
                }
              }

              const t2 = await new Promise((resolve) => {
                setTimeout(resolve, 3000)
              })
            }
            return true
          }
  
          // 获取周接口耗时avg
          await requestLoop(requests)
  
          // 请求失败，重试3次 （cat接口性能问题，失败率较高）
          let reloadTimes = 3
          let shouldReloadRequest = requests
          while (reloadTimes > 0 && res.length < requests.length) {
            const findReload = shouldReloadRequest.filter((req) => {
              const successRes = res.find((r) => {
                return r.commandId === req.commandId
              })
              return !successRes
            })
            console.log('需要重新加载的', findReload)
            const newReload = findReload.map(reload => {
              return {
                ...reload,
                requestPromise: this.transactionService.getTransactionTerminalDelayFromCatByWeek({
                  start:weakStart,
                  end: weakEnd,
                  commandId:reload.commandId
                }),
                requestTP90Promise: this.transactionService.getTransactionTerminalDelayTP90FromCatByWeek({
                  start:weakStart,
                  end: weakEnd,
                  commandId:reload.commandId
                }),
              }
            })
            await requestLoop(newReload)
            shouldReloadRequest = newReload
            reloadTimes = reloadTimes - 1
          }
  
          // 入库
          const weekData = res.map(r => {
            return r.weekData
          })
          if (res.length === requests.length) {
            this.weekTimeService.setWeekTimeData(weekData)
            console.log('周报写入成功！！！！')
          } else {
            console.log('周报查询失败!!!!')
          }

        } else {
          console.log('未找到对应团队')
        }
      }
      
      asyncPromise(ctx).then(r => {
        console.log('周报生成完成....')
      })

      console.log('周报开始生成....')
      return setHttpResult({
        status:'success',
      }, HttpStatus.OK, 'success');
    } catch (error) {
      ctx.cat.logError('api:/transaction/terminal/week', error);
      throw setHttpError(error);
    }
  }

  /**
   * @description: 获取图片平均耗时
   * @param pageKey 必填 页面key，例：dz_channel_joy_home_big_fun (到综休娱频道页)
   * @param dimension 必填 小时级、天级、 hour｜day，小时级每半小时返回一个值，天级每半天返回一个值
   * @param dateStart 必填 开始时间
   * @param dateEnd 必填 结束时间
   * @return {*}
   */
  @Get('/image/trend')
  async imageDelay(ctx: Koa.Context) {
    try {
      isNecessaryParamsReq(
        ['pageKey', 'dateStart', 'dateEnd', 'dimension'],
        ctx.query
      );

      const { pageKey } = ctx.query;

      hasAppkey(pageConfig, pageKey);

      const { image } = pageConfig[pageKey as string];

      ctx.query.imagePageKey = image.pageKey;

      const formatString =
        ctx.query.dimension == 'day' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH';

      const dateType = ['avg', 'yoy', 'qoq'];

      const baseData = dateType.map((type) => {
        ctx.query.start = this.calTargetDate(ctx.query.dateStart, type);
        ctx.query.end = this.calTargetDate(ctx.query.dateEnd, type);
        return this.imageDelayService.getImageDelay(ctx.query);
      });

      const resultInfo = await Promise.all(baseData);

      const resultInfoFormat = resultInfo.map((item, index) => {
        const dps = item.result.reduce((obj, item) => {
          if (item.avg) {
            const time = dayjs(item.time).format(formatString);
            obj[time] = item.avg;
          }
          return obj;
        }, {});
        return {
          delay: dps,
          tag: dateType[index],
        };
      });

      const data = { delay: resultInfoFormat };

      return setHttpResult(data, HttpStatus.OK, 'success');
    } catch (error) {
      ctx.cat.logError('api:/api/experience/image/trend', error);
      throw setHttpError(error);
    }
  }

  @Get('/error')
  throwError(ctx: Koa.Context, next: Function) {
    throw new HttpException('TEST Error', HttpStatus.FORBIDDEN);
  }
}
