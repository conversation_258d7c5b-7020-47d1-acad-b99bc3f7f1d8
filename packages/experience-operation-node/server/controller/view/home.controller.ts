import Koa from 'koa';
import { Controller, Get, BaseController } from '@gfe/moa';

@Controller('/home')
export default class HomeController extends BaseController {
    @Get('/index')
    getAlive(ctx: Koa.Context, next: Function) {
        return ctx.render('hello', {});
    }

    @Get('/cortex')
    getCortexPage(ctx: Koa.Context, next: Function) {
        return ctx.render('cortex', {
            title: 'cortex 测试',
        });
    }
}
