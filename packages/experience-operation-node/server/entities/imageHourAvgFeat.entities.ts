import {
  Entity,
  Column,
  Index,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  
} from '@gfe/zebra-typeorm-client'


@Entity('fe_image_avg_duration_by_page')
export class ImageDelayAVGEntity {
  @PrimaryColumn({ nullable: false, comment: '页面名称'})
  pagename: string

  @Column({ nullable: false,comment: '日期， 格式为YYYMMDD'})
  dt: string

  @Column({ nullable: false,comment: '小时， 格式为HH'})
  hour: string

  @Column({ nullable: false,comment: '平均图片加载总时长，单位ms' })
  avg_totalDuration: number
}
