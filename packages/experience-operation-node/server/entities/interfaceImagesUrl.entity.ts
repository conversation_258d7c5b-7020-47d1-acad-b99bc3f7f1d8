import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn
} from '@gfe/zebra-typeorm-client'

@Entity('fe_interface_images_url')
export class InterfaceImagesUrlEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id?: number

  @Column({ nullable: false, comment: '接口地址', type: 'text' })
  api: string | string[]

  @Column({ nullable: false, comment: '页面标识', type: 'text' })
  appkey: string | string[]

  @Column({ nullable: false, comment: '页面地址', type: 'text' })
  pageUrl: string | string[]

  @Column({ nullable: false, comment: '日期', type: 'text' })
  dt: string

  @Column({ nullable: false, comment: '小时', type: 'text' })
  hour: string

  @Column({ nullable: false, comment: '图片地址', type: 'text' })
  imageUrl: string

  @Column({ nullable: false, comment: '图片尺寸', type: 'text' })
  imageSize?: string

  @Column({ nullable: false, comment: '时间戳', type: 'text' })
  time?: number

  @Column({ nullable: false, comment: '接口参数信息', type: 'text' })
  reqMessage?: string | string[]

  @Column({ nullable: false, comment: '图片像素宽度', type: 'text' })
  pixelWidth?: number

  @Column({ nullable: false, comment: '图片像素高度', type: 'text' })
  pixelHeight?: number

  @Column({ nullable: false, comment: '图片帧数', type: 'text' })
  frameNumber?: number
  // @CreateDateColumn({ comment: '创建时间' })
  // createTime: Date

  // @UpdateDateColumn({ comment: '更新时间' })
  // updateTime: Date

}
