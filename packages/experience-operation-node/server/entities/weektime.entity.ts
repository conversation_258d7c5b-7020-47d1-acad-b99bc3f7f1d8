/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-29 16:40:43
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-07 20:58:41
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/node-manage-monorepo/packages/experience-operation-node/server/entities/weektime.entity.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
} from '@gfe/zebra-typeorm-client'

@Entity('fe_interface_week_time')
export class WeekTimeEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', comment: '自增id主键'})
  id: number

  @Column({ nullable: false, comment: '接口名' })
  api: string

  @Column({ nullable: false, comment: '接口路径' })
  api_en: string

  @Column({ nullable: false, comment: '接口平均耗时，单位ms' })
  avg_totalDuration: number

  @Column({ nullable: false, comment: '接口耗时90分位点，单位ms' })
  P90: number

  @Column({ nullable: false, comment: '年' })
  year: string

  @Column({ nullable: false, comment: '周' })
  week: string
}
