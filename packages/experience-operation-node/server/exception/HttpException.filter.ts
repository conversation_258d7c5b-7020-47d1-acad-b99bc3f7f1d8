import Moa, { Catch, ExceptionFilter, HttpStatus } from '@gfe/moa';

@Catch()
export default class HttpExceptionFilter implements ExceptionFilter {
    catch(exception: import('@gfe/moa').HttpException, ctx: Moa.Context): void {
        if (exception.code === HttpStatus.FORBIDDEN) {
            ctx.body = {
                code: exception.code,
                message: 'FORBIDE<PERSON>',
                data: null,
            };
        } else {
            ctx.body = {
                code: exception.code,
                message: exception.msg,
                data: null,
            };
        }
    }
}
