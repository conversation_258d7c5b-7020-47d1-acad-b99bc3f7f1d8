/*
 * @Author: guowei
 * @Date: 2021-09-27 12:02:40
 * @LastEditors: guowei
 * @LastEditTime: 2023-10-16 22:03:17
 * @Description:
 */

export interface ListQuery {
  where?: object | object[]
  select?: any
  skip?: number
  take?: number
  order?: object
  [prop: string]: any
}

export interface ImageQualityUploadReqDTO {
  api: string
  appkey: string
  pageUrl: string
  requestTime: string
  requestMessage?: object
  picList: Array<any>
}