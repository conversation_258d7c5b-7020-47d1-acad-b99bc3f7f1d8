import Moa, { BaseMiddleware, Middleware } from '@gfe/moa';

@Middleware()
export default class CheckRequiredParams implements BaseMiddleware {
  use(ctx: Moa.Context, next) {
    const { req ,request, requiredParams } = ctx;
    const queryParams = ctx.query;
    ctx.requiredParams = 123

    // for (let i = 0; i < requiredParams.length; i++) {
    //   if (!queryParams.hasOwnProperty(requiredParams[i])) {
    //     ctx.status = 400;
    //     ctx.message = `缺少必要的参数：${requiredParams[i]}`;
    //     return;
    //   }
    // }

    return next();
  }
}
