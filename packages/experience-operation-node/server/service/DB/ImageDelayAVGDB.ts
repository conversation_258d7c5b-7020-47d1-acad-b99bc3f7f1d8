import { Service, BaseService, DBService } from '@gfe/moa';
import { ListQuery } from '../../interface/db';
import { ImageDelayAVGEntity } from '../../entities/imageHourAvgFeat.entities';
import { InterfaceImagesUrlEntity } from '../../entities/interfaceImagesUrl.entity';
import moment from 'moment';
import dayjs from 'dayjs';
import { enumerateDaysBetweenDates } from '../../utils';
import { Between, Like, getConnection } from 'typeorm';
import { PageQueryKey } from '../../config//constant';

interface Query {
  imagePageKey: string;
  start: string;
  end: string;
  dimension: 'hour' | 'day';
}

@Service()
export class PageImageDelayDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super();
  }

  private _repository = this.dbService.getRepository(ImageDelayAVGEntity);
  private _interfaceImagesRepository = this.dbService.getRepository(
    InterfaceImagesUrlEntity
  );

  private connection = getConnection('com.sankuai.dzufebiz.manage');
  private queryRunner = this.connection.createQueryRunner();

  // 按小时图片平均耗时
  async findAverageByHour(query?: Query) {
    const { imagePageKey, start, end, dimension = 'hour' } = query;

    const startDateTime = dayjs(+start).format('YYYYMMDDHH');
    const endDateTime = dayjs(+end).format('YYYYMMDDHH');

    console.log('startDateTime', startDateTime);
    console.log('endDateTime', endDateTime);

    const result = await this._repository
      .createQueryBuilder('db')
      .select('CONCAT(db.dt, db.hour)', 'time')
      .addSelect('db.avg_totalDuration', 'avg')
      .where('db.pagename = :pagename', { pagename: imagePageKey })
      .andWhere('CONCAT(db.dt, db.hour) BETWEEN :start AND :end', {
        start: startDateTime,
        end: endDateTime,
      })
      .execute();
    return { result };
  }

  // 按天图片平均耗时
  async findAverageByDay(query?: Query) {
    const { start, end, imagePageKey } = query;

    const enumerateDays = enumerateDaysBetweenDates(start, end);

    console.log('enumerateDays', enumerateDays);

    const averages = await Promise.all(
      enumerateDays.map(async (time) => {
        const average = await this._repository
          .createQueryBuilder('db')
          .select('AVG(db.avg_totalDuration)', 'avg')
          .where('db.dt = :dt', { dt: time })
          .andWhere('db.pagename = :pagename', { pagename: imagePageKey })
          .execute();

        return {
          time,
          avg: average[0].avg,
        };
      })
    );

    console.log('averages', averages);

    return { result: averages };
  }
  // 更新图片尺寸信息
  async updateImageQualityDB(params: InterfaceImagesUrlEntity) {
    const result = await this._interfaceImagesRepository.update(3, params);
    return result;
  }
  // 保存图片尺寸信息
  async saveImageQualityDB(params: InterfaceImagesUrlEntity[]) {
    // const newParams: InterfaceImagesUrlEntity[] = params.map((param) => {
    //   let entity = new InterfaceImagesUrlEntity();
    //   for (let key in param) {
    //     entity[key] = param[key];
    //   }
    //   return entity;
    // });

    // try {
    //   // 获取连接并创建新的queryRunner
    //   await this.queryRunner.connect();
    //   // 开始事务：
    //   await this.queryRunner.startTransaction();
    //   await this.queryRunner.manager.save(newParams);
    //   // 提交事务：
    //   await this.queryRunner.commitTransaction();

    //   return true;
    // } catch (error) {
    //   // 有错误做出回滚更改
    //   await this.queryRunner.rollbackTransaction();
    //   return false;
    // }

    try {
      const result = await this._repository
      .createQueryBuilder('db')
      .setLock('pessimistic_write')
      .insert()
      .into(InterfaceImagesUrlEntity)
      .values(params)
      .execute();      
      return result;
    } catch (error) {
      return false;
    }



    // this._interfaceImagesRepository.manager.transaction(async (manager) => {
      // manager.save;
    // });
    // const result = await this._interfaceImagesRepository.save(params);
  }

  // 查询图片尺寸信息
  async findOneImageQualityRecord<T>(query: InterfaceImagesUrlEntity) {
    // let entity = new InterfaceImagesUrlEntity();
    // for (let key in query) {
    //   entity[key] = query[key];
    // }
    // const newParams = entity;
    console.log('query', query);

    // 获取连接并创建新的queryRunner
    // await this.queryRunner.connect();
    // // 开始事务：
    // await this.queryRunner.startTransaction();
    // await this.queryRunner.manager.findOne(InterfaceImagesUrlEntity, {
    //   where: query,
    // });
    // const result = await this.queryRunner.commitTransaction();
    // console.log('result', result);
    // await this.queryRunner.manager.save([]);

    // return result;
    try {
      const result = await this._interfaceImagesRepository.findOne(query);
      return result;
    } catch (error) {
      console.log('error', error);
      return false
    }
  }

  // 查询图片尺寸信息列表
  async findListImageQualityRecord<T>(query: ListQuery) {
    const result = await this._interfaceImagesRepository.find(query);
    return result;
  }

  async findListImageQualityRecordByTime<T>(query) {
    const { start, end, appkeys, pageQueryKey, mrnComponent, mrnEntry, imgMiniSize = 200 } = query;

    if (pageQueryKey === PageQueryKey.URL) {
      const result = await this._interfaceImagesRepository
        .createQueryBuilder('db')
        .where('db.time BETWEEN :start AND :end', {
          start: start,
          end: end,
        })
      mrnEntry && result.andWhere('db.pageUrl LIKE :value2', { value2: `%${mrnEntry}%` })
      mrnComponent && result.andWhere('db.pageUrl LIKE :value1', { value1: `%${mrnComponent}%` })
      appkeys && result.andWhere('db.appkey IN (:...appkey)', { appkey: appkeys })

      result.andWhere(`(db.imageSize > ${imgMiniSize} OR (db.pixelWidth * db.pixelHeight) > 50000000)`)
      return result.getMany();
    } else {
      const result = await this._interfaceImagesRepository.createQueryBuilder('db')
      .where('db.time BETWEEN :start AND :end', {
        start: start,
        end: end
      })
      .andWhere('db.appkey IN (:...appkey)', { appkey: appkeys })
      .andWhere(`(db.imageSize > ${imgMiniSize} OR (db.pixelWidth * db.pixelHeight) > 50000000)`)
      .getMany();
      return result;
    }
  }
}
