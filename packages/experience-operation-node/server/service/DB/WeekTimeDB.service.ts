/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-29 16:55:35
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-07 21:32:10
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/node-manage-monorepo/packages/experience-operation-node/server/service/DB/WeekTimeDB.service.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Service, BaseService, DBService } from '@gfe/moa'
import { WeekTimeEntity } from '../../entities/weektime.entity'


@Service()
export class WeekTimeDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super()
  }

  // 查询
  async findWeekTimeRecord(delayType: string, apis?: string[]) {
    const repository = this.dbService.getRepository(WeekTimeEntity)
    const type = delayType === 'avg' ? 'avg_totalDuration': 'P90'
    const result = await repository
      .createQueryBuilder('db')
      .select('api', 'name')
      .addSelect('api_en', 'path')
      .addSelect(type, delayType)
      .addSelect('week', 'week')
      .addSelect('year', 'year')
      .where('api_en IN (:...api_en)', { api_en: apis })
      .execute()
    return result
  }

  // 插入
  async insertWeekTimeRecord(value: string) {
    const repository = this.dbService.getRepository(WeekTimeEntity)
    const result = await repository
      .createQueryBuilder('db')
      .insert()
      .into('fe_interface_week_time')
      .values(value)
      .execute()

    return result
  }
}
