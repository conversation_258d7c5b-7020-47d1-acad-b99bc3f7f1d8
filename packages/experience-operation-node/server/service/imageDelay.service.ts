/*
 * @Author: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-10-30 11:18:25
 * @LastEditors: liz<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-11-06 19:41:04
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/service/transaction.service.ts
 */
import { BaseService, Service } from '@gfe/moa';
import { PageImageDelayDBService } from './DB/ImageDelayAVGDB';
import { InterfaceImagesUrlEntity } from '../entities/interfaceImagesUrl.entity';
import moment from 'moment';

@Service()
export default class TransactionService extends BaseService {
  constructor(protected imageDelayDBS: PageImageDelayDBService) {
    super();
  }

  async getImageDelay(query) {
    const { dimension } = query;
    if (dimension == 'hour') {
      const res = await this.imageDelayDBS.findAverageByHour(query);
      return res;
    }
    if (dimension == 'day') {
      const res = await this.imageDelayDBS.findAverageByDay(query);
      return res;
    }
  }

  async imageQualityUploadDB(param: InterfaceImagesUrlEntity[]) {
    // await this.imageDelayDBS.updateImageQualityDB(param);
    await this.imageDelayDBS.saveImageQualityDB(param);
  }

  queryImageQualityInfo(param) {
    return this.imageDelayDBS.findListImageQualityRecord(param);
  }

  queryOneImageQualityInfo(param) {
    return this.imageDelayDBS.findOneImageQualityRecord(param);
  }

  async queryImageQualityListByTime(param) {
    const data = await this.imageDelayDBS.findListImageQualityRecordByTime(
      param
    );
    let result = data.reduce((acc, cur: any) => {
      let key = cur.api;
      if (!acc[key]) {
        acc[key] = [];
      }
      let exist = acc[key].find((item) => item.imageUrl === cur.imageUrl);
      if (!exist) {
        acc[key].push(cur);
      }
      return acc;
    }, {});
    return result;
  }

  // 获取图片像素大小
  async queryImageConfigFromVenusUserTest() {

  }
}
