/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> liz<PERSON><EMAIL>
 * @Date: 2023-10-30 11:18:25
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-11-21 16:06:29
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/service/transaction.service.ts
 */
import { BaseService, Service } from '@gfe/moa';
import { modHttp, catHttp, CatRequestBody } from '../utils/modHttp';
import moment from 'moment';
import dayjs from 'dayjs';
import { enumerateDaysBetweenDates, dayjsFormat } from '../utils';

interface Filter {
  filter: string;
  groupBy: boolean;
  tagk: string;
  type: 'literal_or' | 'wildcard';
}
interface Querie {
  aggregator: string;
  filters: Filter[];
  metric: string;
  downsample?: string;
  rate: boolean;
}
interface ModRequestBody {
  end: string | number;
  start: string | number;
  queries: Querie[];
  namespace: string;
}

@Service()
export default class TransactionService extends BaseService {
  constructor() {
    super();
  }
  async getTransactionTP90({ type, end, start, appKey, name }) {
    const res = await this.getTransactionDelay({
      metric: `name_tp90_${type}`,
      end,
      start,
      appKey,
      name,
    });
    return res;
  }

  async getTransactionAvg({ type, end, start, appKey, name }) {
    const res = await this.getTransactionDelay({
      metric: `name_avg_${type}`,
      end,
      start,
      appKey,
      name,
    });
    return res;
  }

  async getTransactionTerminalDelay(terminal, query) {
    const { start, end, dimension } = query;

    const daysList = enumerateDaysBetweenDates(start, end);


    if (dimension == 'min') {
      const aggregateInterfacePromise = terminal.map((item) => {
        return this.getTransactionTerminalDelayFromCatByMinutes(
          item.commandId,
          start,
          end
        );
      });
      const terminalResult = await Promise.all(aggregateInterfacePromise);

      const returnDelay = terminalResult.map((data, index) => {
        // 筛选出每五分钟的数据
        const filteredData = data.filter((item, index) => {
          return index % 5 === 0;
        });

        let time = start;

        let currentTime = dayjsFormat(time);

        // 将数据转换为对象
        const result = filteredData.reduce((obj, value) => {
          const timeKey = currentTime.format('YYYY-MM-DD HH:mm:ss');
          obj[timeKey] = value;
          currentTime = currentTime.add(5, 'minute');
          return obj;
        }, {});

        return {
          name: terminal[index].showName,
          dps: result,
        };
      });
      return returnDelay;
    } else {      
      const aggregateInterfacePromise = terminal.map((item) => {
        return this.getTransactionTerminalDelayFromCatByDay(
          item.commandId,
          start,
          end
        );
      });
      const terminalResult = await Promise.all(aggregateInterfacePromise);

      const returnDelay = terminalResult.map((data, index) => {
        const dps = data.reduce((obj, item, index) => {
          const key = moment(daysList[index]).format('YYYY-MM-DD');
          obj[key] = item;
          return obj;
        }, {});

        return {
          name: terminal[index].showName,
          dps: dps,
        };
      });
      return returnDelay;
    }
  }

  async getTransactionTerminalDelayFromCatByMinutes(
    commandId,
    dateStart,
    dateEnd
  ) {
    let startDate = dayjsFormat(dateStart);
    let endDate = dayjsFormat(dateEnd);

    let currentDate = startDate;
    let results = [];

    let startDay = startDate.format('YYYY-MM-DD');

    while (startDate.isBefore(endDate)) {
      let startTime;
      let endTime;
      if (startDate.isSame(currentDate, 'day')) {
        startTime = startDate.format('HH:mm:ss');
      } else {
        startTime = '00:00:00';
      }

      if (startDate.isSame(endDate, 'day')) {
        endTime = endDate.format('HH:mm:ss');
      } else {
        endTime = '23:59:00';
      }

      // 如果下一个日期超过了结束日期，那么就将下一个日期设置为结束日期
      // if (nextDate.isAfter(endDate)) {
      // nextDate = endDate;
      // }

      const data: CatRequestBody = {
        date: startDate.format('YYYY-MM-DD'),
        commandId: commandId,
        start: startTime,
        end: endTime,
        op: 'mobileApiMinuteData',
        isDaily: false,
        type: 'delay',
      };

      startDate = startDate.add(1, 'day');

      const res = await catHttp('/cat/r/mobileapi', data);

      const weeHours = startDate.startOf('day');

      let resData;

      if (startTime === '00:00:00') {
        resData = res.data;
      } else {
        let diff = startDate.diff(weeHours, 'minute');
        resData = res.data.slice(diff);
      }

      let remainder = resData.length % 5; // 计算余数

      if (remainder !== 0) {
        // 如果余数不为0，即数组长度不是5的倍数
        let fillCount = 5 - remainder; // 计算需要补充的元素数量
        for (let i = 0; i < fillCount; i++) {
          resData.push(null); // 在数组末尾添加null
        }
      }
      results = results.concat(resData);
    }

    return results;
  }

  async getTransactionTerminalDelayFromCatByDay(commandId, dateStart, dateEnd) {
    const date = dayjs(+dateStart).format('YYYY-MM-DD');
    const endDate = dayjs(+dateEnd).format('YYYY-MM-DD');
    const start = dayjs(+dateStart).format('HH:mm');
    const end = dayjs(+dateEnd).format('HH:mm');
    const data: CatRequestBody = {
      date: date,
      endDate: endDate,
      commandId: commandId,
      start: start,
      end: end,
      op: 'mobileApiGroupByDayData',
      isDaily: true,
      type: 'delay',
    };
    const res = await catHttp('/cat/r/mobileapi', data);
    return res.data;
  }

  async getTransactionTerminalDelayFromCatByWeek(query) {
    const { start, end, commandId } = query;
    const data: CatRequestBody = {
      date: start,
      endDate: end,
      start: '00:00',
      end: '23:59',
      commandId: commandId,
      op: 'mobileApiSummaryData',
      isDaily: true,
      type: 'delay',
    }
    const res = await catHttp('/cat/r/mobileapi', data);
    return res.data
  }

  async getTransactionTerminalDelayTP90FromCatByWeek(query) {
    const { start, end, commandId } = query;
    const data: CatRequestBody = {
      startDate: start,
      endDate: end,
      commandId: commandId,
      op: 'mobileApiDailyDuration',
    }
    const res = await catHttp('/cat/r/mobileapi', data);
    return res.data
  }

  async getTransactionDelay({ metric, end, start, appKey, name }) {
    const currentDate = moment();
    const previousDate = currentDate.subtract(1, 'day');
    const startTime = start || previousDate.valueOf();
    const endTime = end || moment().valueOf();
    const data = {
      namespace: 'transaction',
      end: endTime,
      start: startTime,
      queries: [
        {
          aggregator: 'zimsum',
          filters: [
            {
              filter: appKey,
              groupBy: false,
              tagk: 'app',
              type: 'literal_or',
            },
            {
              filter: name,
              groupBy: false,
              tagk: 'name',
              type: 'literal_or',
            },
          ],
          metric: metric,
          rate: false,
        },
      ],
    } as ModRequestBody;
    const res = await modHttp('/metric/multi', data);
    return res.data;
  }
}
