/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-29 17:12:41
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-07 20:50:48
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/node-manage-monorepo/packages/experience-operation-node/server/service/weektime.service.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Moa, { BaseService, Service } from '@gfe/moa'
import { Not } from 'typeorm'
import { WeekTimeDBService } from "./DB/WeekTimeDB.service"

@Service()
export default class WeekTimeService extends BaseService {
  constructor(protected weekTimeDBService: WeekTimeDBService,) {
    super()
  }

  async getWeekTimeData(delayType, apis) {
    const res = (await this.weekTimeDBService.findWeekTimeRecord(delayType, apis))
    const hash = {}
    res.forEach(item => {
      if(!hash[item.name]) {
        hash[item.name] = []
      } 

      hash[item.name].push({
        week: `${item.year}${item.week}`,
        [delayType]: item[delayType]
      })
    })
   
    return hash
  }

  setWeekTimeData(value) {
    this.weekTimeDBService.insertWeekTimeRecord(value)
  }
}
