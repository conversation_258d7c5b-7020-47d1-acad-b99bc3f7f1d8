const DaXiang = require('@mtfe/daxiang')

async function initDaxiang(clientConfig?: object) {
  return new DaXiang(clientConfig)
}

/**
 * 发送简单大象消息
 * @param {Array|String} contacts mis号数组/mis号
 * @param {String} message 消息
 */
export async function daXiangpush(
  contacts: string | Array<string>,
  message: string,
) {
  const neixin = await initDaxiang({
    Name: 'Image chack',
    AppId: 1,
    PubId: '138212634216',
    Key: '800693014219O12v',
    Token: '7128da4f5d2465517e5d8a496ac917bf',
  })

  if (!message) return
  if (!neixin) return
  if (typeof contacts === 'string') contacts = [contacts]

  return neixin.send(message, contacts).catch((err) => {
    throw new Error(`DaxiangPush Failure: ${err}`)
  })
}

export async function daXiangGroupPush(
  group: string,
  message: string,
) {
  const neixin = await initDaxiang({
    Name: 'Image chack',
    AppId: 1,
    PubId: '138212634216',
    Key: '800693014219O12v',
    Token: '7128da4f5d2465517e5d8a496ac917bf',
  })

  if (!message) return
  if (!neixin) return

  return neixin.group(message, group).catch((err) => {
    throw new Error(`DaxiangPush Failure: ${err}`)
  })
}
