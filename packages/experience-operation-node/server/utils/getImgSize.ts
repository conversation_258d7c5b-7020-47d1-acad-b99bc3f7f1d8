import axios from 'axios';
const url = require('url');
const http = require('http');
const sizeOf = require('image-size');

export async function getImgSize(imgURL: string): Promise<any> {
  if (!imgURL) {
    return '-1';
  }

  return new Promise(function (resolve, reject) {
    try {
      axios
        .head(imgURL)
        .then((res) => {
          let size: any = res.headers['content-length'];
          size = size / 1000;
          size = size.toFixed(2);
          resolve(size);
        })
        .catch((err) => {
          resolve('-2');
        });
    } catch (error) {
      resolve('-3');
    }
  });
}

export async function getImgPixelSize(imgURL: string): Promise<any> {
  // 图片像素大小
  const imgUrl = new URL(imgURL)
  let imgPixelSize = null
  let pixelOutofSize = false
  if (imgUrl) {
    let filename = imgUrl.pathname
    if (filename) {
      // 删除无效路径“/0.0/”
      filename = filename.replace('/0.0', '')
      const nameSplit = filename.split('.')
      if (nameSplit && nameSplit.length > 2) {
        filename = `${nameSplit[0]}.${nameSplit[1]}`
      } else if (nameSplit.length > 1){
        const realType = ['jpg', 'JPG', 'jpeg', 'JPEG', 'png', 'PNG', 'gif', 'GIF', 'webp', 'WEBP'].find((type) => {
          if (nameSplit[1].includes(type)) {
            return true
          }
          return false
        })
        if (realType) {
          filename = `${nameSplit[0]}.${realType}`
        }
      }
      imgPixelSize = await imageConfigHttp({
        filename
      }).catch(e => {
        return null
      })

      if (imgPixelSize) {
        // 宽度或者高度大于10000，或者像素大于50000000
        if (imgPixelSize.width > 10000 || imgPixelSize.height > 10000 || imgPixelSize.width * imgPixelSize.height > 50000000) {
          console.log('像素大图！！，', imgPixelSize.width, imgPixelSize.height)
          pixelOutofSize = true
        } else if (imgPixelSize.format === 'gif' || imgPixelSize.format === 'GIF') {
          if (imgPixelSize.frame_num > 150) {
            console.log('动图大图！！ frame_num: ', imgPixelSize.frame_num)
            pixelOutofSize = true
          }
        } else {
          console.log('像素正常，', imgPixelSize.width, imgPixelSize.height)
        }
      } else {
        console.log('图片像素解析失败，', imgURL, filename)
      }

      return {
        imgPixelSize,
        pixelOutofSize
      }
    } else {
      return null
    }
  } else {
    return null
  }
}

export function imageConfigHttp(params: any): Promise<any> {
  const host = 'http://pic-in.vip.sankuai.com'
  let _requestUrl = `${host}/storage/query/venususertest`
  return axios.post(_requestUrl, {}, {
    params: params,
  })
    .then((res) => {
      if (res && res.data && res.data.data && res.data.success){
        return res.data.data
      }
    })
    .catch((e) => {
      throw new Error(`调用venus 接口失败: ${e}`)
  })
}

// const img: any = await axois.get(
//   'https://p1.meituan.net/travelcube/3088d436e78b581e50f8da9f2d1b7ccf411154.png'
// );

// console.log('size2222', img.data.length / 1024);

// const buffer = Buffer.concat(img);
// console.log('buffer', sizeOf(buffer));

// const imgUrl =
//   'http://p1.meituan.net/travelcube/3088d436e78b581e50f8da9f2d1b7ccf411154.png';
// const options = url.parse(imgUrl);

// http.get(options, function (response) {
//   const chunks = [];
//   response
//     .on('data', function (chunk) {
//       chunks.push(chunk);
//     })
//     .on('end', function () {
//       const buffer = Buffer.concat(chunks);
//       console.log('sizeOf', sizeOf(buffer));
//     });
// });
