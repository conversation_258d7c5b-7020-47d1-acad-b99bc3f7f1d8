import { HttpException } from '@gfe/moa'

export interface HTTPResObj<T> {
  code: number
  massage: string
  data: T
}

export function setHttpResult<T>(
  data: T,
  code: number = 200,
  massage: string = 'success',
): HTTPResObj<T> {
  return {
    data,
    code,
    massage,
  }
}

export function setHttpError(err: Error | HttpException) {
  if (!err) {
    return new HttpException('Internal Server Error', 500)
  }
  return new HttpException(err.message, 500)
}
