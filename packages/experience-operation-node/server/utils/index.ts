export * from './http';
import moment from 'moment';
import dayjs from 'dayjs';

import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(isSameOrBefore);

/**
 * 删除对象的空值key
 * @param obj 简单对象
 */
export function deleteEmptyValueKey(obj: Object) {
  const objCopy = JSON.parse(JSON.stringify(obj));
  Object.keys(objCopy).forEach((key) => {
    if (!objCopy[key]) {
      delete objCopy[key];
    }
  });

  return objCopy;
}

export function uniqueArr(arr: Array<any>) {
  return Array.from(new Set(arr));
}

export function getAvatar(mis: string) {
  return `https://serverless.sankuai.com/dx-avatar/?type=img&mis=${mis.trim()}`;
}
export function isEmpty(obj) {
  if (typeof obj === 'undefined' || obj == null || obj === '') {
    return true;
  } else {
    return false;
  }
}

export function hasAppkey(pageConfig, pageKey) {
  const hasKey = Object.keys(pageConfig).find((key) => {
    return pageKey === key;
  });
  if (!hasKey) {
    throw new Error(`pageKey ${pageKey} 不存在`);
  }
  return hasKey;
}

export function isNecessaryParamsReq(paramsArr: Array<string>, query: Object) {
  return paramsArr
    .map((param) => {
      if (isEmpty(query[param])) {
        throw new Error(`未填写${param}`);
      } else {
        return false;
      }
    })
    .every((isEmpty) => isEmpty === false);
}

/**
 *
 * @param arr 要查询的数组
 * @param key key 名
 * @param value value 值
 */
export function getDataBykeyFromArray<T>(
  arr: Array<T>,
  key: string,
  value: any
) {
  let res: T;
  Array.isArray(arr) &&
    arr.forEach((item) => {
      if (item[key] == value) {
        res = item;
      }
    });
  return res;
}

export function string2Ary(str: string) {
  return str.split(',').filter((i) => i);
}

export function enumerateDaysBetweenDates(start, end) {
  const startDate = dayjsFormat(start);
  const endDate = dayjsFormat(end);
  const daysList = [];

  for (
    let date = startDate;
    date.isSameOrBefore(endDate);
    date = date.add(1, 'day')
  ) {
    daysList.push(date.format('YYYYMMDD'));
  }

  return daysList;

  // let daysList = [];
  // let SDate = dayjsFormat(startDate);
  // let EDate = dayjsFormat(endDate);
  // let currentDate = SDate;

  // daysList.push(currentDate.format('YYYYMMDD'));
  // console.log('daysList', daysList);

  // while (currentDate.isBefore(EDate) && !currentDate.isSame(EDate, 'day')) {
  //   currentDate = SDate.add(1, 'days')
  //   daysList.push(currentDate.format('YYYYMMDD'));
  // }
  // return daysList;
}

export function dayjsFormat(time) {
  // 如果 time 是数字字符串，我们假设它是一个时间戳字符串
  // 如果 time 是非数字字符串，我们假设它是一个年月日字符串
  const isTimestamp = /^\d+$/.test(time);
  const dayjsObject = dayjs(isTimestamp ? Number(time) : time);
  return dayjsObject;
}
