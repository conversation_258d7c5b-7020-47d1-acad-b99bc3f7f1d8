/*
 * @Author: l<PERSON><PERSON><PERSON><PERSON> lizhi<PERSON>@meituan.com
 * @Date: 2023-11-06 15:33:27
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-11-06 19:26:45
 * @FilePath: /node-manage-monorepo/packages/experience-operation-node/server/utils/modHttp.ts
 */

export interface CatRequestBody {
  commandId: string | number,
  date?: string,
  startDate?: string,
  endDate?: string,
  op: 'mobileApiMinuteData' | 'mobileApiGroupByDayData' | 'mobileApiSummaryData' | 'mobileApiDailyDuration',
  type?: 'delay',
  isDaily?: boolean,
  start?: string,
  end?: string,
}

import axois from 'axios'
import { TOKEN, ModOpenApiHost, CatOpenApiHost, CAT_TOKEN } from '../config/ModApi'
export async function modHttp(
  requestUrl: string,
  data: any,
): Promise<any> {
  let _requestUrl = ''
  const host = ModOpenApiHost
  _requestUrl = `${host}${requestUrl}`
  return axois
    .post(_requestUrl, data, {
      params: {
        token: TOKEN
      },
    })
    .then((res) => {
      if (res){
        return res
      }
    })
    .catch((e) => {
      throw new Error(`调用Mod http post接口失败: ${e}`)
    })
}

export function catHttp(
  requestUrl: string,
  data: CatRequestBody,
): Promise<any> {
  let _requestUrl = ''
  const host = CatOpenApiHost
  _requestUrl = `${host}${requestUrl}`
  return axois
    .get(_requestUrl, {
      params: {
        token: CAT_TOKEN,
        ...data,
      },
    })
    .then((res) => {
      if (res){
        return res
      }
    })
    .catch((e) => {
      throw new Error(`调用Cat http get接口失败: ${e}`)
    })
}