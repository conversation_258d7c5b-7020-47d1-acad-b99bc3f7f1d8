# serverless.yml
service: experience-operation-node

provider:
  environment:
    TEST_ENV: value # 可以向函数运行时的process.env内注入自定义字段

plugins: # 后续将会提供部分平台默认能力，业务方无需安装npm包，在此目录下声明即可调用相关能力

functions:
  main:
    handler: index.main  # 表示将 index.js 中的 main 方法作为 handler
    timeout: 100
    events: # events字段仅在本地运行时生效，生产环境需进入平台进行触发器设定
      - http:
          path: (.*) 
          method: get # 支持的请求方法，支持常见的请求类型以及any(全类型)
