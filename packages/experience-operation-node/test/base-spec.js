const assert = require('assert')
const { main } = require('..')
const mockEvent = require('./mock-event')

describe('basic', function () {
  let event
  beforeEach(() => {
    event = mockEvent()
  })
  it('http.get', async () => {
    const res = await main(event, {})
    assert.deepStrictEqual(res.status, 200)
    assert(res.headers['content-type'])
    assert(res.body)
  })
  it('http.get 404', async () => {
    event.extensions.request.path = '/404/'
    event.extensions.request.url = '/404/'
    event.extensions.request.originalUrl = '/404/'
    const res = await main(event, {})
    assert.deepStrictEqual(res.status, 404)
  })
})
