module.exports = () => {
  return {
    "cloudEventsVersion": "0.1",
    "eventTypeVersion": "1.0",
    "eventType": "http.eracloud.io",
    "eventID": "5fbf38de-baae-4f2a-b5a2-528200ca2ada",
    "eventTime": "2018-12-13T09:04:33.958Z",
    "eventNamespace": "http.trigger.eracloud.io",
    "source": "/koa-mini/",
    "contentType": "application/json",
    "extensions": {
      "query": {},
      "path": "/koa-mini/",
      "request": {
        "method": "GET",
        "url": "/koa-mini/",
        "header": {
          "host": "127.0.0.1:8080",
          "connection": "keep-alive",
          "pragma": "no-cache",
          "cache-control": "no-cache",
          "upgrade-insecure-requests": "1",
          "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.80 Safari/537.36",
          "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
          "accept-encoding": "gzip, deflate, br",
          "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,ru;q=0.6,sq;q=0.5,fr;q=0.4,nb;q=0.3",
          "cookie": "_lxsdk_cuid=1679249c25fc8-0fc8274a8d2fff-113c6654-13c680-1679249c25fc8; _lxsdk=1679249c25fc8-0fc8274a8d2fff-113c6654-13c680-1679249c25fc8; 73070914_ssoid=1dbff6ec1a*6444181244d1a54801318; csrfToken=di-mCZDsBuLxXtCvPploUqsz"
        },
        "body": {},
        "path": "/koa-mini/",
        "originalUrl": "/koa-mini/",
        "protocol": "http",
        "host": "127.0.0.1:8080",
        "hostname": "127.0.0.1",
        "ip": "::ffff:127.0.0.1"
      },
      "response": null
    }
  }
}
