# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.22.13", "@babel/code-frame@^7.23.5":
  version "7.23.5"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.23.5.tgz#9009b69a8c602293476ad598ff53e4562e15c244"
  integrity sha1-kAm2moxgIpNHatWY/1PkVi4VwkQ=
  dependencies:
    "@babel/highlight" "^7.23.4"
    chalk "^2.4.2"

"@babel/generator@^7.23.6", "@babel/generator@^7.4.0":
  version "7.23.6"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.23.6.tgz#9e1fca4811c77a10580d17d26b57b036133f3c2e"
  integrity sha1-nh/KSBHHehBYDRfSa1ewNhM/PC4=
  dependencies:
    "@babel/types" "^7.23.6"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-environment-visitor@^7.22.20":
  version "7.22.20"
  resolved "http://r.npm.sankuai.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.22.20.tgz#96159db61d34a29dba454c959f5ae4a649ba9167"
  integrity sha1-lhWdth00op26RUyVn1rkpkm6kWc=

"@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.23.0.tgz#1f9a3cdbd5b2698a670c30d2735f9af95ed52759"
  integrity sha1-H5o829WyaYpnDDDSc1+a+V7VJ1k=
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.22.5.tgz#c01a007dac05c085914e8fb652b339db50d823bb"
  integrity sha1-wBoAfawFwIWRTo+2UrM521DYI7s=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.22.6":
  version "7.22.6"
  resolved "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.22.6.tgz#322c61b7310c0997fe4c323955667f18fcefb91c"
  integrity sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw=
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.23.4":
  version "7.23.4"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.23.4.tgz#9478c707febcbbe1ddb38a3d91a2e054ae622d83"
  integrity sha1-lHjHB/68u+Hds4o9kaLgVK5iLYM=

"@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.22.20.tgz#c4ae002c61d2879e724581d96665583dbc1dc0e0"
  integrity sha1-xK4ALGHSh55yRYHZZmVYPbwdwOA=

"@babel/highlight@^7.23.4":
  version "7.23.4"
  resolved "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.23.4.tgz#edaadf4d8232e1a961432db785091207ead0621b"
  integrity sha1-7arfTYIy4alhQy23hQkSB+rQYhs=
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.22.15", "@babel/parser@^7.4.3":
  version "7.23.5"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.23.5.tgz#37dee97c4752af148e1d38c34b856b2507660563"
  integrity sha1-N97pfEdSrxSOHTjDS4VrJQdmBWM=

"@babel/parser@^7.23.6":
  version "7.23.6"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.23.6.tgz#ba1c9e512bda72a47e285ae42aff9d2a635a9e3b"
  integrity sha1-uhyeUSvacqR+KFrkKv+dKmNanjs=

"@babel/runtime@^7.21.0":
  version "7.23.6"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.23.6.tgz#c05e610dc228855dc92ef1b53d07389ed8ab521d"
  integrity sha1-wF5hDcIohV3JLvG1PQc4ntirUh0=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.22.15", "@babel/template@^7.4.0":
  version "7.22.15"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.22.15.tgz#09576efc3830f0430f4548ef971dde1350ef2f38"
  integrity sha1-CVdu/Dgw8EMPRUjvlx3eE1DvLzg=
  dependencies:
    "@babel/code-frame" "^7.22.13"
    "@babel/parser" "^7.22.15"
    "@babel/types" "^7.22.15"

"@babel/traverse@^7.4.3":
  version "7.23.6"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.23.6.tgz#b53526a2367a0dd6edc423637f3d2d0f2521abc5"
  integrity sha1-tTUmojZ6DdbtxCNjfz0tDyUhq8U=
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/generator" "^7.23.6"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.23.6"
    "@babel/types" "^7.23.6"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.22.15", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.23.6", "@babel/types@^7.4.0":
  version "7.23.6"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.23.6.tgz#be33fdb151e1f5a56877d704492c240fc71c7ccd"
  integrity sha1-vjP9sVHh9aVod9cESSwkD8ccfM0=
  dependencies:
    "@babel/helper-string-parser" "^7.23.4"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@bfe/node-sdk-logger@^1.0.8":
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/@bfe/node-sdk-logger/download/@bfe/node-sdk-logger-1.0.9.tgz#41b0c3f82c5d08c98fb5757d170c8b93f5f56007"
  integrity sha1-QbDD+CxdCMmPtXV9FwyLk/X1YAc=
  dependencies:
    winston "2.4.0"

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "http://r.npm.sankuai.com/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz#00629c35a688e05a88b1cda684fb9d5e73f000a1"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@dp/cat-client@^2.0.0", "@dp/cat-client@^2.3.4", "@dp/cat-client@^2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-2.5.1.tgz#7f45f0579878be69d830465961fe578b0140d034"
  integrity sha1-f0XwV5h4vmnYMEZZYf5XiwFA0DQ=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    "@dp/simple-util" "^1.0.0"
    buffer-builder "^0.2.0"
    debug "^2.2.0"
    mkdirp "^0.5.1"
    moment "^2.10.6"
    request "^2.67.0"
    semver "^6.1.2"
    xml2js "^0.4.15"

"@dp/cat-client@^3.0.3", "@dp/cat-client@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-3.0.4.tgz#c72dd18af0f10e22ec8199a0c809209814c4d295"
  integrity sha1-xy3RivDxDiLsgZmgyAkgmBTE0pU=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    "@dp/simple-util" "^1.0.0"
    buffer-builder "^0.2.0"
    debug "^2.2.0"
    mkdirp "^0.5.1"
    moment "^2.10.6"
    node-addon-api "^3.1.0"
    request "^2.67.0"
    semver "^6.1.2"
    xml2js "^0.4.15"

"@dp/cortex4n@^3.0.9":
  version "3.0.10"
  resolved "http://r.npm.sankuai.com/@dp/cortex4n/download/@dp/cortex4n-3.0.10.tgz#3efea18eaba117e168eb747b475df8865b6be2c5"
  integrity sha1-Pv6hjquhF+Fo63R7R134hltr4sU=
  dependencies:
    "@dp/cat-client" "^2.0.0"
    "@dp/keepalive-zookeeper" "^2.1.2"
    "@dp/lion-client" "^2.0.0"
    "@dp/logger-container" "^1.0.0"
    "@dp/pigeon-client" "^2.0.0"
    "@dp/pigeon-util" "^1.0.5"
    "@dp/server-env" "^1.0.1"
    debug "^2.2.0"
    object-hash "^1.1.2"

"@dp/crane-client@^1.0.5":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@dp/crane-client/download/@dp/crane-client-1.0.5.tgz#03eadf9eef248df6f6077ba7dc136a519c4d23a9"
  integrity sha1-A+rfnu8kjfb2B3un3BNqUZxNI6k=
  dependencies:
    "@dp/keepalive-zookeeper" "^1.1.4"
    "@dp/lion-client" "^2.2.0"
    "@dp/simple-util" "^1.1.0"
    ip "^1.1.5"

"@dp/keepalive-zookeeper@^1.1.4":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@dp/keepalive-zookeeper/download/@dp/keepalive-zookeeper-1.2.1.tgz#b86fc882822baf6591595e24300ea50643bfd85b"
  integrity sha1-uG/IgoIrr2WRWV4kMA6lBkO/2Fs=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    "@dp/server-env" "^1.0.0"
    node-zookeeper-client "^0.2.2"

"@dp/keepalive-zookeeper@^2.1.0", "@dp/keepalive-zookeeper@^2.1.2":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/@dp/keepalive-zookeeper/download/@dp/keepalive-zookeeper-2.1.2.tgz#3202303090ec6a12d89fc5ea3cb81ce3fc20550b"
  integrity sha1-MgIwMJDsahLYn8XqPLgc4/wgVQs=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    "@dp/server-env" "^1.0.0"
    node-zookeeper-client "^0.2.2"

"@dp/lion-client@^2.0.0", "@dp/lion-client@^2.2.0", "@dp/lion-client@^2.2.1", "@dp/lion-client@^2.2.3", "@dp/lion-client@^3.0.2", "@dp/lion-client@^3.0.3", "@dp/lion-client@^3.0.6", "@dp/lion-client@^3.1.1":
  version "3.1.8"
  resolved "http://r.npm.sankuai.com/@dp/lion-client/download/@dp/lion-client-3.1.8.tgz#8c178e74e9bd70b8e606398375670f4c429884fd"
  integrity sha1-jBeOdOm9cLjmBjmDdWcPTEKYhP0=
  dependencies:
    "@bfe/node-sdk-logger" "^1.0.8"
    "@dp/logger-container" "^1.2.0"
    "@dp/server-env" "^1.0.3"
    "@dp/simple-util" "^1.1.1"
    "@mtfe/cat" "^1.1.0"
    async "^3.2.4"
    async-lock "^1.4.0"
    imurmurhash "^0.1.4"
    ip "^1.1.5"
    moment "^2.19.4"
    request "^2.88.2"

"@dp/logger-container@^1.0.0", "@dp/logger-container@^1.1.0", "@dp/logger-container@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz#02ea0bc21377eff37a5072ffb514dc70c07cf26f"
  integrity sha1-AuoLwhN37/N6UHL/tRTccMB88m8=

"@dp/middleware-info@^1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@dp/middleware-info/download/@dp/middleware-info-1.0.0.tgz#a3612b1a0084502db69011b669885568048342aa"
  integrity sha1-o2ErGgCEUC22kBG2aYhVaASDQqo=
  dependencies:
    "@dp/lion-client" "^2.2.1"
    "@dp/server-env" "^1.0.1"
    co-request "^1.0.0"
    ip "^1.1.5"

"@dp/node-kms@2.0.5":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.5.tgz#f674aa14672bd54f5f23d1b07b2d0e8722b430f0"
  integrity sha1-9nSqFGcr1U9fI9Gwey0OhyK0MPA=
  dependencies:
    "@dp/server-env" "^1.0.1"
    "@dp/simple-util" "^1.1.1"
    bindings "^1.5.0"
    co-request "^1.0.0"
    ip "^1.1.5"
    node-addon-api "^2.0.0"

"@dp/node-kms@^2.0.0":
  version "2.0.9"
  resolved "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.9.tgz#fef0c36a3aa2aa2501b6df1534f2704dadeb3ae8"
  integrity sha1-/vDDajqiqiUBtt8VNPJwTa3rOug=
  dependencies:
    "@dp/server-env" "^1.0.1"
    "@dp/simple-util" "^1.1.1"
    "@mtfe/cat" "^1.1.0"
    bindings "^1.5.0"
    co-request "^1.0.0"
    ip "^1.1.5"
    node-addon-api "^2.0.0"

"@dp/patriot-sdk@^2.0.2":
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/@dp/patriot-sdk/download/@dp/patriot-sdk-2.0.2.tgz#57549214ceadc9fe5d3be01db2adc34eae3c9a00"
  integrity sha1-V1SSFM6tyf5dO+Adsq3DTq48mgA=
  dependencies:
    "@dp/logger-container" "^1.2.0"
    "@dp/node-kms" "^2.0.0"
    "@dp/simple-util" "^1.1.1"

"@dp/pigeon-client@^2.0.0", "@dp/pigeon-client@^2.5.12", "@dp/pigeon-client@^2.5.13", "@dp/pigeon-client@^2.5.6":
  version "2.5.13"
  resolved "http://r.npm.sankuai.com/@dp/pigeon-client/download/@dp/pigeon-client-2.5.13.tgz#cdff8d12625a6bf7ecf0449253478a41e5381b8f"
  integrity sha1-zf+NEmJaa/fs8ESSU0eKQeU4G48=
  dependencies:
    "@dp/cat-client" "^3.0.4"
    "@dp/keepalive-zookeeper" "^2.1.0"
    "@dp/lion-client" "^3.0.2"
    "@dp/logger-container" "^1.1.0"
    "@dp/middleware-info" "^1.0.0"
    "@dp/server-env" "^1.0.0"
    "@dp/simple-util" "^1.1.0"
    "@mtfe/thrift" "^4.0.4"
    "@types/js-to-java" "^2.4.0"
    byte "~1.0.0"
    debug "^1.0.4"
    is-type-of "~0.3.1"
    js-to-java "^2.3.0"
    request "~2.51.0"

"@dp/pigeon-util@^1.0.2", "@dp/pigeon-util@^1.0.5":
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/@dp/pigeon-util/download/@dp/pigeon-util-1.0.8.tgz#315e2a0b4311e750451f98fbd8b7c4e64b6c1ac8"
  integrity sha1-MV4qC0MR51BFH5j72LfE5ktsGsg=
  dependencies:
    js-to-java "^2.4.0"
    lodash "^4.17.4"

"@dp/pigeon-util@^2.1.3":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@dp/pigeon-util/download/@dp/pigeon-util-2.1.3.tgz#8a8f8b107aba828443e46edefa2aa875533f4137"
  integrity sha1-io+LEHq6goRD5G7e+iqodVM/QTc=
  dependencies:
    "@dp/pigeon-client" "^2.5.12"
    co-request "^1.0.0"
    ejs "^2.6.1"
    fs-extra "^7.0.1"
    js-to-java "^2.4.0"
    lodash "^4.17.4"
    prompts "^1.2.0"
    yargs "^13.3.0"

"@dp/rhino@^1.0.8":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@dp/rhino/download/@dp/rhino-1.2.0.tgz#cace990b9663d3874dbd4e0077f5dc9cb2820a9f"
  integrity sha1-ys6ZC5Zj04dNvU4Ad/XcnLKCCp8=
  dependencies:
    "@dp/cat-client" "^2.5.1"
    "@dp/lion-client" "^3.0.3"
    "@dp/simple-util" "^1.0.0"
    co-request "^1.0.0"
    ip "^1.1.5"
    lodash "^4.17.5"

"@dp/scribe-log@^0.1.10":
  version "0.1.10"
  resolved "http://r.npm.sankuai.com/@dp/scribe-log/download/@dp/scribe-log-0.1.10.tgz#586cd31ffc144c845464866889438809eaccce2b"
  integrity sha1-WGzTH/wUTIRUZIZoiUOICerMzis=
  dependencies:
    "@dp/server-env" "^1.0.1"
    ip "^1.1.5"
    moment "^2.18.1"
    properties "^1.2.1"
    properties-reader "0.0.15"
    uuid "^3.0.1"

"@dp/server-env@^1.0.0", "@dp/server-env@^1.0.1", "@dp/server-env@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz#045a0945a05d46f452d97d060435416c08332731"
  integrity sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    properties-parser "^0.3.1"

"@dp/simple-util@^1.0.0", "@dp/simple-util@^1.1.0", "@dp/simple-util@^1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz#972c7a41511aecbda67a4ac4893b2d3ebf3d5841"
  integrity sha1-lyx6QVEa7L2mekrEiTstPr89WEE=

"@dp/squirrel-proxy-client@^0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@dp/squirrel-proxy-client/download/@dp/squirrel-proxy-client-0.0.2.tgz#03caef3d52f3f9e0dc2b86c3ac1fb8db10b468ca"
  integrity sha1-A8rvPVLz+eDcK4bDrB+42xC0aMo=
  dependencies:
    "@dp/logger-container" "^1.2.0"
    "@dp/pigeon-util" "^1.0.2"
    "@dp/simple-util" "^1.1.0"
    "@mtfe/cat" "^1.1.0"
    bluebird "^3.5.0"
    byte "^2.0.0"
    ip "^1.1.5"
    is-type-of "^1.2.0"
    mocha "6.2.2"
    nyc "14.1.1"
    redis "^2.7.1"
    request "^2.69.0"
    snappyjs "^0.6.0"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.0.tgz#a23514e8fb9af1269d5f7788aa556798d61c6b59"
  integrity sha1-ojUU6Pua8SadX3eIqlVnmNYca1k=
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  version "4.10.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.10.0.tgz#548f6de556857c8bb73bbee70c35dc82a2e74d63"
  integrity sha1-VI9t5VaFfIu3O77nDDXcgqLnTWM=

"@fdfe/ecf-http-adapter@^1.0.21-beta2":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@fdfe/ecf-http-adapter/download/@fdfe/ecf-http-adapter-1.1.0.tgz#e85bcf2b287034b704298c521eef88c2b8a52d44"
  integrity sha1-6FvPKyhwNLcEKYxSHu+IwrilLUQ=
  dependencies:
    koa-compose "^4.1.0"
    on-finished "^2.3.0"

"@fdfe/era-cloud-uploader@^0.11.2":
  version "0.11.5"
  resolved "http://r.npm.sankuai.com/@fdfe/era-cloud-uploader/download/@fdfe/era-cloud-uploader-0.11.5.tgz#5f41b3e43e5f0085fa7fb9f62af3db31c15843bd"
  integrity sha1-X0Gz5D5fAIX6f7n2KvPbMcFYQ70=
  dependencies:
    "@zeit/ncc" "^0.20.5"
    archiver "^3.0.0"
    cac "^5.0.12"
    chalk "^2.4.1"
    debug "^4.1.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    js-yaml "^3.12.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    node-stream-zip "^1.7.0"
    ora "^3.0.0"
    request "^2.88.0"
    rimraf "^2.6.2"
    semver "^6.0.0"
    tar "^6.0.1"

"@gfe/moa@^2.1.0-beta.2":
  version "2.1.0-beta.2"
  resolved "http://r.npm.sankuai.com/@gfe/moa/download/@gfe/moa-2.1.0-beta.2.tgz#822c5785d9e49e9ef573c88772a44b90b25101c9"
  integrity sha1-gixXhdnknp71c8iHcqRLkLJRAck=
  dependencies:
    "@dp/cat-client" "^2.3.4"
    "@dp/cortex4n" "^3.0.9"
    "@dp/lion-client" "^2.2.3"
    "@dp/logger-container" "^1.2.0"
    "@dp/pigeon-client" "^2.5.6"
    "@dp/rhino" "^1.0.8"
    "@dp/scribe-log" "^0.1.10"
    "@gfe/zebra-typeorm-client" "^1.0.2"
    "@mtfe/mns-util" "^2.0.0"
    glob "^7.1.4"
    html-minifier "^4.0.0"
    koa "^2.7.0"
    koa-bodyparser "^4.2.1"
    koa-ejs "^4.2.0"
    koa-logger "^3.2.1"
    koa-router "^7.4.0"
    mkdirp "^0.5.1"
    moment "^2.24.0"
    path-to-regexp "^3.0.0"
    querystring "^0.2.0"
    reflect-metadata "^0.1.13"
    util "^0.12.1"
    winston "1.0.0"

"@gfe/zebra-typeorm-client@^1.0.2":
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/@gfe/zebra-typeorm-client/download/@gfe/zebra-typeorm-client-1.0.7.tgz#ea95bf9b17275d484f2c5407d120cd200d9836ec"
  integrity sha1-6pW/mxcnXUhPLFQH0SDNIA2YNuw=
  dependencies:
    "@dp/cat-client" "^3.0.4"
    "@dp/lion-client" "^3.1.1"
    log4js "^6.1.2"
    mysql "^2.18.1"
    typeorm "^0.2.22"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.3.tgz#7e02e6eb5df901aaedb08514203b096614024098"
  integrity sha1-fgLm6135AartsIUUIDsJZhQCQJg=
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.1.tgz#c08679063f279615a3326583ba3a90d1d82cc721"
  integrity sha1-wIZ5Bj8nlhWjMmWDujqQ0dgsxyE=

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz#7c6cf998d6d20b914c0a55a91ae928ff25965e72"
  integrity sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.4.15"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.15.tgz#d7c6e6755c78567a951e04ab52ef0fd26de59f32"
  integrity sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz#6534fd5933a53ba7cbf3a17615e273a0d1273ff9"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.20"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.20.tgz#72e45707cf240fa6b081d0366f8265b0cd10197f"
  integrity sha1-cuRXB88kD6awgdA2b4JlsM0QGX8=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@mtfe/ba-auth@0.0.5", "@mtfe/ba-auth@^0.0.5":
  version "0.0.5"
  resolved "http://r.npm.sankuai.com/@mtfe/ba-auth/download/@mtfe/ba-auth-0.0.5.tgz#61139f0ca4ccd58592048613937db15838093a86"
  integrity sha1-YROfDKTM1YWSBIYTk32xWDgJOoY=

"@mtfe/ba@*", "@mtfe/ba@^2.1.1", "@mtfe/ba@^2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@mtfe/ba/download/@mtfe/ba-2.1.5.tgz#119b71f4c96a7fa400c54cf6d0ddcefe36db2110"
  integrity sha1-EZtx9Mlqf6QAxUz20N3O/jbbIRA=

"@mtfe/basic-auth@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@mtfe/basic-auth/download/@mtfe/basic-auth-0.3.2.tgz#a2db52f5b72ec741d3f08396b7b53719e6cba6fa"
  integrity sha1-ottS9bcux0HT8IOWt7U3GebLpvo=

"@mtfe/cat@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/cat/download/@mtfe/cat-1.1.0.tgz#37663ef5afbc9d09e0d9b4008406d611ad78d008"
  integrity sha1-N2Y+9a+8nQng2bQAhAbWEa140Ag=
  dependencies:
    "@dp/cat-client" "^3.0.3"
    debug "^4.3.3"

"@mtfe/daxiang@^2.3.2":
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/@mtfe/daxiang/download/@mtfe/daxiang-2.3.2.tgz#0ce0ff9e87ad0a6398265db350aa34d2f0183442"
  integrity sha1-DOD/noetCmOYJl2zUKo00vAYNEI=
  dependencies:
    "@mtfe/ba" "^2.1.5"
    "@mtfe/http" latest
    form-data "^2.3.2"

"@mtfe/http@latest":
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/@mtfe/http/download/@mtfe/http-0.0.3.tgz#763b5c9b202f435e08640fee4fd61d2bbfe1cec6"
  integrity sha1-djtcmyAvQ14IZA/uT9YdK7/hzsY=
  dependencies:
    "@mtfe/ba" "*"
    xttp "*"

"@mtfe/mns-util@^2.0.0", "@mtfe/mns-util@^2.0.1":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@mtfe/mns-util/download/@mtfe/mns-util-2.0.1.tgz#7ed17fb6db9b8292e3ee86523e5d4ab39e0059ca"
  integrity sha1-ftF/ttubgpLj7oZSPl1Ks54AWco=
  dependencies:
    ini "^1.3.5"
    ip "^1.1.5"

"@mtfe/mns-util@^3.1.0":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/mns-util/download/@mtfe/mns-util-3.1.0.tgz#60ca69d1d8f788f18d536cee173dc0b60dd7c955"
  integrity sha1-YMpp0dj3iPGNU2zuFz3Atg3XyVU=
  dependencies:
    debug "^4.1.1"
    ini "^1.3.5"
    ip "^1.1.5"

"@mtfe/octo-auth@^0.2.0":
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-auth/download/@mtfe/octo-auth-0.2.0.tgz#83e82f1752d490aae1e7565104e8840399f717b1"
  integrity sha1-g+gvF1LUkKrh51ZRBOiEA5n3F7E=
  dependencies:
    "@dp/node-kms" "^2.0.0"
    debug "^3.1.0"

"@mtfe/octo-idls@^0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-idls/download/@mtfe/octo-idls-0.1.3.tgz#d9418828c35547567886c4eec46377b05220fdf6"
  integrity sha1-2UGIKMNVR1Z4hsTuxGN3sFIg/fY=
  dependencies:
    thrift "^0.11.0"

"@mtfe/octo-idls@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-idls/download/@mtfe/octo-idls-1.1.0.tgz#92974c35646daaaca2984c7ab03881a930657947"
  integrity sha1-kpdMNWRtqqyimEx6sDiBqTBleUc=
  dependencies:
    thrift "^0.11.0"

"@mtfe/request@^0.10.1":
  version "0.10.1"
  resolved "http://r.npm.sankuai.com/@mtfe/request/download/@mtfe/request-0.10.1.tgz#219b07e85d47702f3b768cca1a76cd522b81dea1"
  integrity sha1-IZsH6F1HcC87dozKGnbNUiuB3qE=
  dependencies:
    "@mtfe/mns-util" "^2.0.0"
    event-emitter "^0.3.3"
    http-proxy-agent "^2.1.0"
    qs "^2.4.1"
    retry "^0.6.1"

"@mtfe/sc-plugin-ba@^0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@mtfe/sc-plugin-ba/download/@mtfe/sc-plugin-ba-0.0.7.tgz#9296a66eed97c356e2eb496d2ade107be767c8ef"
  integrity sha1-kpambu2Xw1bi60ltKt4Qe+dnyO8=
  dependencies:
    "@dp/node-kms" "2.0.5"
    "@mtfe/ba-auth" "0.0.5"
    minimatch "^3.0.4"

"@mtfe/sso-client@^2.3.4":
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-client/download/@mtfe/sso-client-2.6.0.tgz#9d0b73a2a718ae9654c77993b654bff55e6d5bfd"
  integrity sha1-nQtzoqcYrpZUx3mTtlS/9V5tW/0=
  dependencies:
    "@dp/cat-client" "^3.0.4"
    "@mtfe/basic-auth" "^0.3.2"
    "@mtfe/request" "^0.10.1"
    "@mtfe/thrift" "^4.0.4"
    babel-runtime "^6.26.0"
    ejs "^3.1.6"
    ipv4 "^1.0.4"
    koa-convert "^1.2.0"
    minimatch "^3.0.4"
    sdk-base "^3.6.0"

"@mtfe/talostwo-node-sdk@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@mtfe/talostwo-node-sdk/download/@mtfe/talostwo-node-sdk-1.0.1.tgz#c0aaae38e390f71c6eee6bba3503c545c481d874"
  integrity sha1-wKquOOOQ9xxu7mu6NQPFRcSB2HQ=
  dependencies:
    co "^4.6.0"
    co-request "^1.0.0"

"@mtfe/thrift-api-geo@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@mtfe/thrift-api-geo/download/@mtfe/thrift-api-geo-1.0.3.tgz#fbc97565134b53a48f643c6640f842f5c627094b"
  integrity sha1-+8l1ZRNLU6SPZDxmQPhC9cYnCUs=
  dependencies:
    "@mtfe/thrift" "^2.2.7"

"@mtfe/thrift-compiler@^0.1.5":
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/@mtfe/thrift-compiler/download/@mtfe/thrift-compiler-0.1.5.tgz#ede15b6bc2acf6cbc7bd42a8247d866b8a99f91d"
  integrity sha1-7eFba8Ks9svHvUKoJH2Ga4qZ+R0=
  dependencies:
    cross-spawn "^6.0.5"

"@mtfe/thrift@^2.2.7":
  version "2.6.8"
  resolved "http://r.npm.sankuai.com/@mtfe/thrift/download/@mtfe/thrift-2.6.8.tgz#110ab4d329adae00eae88356079cdfed5aeb0c51"
  integrity sha1-EQq00ymtrgDq6INWB5zf7VrrDFE=
  dependencies:
    "@mtfe/mns-util" "^2.0.1"
    "@mtfe/octo-auth" "^0.2.0"
    "@mtfe/octo-idls" "^0.1.0"
    adler-32 "^1.0.0"
    co "^4.6.0"
    debug "^4.1.1"
    int64-transform "^0.1.13"
    ip "^1.1.5"
    memory-cache "^0.2.0"
    retry "^0.10.1"
    snappyjs "^0.5.0"
    thrift "0.11.0"
    uuid "^3.3.2"

"@mtfe/thrift@^4.0.3", "@mtfe/thrift@^4.0.4":
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/@mtfe/thrift/download/@mtfe/thrift-4.1.1.tgz#4219970bf9dd089134609131988139141a2ae065"
  integrity sha1-QhmXC/ndCJE0YJExmIE5FBoq4GU=
  dependencies:
    "@dp/patriot-sdk" "^2.0.2"
    "@mtfe/cat" "^1.1.0"
    "@mtfe/mns-util" "^3.1.0"
    "@mtfe/octo-auth" "^0.2.0"
    "@mtfe/octo-idls" "^1.1.0"
    adler-32 "^1.0.0"
    co "^4.6.0"
    debug "^4.1.1"
    int64-transform "^0.1.13"
    ip "^1.1.5"
    memory-cache "^0.2.0"
    retry "^0.10.1"
    snappyjs "^0.5.0"
    thrift "0.11.0"
    uuid "^3.3.2"

"@mtfe/venus@^0.0.14":
  version "0.0.14"
  resolved "http://r.npm.sankuai.com/@mtfe/venus/download/@mtfe/venus-0.0.14.tgz#dce9f656e671bd74504d31c798a325797aac511d"
  integrity sha1-3On2VuZxvXRQTTHHmKMleXqsUR0=
  dependencies:
    "@mtfe/ba" "^2.1.1"
    form-data "^2.3.2"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@sqltools/formatter@^1.2.2":
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/@sqltools/formatter/download/@sqltools/formatter-1.2.5.tgz#3abc203c79b8c3e90fd6c156a0c62d5403520e12"
  integrity sha1-OrwgPHm4w+kP1sFWoMYtVANSDhI=

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/@tsconfig/node10/download/@tsconfig/node10-1.0.9.tgz#df4907fc07a886922637b15e02d4cebc4c0021b2"
  integrity sha1-30kH/AeohpImN7FeAtTOvEwAIbI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz#ee3def1f27d9ed66dac6e46a295cffb0152e058d"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/@tsconfig/node16/download/@tsconfig/node16-1.0.4.tgz#0b92dcc0cc1c81f6f306a381f28e31b1a56536e9"
  integrity sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=

"@types/accepts@*":
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/@types/accepts/download/@types/accepts-1.3.7.tgz#3b98b1889d2b2386604c2bbbe62e4fb51e95b265"
  integrity sha1-O5ixiJ0rI4ZgTCu75i5PtR6VsmU=
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.5"
  resolved "http://r.npm.sankuai.com/@types/body-parser/download/@types/body-parser-1.19.5.tgz#04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4"
  integrity sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/co-body@^6.1.0":
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/@types/co-body/download/@types/co-body-6.1.0.tgz#b52625390eb0d113c9b697ea92c3ffae7740cdb9"
  integrity sha1-tSYlOQ6w0RPJtpfqksP/rndAzbk=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "http://r.npm.sankuai.com/@types/connect/download/@types/connect-3.4.38.tgz#5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/@types/content-disposition/download/@types/content-disposition-0.5.6.tgz#0f5fa03609f308a7a1a57e0b0afe4b95f1d19740"
  integrity sha1-D1+gNgnzCKehpX4LCv5LlfHRl0A=

"@types/cookies@*":
  version "0.7.8"
  resolved "http://r.npm.sankuai.com/@types/cookies/download/@types/cookies-0.7.8.tgz#16fccd6d58513a9833c527701a90cc96d216bc18"
  integrity sha1-FvzNbVhROpgzxSdwGpDMltIWvBg=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.17.41"
  resolved "http://r.npm.sankuai.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.41.tgz#5077defa630c2e8d28aa9ffc2c01c157c305bef6"
  integrity sha1-UHfe+mMMLo0oqp/8LAHBV8MFvvY=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/@types/express/download/@types/express-4.17.21.tgz#c26d4a151e60efe0084b23dc3369ebc631ed192d"
  integrity sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/formidable@^2.0.5":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@types/formidable/download/@types/formidable-2.0.5.tgz#e54e31d242ef750ac2d05aa163fa0274c8e6ef9c"
  integrity sha1-5U4x0kLvdQrC0FqhY/oCdMjm75w=
  dependencies:
    "@types/node" "*"

"@types/http-assert@*":
  version "1.5.3"
  resolved "http://r.npm.sankuai.com/@types/http-assert/download/@types/http-assert-1.5.3.tgz#ef8e3d1a8d46c387f04ab0f2e8ab8cb0c5078661"
  integrity sha1-7449Go1Gw4fwSrDy6KuMsMUHhmE=

"@types/http-errors@*":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@types/http-errors/download/@types/http-errors-2.0.4.tgz#7eb47726c391b7345a6ec35ad7f4de469cf5ba4f"
  integrity sha1-frR3JsORtzRabsNa1/TeRpz1uk8=

"@types/js-to-java@^2.4.0":
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/@types/js-to-java/download/@types/js-to-java-2.7.1.tgz#2113b2f7e08b13eb27f01379e9c571053c8bd112"
  integrity sha1-IROy9+CLE+sn8BN56cVxBTyL0RI=

"@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://r.npm.sankuai.com/@types/json5/download/@types/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/keygrip@*":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@types/keygrip/download/@types/keygrip-1.0.3.tgz#2286b16ef71d8dea74dab00902ef419a54341bfe"
  integrity sha1-Ioaxbvcdjep02rAJAu9BmlQ0G/4=

"@types/koa-bodyparser@^4.3.5":
  version "4.3.10"
  resolved "http://r.npm.sankuai.com/@types/koa-bodyparser/download/@types/koa-bodyparser-4.3.10.tgz#02b8d3d57579aa7d491d553f1f4058088bfe127f"
  integrity sha1-ArjT1XV5qn1JHVU/H0BYCIv+En8=
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*":
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/@types/koa-compose/download/@types/koa-compose-3.2.6.tgz#17a077786d0ac5eee04c37a7d6c207b3252f6de9"
  integrity sha1-F6B3eG0Kxe7gTDen1sIHsyUvbek=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@^7.4.4":
  version "7.4.4"
  resolved "http://r.npm.sankuai.com/@types/koa-router/download/@types/koa-router-7.4.4.tgz#db72bde3616365d74f00178d5f243c4fce7da572"
  integrity sha1-23K942FjZddPABeNXyQ8T859pXI=
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.13.4", "@types/koa@^2.13.5":
  version "2.13.9"
  resolved "http://r.npm.sankuai.com/@types/koa/download/@types/koa-2.13.9.tgz#8d989ac17d7f033475fbe34c4f906c9287c2041a"
  integrity sha1-jZiawX1/AzR1++NMT5BskofCBBo=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash@^4.14.182":
  version "4.14.202"
  resolved "http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.14.202.tgz#f09dbd2fb082d507178b2f2a5c7e74bd72ff98f8"
  integrity sha1-8J29L7CC1QcXiy8qXH50vXL/mPg=

"@types/md5@^2.3.1":
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/@types/md5/download/@types/md5-2.3.2.tgz#529bb3f8a7e9e9f621094eb76a443f585d882528"
  integrity sha1-Upuz+Kfp6fYhCU63akQ/WF2IJSg=

"@types/mime@*":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/mime/download/@types/mime-3.0.4.tgz#2198ac274de6017b44d941e00261d5bc6a0e0a45"
  integrity sha1-IZisJ03mAXtE2UHgAmHVvGoOCkU=

"@types/mime@^1":
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/@types/mime/download/@types/mime-1.3.5.tgz#1ef302e01cf7d2b5a0fa526790c9123bf1d06690"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/minimist@^1.2.0":
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/@types/minimist/download/@types/minimist-1.2.5.tgz#ec10755e871497bcd83efe927e43ec46e8c0747e"
  integrity sha1-7BB1XocUl7zYPv6SfkPsRujAdH4=

"@types/node@*":
  version "20.10.4"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-20.10.4.tgz#b246fd84d55d5b1b71bf51f964bd514409347198"
  integrity sha1-skb9hNVdWxtxv1H5ZL1RRAk0cZg=
  dependencies:
    undici-types "~5.26.4"

"@types/node@^12.19.0":
  version "12.20.55"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-12.20.55.tgz#c329cbd434c42164f846b909bd6f85b5537f6240"
  integrity sha1-wynL1DTEIWT4RrkJvW+FtVN/YkA=

"@types/qs@*":
  version "6.9.10"
  resolved "http://r.npm.sankuai.com/@types/qs/download/@types/qs-6.9.10.tgz#0af26845b5067e1c9a622658a51f60a3934d51e8"
  integrity sha1-CvJoRbUGfhyaYiZYpR9go5NNUeg=

"@types/range-parser@*":
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz#50ae4353eaaddc04044279812f52c8c65857dbcb"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/semver@^7.3.12":
  version "7.5.6"
  resolved "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.5.6.tgz#c65b2bfce1bec346582c07724e3f8c1017a20339"
  integrity sha1-xlsr/OG+w0ZYLAdyTj+MEBeiAzk=

"@types/send@*":
  version "0.17.4"
  resolved "http://r.npm.sankuai.com/@types/send/download/@types/send-0.17.4.tgz#6619cd24e7270793702e4e6a4b958a9010cfc57a"
  integrity sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.5"
  resolved "http://r.npm.sankuai.com/@types/serve-static/download/@types/serve-static-1.15.5.tgz#15e67500ec40789a1e8c9defc2d32a896f05b033"
  integrity sha1-FeZ1AOxAeJoejJ3vwtMqiW8FsDM=
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/zen-observable@0.8.3":
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/@types/zen-observable/download/@types/zen-observable-0.8.3.tgz#781d360c282436494b32fe7d9f7f8e64b3118aa3"
  integrity sha1-eB02DCgkNklLMv59n3+OZLMRiqM=

"@typescript-eslint/eslint-plugin@^5.4.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz#aeef0328d172b9e37d9bab6dbc13b87ed88977db"
  integrity sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.4.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz#1b63d082d849a2fcae8a569248fbe2ee1b8a56c7"
  integrity sha1-G2PQgthJovyuilaSSPvi7huKVsc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz#d9457ccc6a0b8d6b37d0eb252a23022478c5460c"
  integrity sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz#286f0389c41681376cdad96b309cedd17d70346a"
  integrity sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz#258607e60effa309f067608931c3df6fed41fd2f"
  integrity sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz#7d17794b77fabcac615d6a48fb143330d962eb9b"
  integrity sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz#141e809c71636e4a75daa39faed2fb5f4b10df86"
  integrity sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz#2174011917ce582875954ffe2f6912d5931e353e"
  integrity sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@zeit/ncc@^0.20.5":
  version "0.20.5"
  resolved "http://r.npm.sankuai.com/@zeit/ncc/download/@zeit/ncc-0.20.5.tgz#a41af6e6bcab4a58f4612bae6137f70bce0192e3"
  integrity sha1-pBr25ryrSlj0YSuuYTf3C84BkuM=

accepts@^1.3.5:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-jsx@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.3.1"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-8.3.1.tgz#2f10f5b69329d90ae18c58bf1fa8fccd8b959a43"
  integrity sha1-LxD1tpMp2QrhjFi/H6j8zYuVmkM=

acorn@^7.1.1:
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.4.1:
  version "8.11.2"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.11.2.tgz#ca0d78b51895be5390a5903c5b3bdcdaf78ae40b"
  integrity sha1-yg14tRiVvlOQpZA8Wzvc2veK5As=

address@^1.0.3:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/address/download/address-1.2.2.tgz#2b5248dac5485a6390532c6a517fda2e3faac89e"
  integrity sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=

adler-32@^1.0.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/adler-32/download/adler-32-1.3.1.tgz#1dbf0b36dda0012189a32b3679061932df1821e2"
  integrity sha1-Hb8LNt2gASGJoys2eQYZMt8YIeI=

agent-base@4:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-4.3.0.tgz#8165f01c436009bccad0b1d122f05ed770efc6ee"
  integrity sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4=
  dependencies:
    es6-promisify "^5.0.0"

ajv@^6.10.0, ajv@^6.10.2, ajv@^6.12.3:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-colors@3.2.3:
  version "3.2.3"
  resolved "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-3.2.3.tgz#57d35b8686e851e2cc04c403f1c00203976a1813"
  integrity sha1-V9NbhoboUeLMBMQD8cACA5dqGBM=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0, any-promise@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

app-root-path@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/app-root-path/download/app-root-path-3.1.0.tgz#5971a2fc12ba170369a7a1ef018c71e6e47c2e86"
  integrity sha1-WXGi/BK6FwNpp6HvAYxx5uR8LoY=

append-transform@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/append-transform/download/append-transform-1.0.0.tgz#046a52ae582a228bd72f58acfbe2967c678759ab"
  integrity sha1-BGpSrlgqIovXL1is++KWfGeHWas=
  dependencies:
    default-require-extensions "^2.0.0"

archiver-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/archiver-utils/download/archiver-utils-2.1.0.tgz#e8a460e94b693c3e3da182a098ca6285ba9249e2"
  integrity sha1-6KRg6UtpPD49oYKgmMpihbqSSeI=
  dependencies:
    glob "^7.1.4"
    graceful-fs "^4.2.0"
    lazystream "^1.0.0"
    lodash.defaults "^4.2.0"
    lodash.difference "^4.5.0"
    lodash.flatten "^4.4.0"
    lodash.isplainobject "^4.0.6"
    lodash.union "^4.6.0"
    normalize-path "^3.0.0"
    readable-stream "^2.0.0"

archiver@^3.0.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/archiver/download/archiver-3.1.1.tgz#9db7819d4daf60aec10fe86b16cb9258ced66ea0"
  integrity sha1-nbeBnU2vYK7BD+hrFsuSWM7WbqA=
  dependencies:
    archiver-utils "^2.1.0"
    async "^2.6.3"
    buffer-crc32 "^0.2.1"
    glob "^7.1.4"
    readable-stream "^3.4.0"
    tar-stream "^2.1.0"
    zip-stream "^2.1.2"

archy@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/archy/download/archy-1.0.0.tgz#f9c8c13757cc1dd7bc379ac77b2c62a5c2868c40"
  integrity sha1-+cjBN1fMHde8N5rHeyxipcKGjEA=

arg@^4.1.0:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/arg/download/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.0.tgz#fabe8bc193fea865f317fe7807085ee0dee5aead"
  integrity sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0=
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-includes@^3.1.7:
  version "3.1.7"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.7.tgz#8cd2e01b26f7a3086cbc87271593fe921c62abda"
  integrity sha1-jNLgGyb3owhsvIcnFZP+khxiq9o=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.findlastindex@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.3.tgz#b37598438f97b579166940814e2c0493a4f50207"
  integrity sha1-s3WYQ4+XtXkWaUCBTiwEk6T1Agc=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.2.1"

array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.2.tgz#1476217df8cff17d72ee8f3ba06738db5b387d18"
  integrity sha1-FHYhffjP8X1y7o87oGc421s4fRg=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.2.tgz#c9a7c6831db8e719d6ce639190146c24bbd3e527"
  integrity sha1-yafGgx245xnWzmORkBRsJLvT5Sc=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.reduce@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/array.prototype.reduce/download/array.prototype.reduce-1.0.6.tgz#63149931808c5fc1e1354814923d92d45f7d96d5"
  integrity sha1-YxSZMYCMX8HhNUgUkj2S1F99ltU=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-array-method-boxes-properly "^1.0.0"
    is-string "^1.0.7"

arraybuffer.prototype.slice@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.2.tgz#98bd561953e3e74bb34938e77647179dfe6e9f12"
  integrity sha1-mL1WGVPj50uzSTjndkcXnf5unxI=
  dependencies:
    array-buffer-byte-length "^1.0.0"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-array-buffer "^3.0.2"
    is-shared-array-buffer "^1.0.2"

asap@^2.0.0:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@0.1.11:
  version "0.1.11"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.1.11.tgz#559be18376d08a4ec4dbe80877d27818639b2df7"
  integrity sha1-VZvhg3bQik7E2+gId9J4GGObLfc=

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert-plus@^0.1.5:
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-0.1.5.tgz#ee74009413002d84cec7219c6ac811812e723160"
  integrity sha1-7nQAlBMALYTOxyGcasgRgS5yMWA=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-lock@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/async-lock/download/async-lock-1.4.0.tgz#c8b6630eff68fbbdd8a5b6eb763dac3bfbb8bf02"
  integrity sha1-yLZjDv9o+73Ypbbrdj2sO/u4vwI=

async@0.9.x, async@~0.9.0:
  version "0.9.2"
  resolved "http://r.npm.sankuai.com/async/download/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

async@^2.6.3:
  version "2.6.4"
  resolved "http://r.npm.sankuai.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=
  dependencies:
    lodash "^4.17.14"

async@^3.2.3, async@^3.2.4:
  version "3.2.5"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.5.tgz#ebd52a8fdaf7a2289a24df399f8d8485c8a46b66"
  integrity sha1-69Uqj9r3oiiaJN85n42Ehcika2Y=

async@~0.2.7:
  version "0.2.10"
  resolved "http://r.npm.sankuai.com/async/download/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"
  integrity sha1-trvgsGdLnXGXCMo43owjfLUmw9E=

async@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/async/download/async-1.0.0.tgz#f8fc04ca3a13784ade9e1641af98578cfbd647a9"
  integrity sha1-+PwEyjoTeErenhZBr5hXjPvWR6k=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.5.tgz#92f95616501069d07d10edb2fc37d3e1c65123b7"
  integrity sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=

await-event@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/await-event/download/await-event-2.1.0.tgz#78e9f92684bae4022f9fa0b5f314a11550f9aa76"
  integrity sha1-eOn5JoS65AIvn6C18xShFVD5qnY=

await-first@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/await-first/download/await-first-1.0.0.tgz#06afa6db7cebe412be9be54e82dd8c6cb4cdb241"
  integrity sha1-Bq+m23zr5BK+m+VOgt2MbLTNskE=
  dependencies:
    ee-first "^1.1.1"

aws-sign2@~0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.5.0.tgz#c57103f7a17fc037f02d7c2e64b602ea223f7d63"
  integrity sha1-xXED96F/wDfwLXwuZLYC6iI/fWM=

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.12.0"
  resolved "http://r.npm.sankuai.com/aws4/download/aws4-1.12.0.tgz#ce1c9d143389679e253b314241ea9aa5cec980d3"
  integrity sha1-zhydFDOJZ54lOzFCQeqapc7JgNM=

axios@^0.27.2:
  version "0.27.2"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.27.2.tgz#207658cc8621606e586c85db4b41a750e756d972"
  integrity sha1-IHZYzIYhYG5YbIXbS0GnUOdW2XI=
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bignumber.js@9.0.0:
  version "9.0.0"
  resolved "http://r.npm.sankuai.com/bignumber.js/download/bignumber.js-9.0.0.tgz#805880f84a329b5eac6e7cb6f8274b6d82bdf075"
  integrity sha1-gFiA+Eoym16sbny2+CdLbYK98HU=

bindings@^1.3.0, bindings@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^4.0.3:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bl@~0.9.0:
  version "0.9.5"
  resolved "http://r.npm.sankuai.com/bl/download/bl-0.9.5.tgz#c06b797af085ea00bc527afc8efcf11de2232054"
  integrity sha1-wGt5evCF6gC8Unr8jvzxHeIjIFQ=
  dependencies:
    readable-stream "~1.0.26"

bluebird@^3.5.0:
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

boom@0.4.x:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/boom/download/boom-0.4.2.tgz#7a636e9ded4efcefb19cef4947a3c67dfaee911b"
  integrity sha1-emNune1O/O+xnO9JR6PGffrukRs=
  dependencies:
    hoek "0.9.x"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-stdout@1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/browser-stdout/download/browser-stdout-1.3.1.tgz#baa559ee14ced73452229bad7326467c61fabd60"
  integrity sha1-uqVZ7hTO1zRSIputcyZGfGH6vWA=

buffer-builder@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz#3322cd307d8296dab1f604618593b261a3fade8f"
  integrity sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=

buffer-crc32@^0.2.1, buffer-crc32@^0.2.13:
  version "0.2.13"
  resolved "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"
  integrity sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=

buffer@^5.1.0, buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

byte@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/byte/download/byte-2.0.0.tgz#c6188cf7e4be92daac22f47312f5a1f64091b18a"
  integrity sha1-xhiM9+S+ktqsIvRzEvWh9kCRsYo=
  dependencies:
    debug "^3.1.0"
    long "^4.0.0"
    utility "^1.13.1"

byte@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/byte/download/byte-1.0.0.tgz#ab9590c6ec759f975312800e540bffd9ac463ac1"
  integrity sha1-q5WQxux1n5dTEoAOVAv/2axGOsE=
  dependencies:
    debug "~1.0.4"
    long "~2.1.0"
    utility "~1.1.0"

bytes@3.1.2, bytes@^3.1.0:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cac@^5.0.12:
  version "5.0.16"
  resolved "http://r.npm.sankuai.com/cac/download/cac-5.0.16.tgz#1914697f58a16e863f9bc2bd1b6111662045c147"
  integrity sha1-GRRpf1ihboY/m8K9G2ERZiBFwUc=
  dependencies:
    chalk "^2.4.1"
    joycon "^2.1.2"
    minimost "^1.2.0"
    redent "^2.0.0"
    string-width "^2.1.1"
    text-table "^0.2.0"

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/cache-content-type/download/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

caching-transform@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/caching-transform/download/caching-transform-3.0.2.tgz#601d46b91eca87687a281e71cef99791b0efca70"
  integrity sha1-YB1GuR7Kh2h6KB5xzvmXkbDvynA=
  dependencies:
    hasha "^3.0.0"
    make-dir "^2.0.0"
    package-hash "^3.0.0"
    write-file-atomic "^2.4.2"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.4, call-bind@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.5.tgz#6fa2b7845ce0ea49bf4d8b9ef64727a2c2e2e513"
  integrity sha1-b6K3hFzg6km/TYue9kcnosLi5RM=
  dependencies:
    function-bind "^1.1.2"
    get-intrinsic "^1.2.1"
    set-function-length "^1.1.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/camel-case/download/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  integrity sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

caseless@~0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.8.0.tgz#5bca2881d41437f54b2407ebe34888c7b9ad4f7d"
  integrity sha1-W8oogdQUN/VLJAfr40iIx7mtT30=

chalk@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.1, chalk@^2.1.0, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charenc@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

chownr@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/chownr/download/chownr-2.0.0.tgz#15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece"
  integrity sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=

clean-css@^4.2.1:
  version "4.2.4"
  resolved "http://r.npm.sankuai.com/clean-css/download/clean-css-4.2.4.tgz#733bf46eba4e607c6891ea57c24a989356831178"
  integrity sha1-czv0brpOYHxokepXwkqYk1aDEXg=
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.11:
  version "2.1.11"
  resolved "http://r.npm.sankuai.com/cli-highlight/download/cli-highlight-2.1.11.tgz#49736fa452f0aaf4fae580e30acb26828d2dc1bf"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.0.0:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz#1773a8f4b9c4d6ac31563df53b3fc1d79462fe41"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-5.0.0.tgz#deefcfdb2e800784aa34f46fa08e06851c7bbbc5"
  integrity sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=
  dependencies:
    string-width "^3.1.0"
    strip-ansi "^5.2.0"
    wrap-ansi "^5.1.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co-body@^6.0.0, co-body@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/co-body/download/co-body-6.1.0.tgz#d87a8efc3564f9bfe3aced8ef5cd04c7a8766547"
  integrity sha1-2HqO/DVk+b/jrO2O9c0Ex6h2ZUc=
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co-request@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz#8eb5fb656c2ee1e82e36c4ccfe9376846406b260"
  integrity sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=
  dependencies:
    request "*"

co@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colors@1.0.x:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/colors/download/colors-1.0.3.tgz#0433f44d809680fdeb60ed260f1b0c262e82a40b"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

combined-stream@~0.0.4, combined-stream@~0.0.5:
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-0.0.7.tgz#0137e657baa5a7541c57ac37ac5fc07d73b4dc1f"
  integrity sha1-ATfmV7qlp1QcV6w3rF/AfXO03B8=
  dependencies:
    delayed-stream "0.0.5"

commander@^2.19.0, commander@^2.20.3:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compress-commons@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/compress-commons/download/compress-commons-2.1.1.tgz#9410d9a534cf8435e3fbbb7c6ce48de2dc2f0610"
  integrity sha1-lBDZpTTPhDXj+7t8bOSN4twvBhA=
  dependencies:
    buffer-crc32 "^0.2.13"
    crc32-stream "^3.0.1"
    normalize-path "^3.0.0"
    readable-stream "^2.3.6"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

content-disposition@~0.5.2:
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/content-type/download/content-type-1.0.5.tgz#8b773162656d1d1086784c8f23a54ce6d73d7918"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^1.6.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

cookies@~0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/cookies/download/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90"
  integrity sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-paste@^1.3.0:
  version "1.5.3"
  resolved "http://r.npm.sankuai.com/copy-paste/download/copy-paste-1.5.3.tgz#ee9e775858d05c57a91ea2a063188ab686840797"
  integrity sha1-7p53WFjQXFepHqKgYxiKtoaEB5c=
  dependencies:
    iconv-lite "^0.4.8"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/copy-to/download/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

copyfiles@^2.4.1:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/copyfiles/download/copyfiles-2.4.1.tgz#d2dcff60aaad1015f09d0b66e7f0f1c5cd3c5da5"
  integrity sha1-0tz/YKqtEBXwnQtm5/Dxxc08XaU=
  dependencies:
    glob "^7.0.5"
    minimatch "^3.0.3"
    mkdirp "^1.0.4"
    noms "0.0.0"
    through2 "^2.0.1"
    untildify "^4.0.0"
    yargs "^16.1.0"

core-js@^2.4.0:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@^1.0.1, core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cp-file@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/cp-file/download/cp-file-6.2.0.tgz#40d5ea4a1def2a9acdd07ba5c0b0246ef73dc10d"
  integrity sha1-QNXqSh3vKprN0HulwLAkbvc9wQ0=
  dependencies:
    graceful-fs "^4.1.2"
    make-dir "^2.0.0"
    nested-error-stacks "^2.0.0"
    pify "^4.0.1"
    safe-buffer "^5.0.1"

crc32-stream@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/crc32-stream/download/crc32-stream-3.0.1.tgz#cae6eeed003b0e44d739d279de5ae63b171b4e85"
  integrity sha1-yubu7QA7DkTXOdJ53lrmOxcbToU=
  dependencies:
    crc "^3.4.4"
    readable-stream "^3.4.0"

crc@^3.4.4:
  version "3.8.0"
  resolved "http://r.npm.sankuai.com/crc/download/crc-3.8.0.tgz#ad60269c2c856f8c299e2c4cc0de4556914056c6"
  integrity sha1-rWAmnCyFb4wpnixMwN5FVpFAVsY=
  dependencies:
    buffer "^5.1.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/create-require/download/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^4:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-4.0.2.tgz#7b9247621c23adfdd3856004a823cbe397424d41"
  integrity sha1-e5JHYhwjrf3ThWAEqCPL45dCTUE=
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypt@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

cryptiles@0.2.x:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/cryptiles/download/cryptiles-0.2.2.tgz#ed91ff1f17ad13d3748288594f8a48a0d26f325c"
  integrity sha1-7ZH/HxetE9N0gohZT4pIoNJvMlw=
  dependencies:
    boom "0.4.x"

crypto-js@^4.1.1:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-4.2.0.tgz#4d931639ecdfd12ff80e8186dba6af2c2e856631"
  integrity sha1-TZMWOezf0S/4DoGG26avLC6FZjE=

cssfilter@0.0.10:
  version "0.0.10"
  resolved "http://r.npm.sankuai.com/cssfilter/download/cssfilter-0.0.10.tgz#c6d2672632a2e5c83e013e6864a42ce8defd20ae"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

ctype@0.5.3:
  version "0.5.3"
  resolved "http://r.npm.sankuai.com/ctype/download/ctype-0.5.3.tgz#82c18c2461f74114ef16c135224ad0b9144ca12f"
  integrity sha1-gsGMJGH3QRTvFsE1IkrQuRRMoS8=

cycle@1.0.x:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/cycle/download/cycle-1.0.3.tgz#21e80b2be8580f98b468f379430662b046c34ad2"
  integrity sha1-IegLK+hYD5i0aPN5QwZisEbDStI=

d@1, d@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/d/download/d-1.0.1.tgz#8698095372d58dbee346ffd0c7093f99f8f9eb5a"
  integrity sha1-hpgJU3LVjb7jRv/Qxwk/mfj561o=
  dependencies:
    es5-ext "^0.10.50"
    type "^1.0.1"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

date-fns@^2.30.0:
  version "2.30.0"
  resolved "http://r.npm.sankuai.com/date-fns/download/date-fns-2.30.0.tgz#f367e644839ff57894ec6ac480de40cae4b0f4d0"
  integrity sha1-82fmRIOf9XiU7GrEgN5AyuSw9NA=
  dependencies:
    "@babel/runtime" "^7.21.0"

date-format@^4.0.14:
  version "4.0.14"
  resolved "http://r.npm.sankuai.com/date-format/download/date-format-4.0.14.tgz#7a8e584434fb169a521c8b7aa481f355810d9400"
  integrity sha1-eo5YRDT7FppSHIt6pIHzVYENlAA=

dayjs@^1.11.10:
  version "1.11.10"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.10.tgz#68acea85317a6e164457d6d6947564029a6a16a0"
  integrity sha1-aKzqhTF6bhZEV9bWlHVkAppqFqA=

debug@3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@3.2.6:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@^1.0.4, debug@~1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/debug/download/debug-1.0.5.tgz#f7241217430f99dec4c2b473eab92228e874c2ac"
  integrity sha1-9yQSF0MPmd7EwrRz6rkiKOh0wqw=
  dependencies:
    ms "2.0.0"

debug@^2.2.0, debug@^2.6.1:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.2.7:
  version "3.2.7"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-is@~0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

default-require-extensions@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/default-require-extensions/download/default-require-extensions-2.0.0.tgz#f5f8fbb18a7d6d50b21f641f649ebb522cfe24f7"
  integrity sha1-9fj7sYp9bVCyH2QfZJ67Uiz+JPc=
  dependencies:
    strip-bom "^3.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.1.tgz#c35f7cd0ab09883480d12ac5cb213715587800b3"
  integrity sha1-w1980KsJiDSA0SrFyyE3FVh4ALM=
  dependencies:
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

define-properties@^1.1.2, define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@0.0.5:
  version "0.0.5"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-0.0.5.tgz#d4b1f43a93e8296dfe02694f4680bc37a313c73f"
  integrity sha1-1LH0OpPoKW3+AmlPRoC8N6MTxz8=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^2.0.1:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/denque/download/denque-2.1.0.tgz#e93e1a6569fb5e66f16a3c2a2964617d349d6ab1"
  integrity sha1-6T4aZWn7XmbxajwqKWRhfTSdarE=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

depd@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@^1.0.4:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

dezalgo@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/dezalgo/download/dezalgo-1.0.4.tgz#751235260469084c132157dfa857f386d4c33d81"
  integrity sha1-dRI1JgRpCEwTIVffqFfzhtTDPYE=
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff@3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/diff/download/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

diff@^4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/diff/download/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dotenv@^8.2.0:
  version "8.6.0"
  resolved "http://r.npm.sankuai.com/dotenv/download/dotenv-8.6.0.tgz#061af664d19f7f4d8fc6e4ff9b584ce237adcb8b"
  integrity sha1-Bhr2ZNGff02PxuT/m1hM4jety4s=

double-ended-queue@^2.1.0-0:
  version "2.1.0-0"
  resolved "http://r.npm.sankuai.com/double-ended-queue/download/double-ended-queue-2.1.0-0.tgz#103d3527fd31528f40188130c841efdd78264e5c"
  integrity sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1, ee-first@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-2.7.4.tgz#48661287573dcc53e366c7a1ae52c3a120eec9ba"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

ejs@^3.1.6:
  version "3.1.9"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.9.tgz#03c9e8777fe12686a9effcef22303ca3d8eeb361"
  integrity sha1-A8nod3/hJoap7/zvIjA8o9jus2E=
  dependencies:
    jake "^10.8.5"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.22.1:
  version "1.22.3"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.22.3.tgz#48e79f5573198de6dee3589195727f4f74bc4f32"
  integrity sha1-SOefVXMZjebe41iRlXJ/T3S8TzI=
  dependencies:
    array-buffer-byte-length "^1.0.0"
    arraybuffer.prototype.slice "^1.0.2"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.5"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.2"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.12"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.5.1"
    safe-array-concat "^1.0.1"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.8"
    string.prototype.trimend "^1.0.7"
    string.prototype.trimstart "^1.0.7"
    typed-array-buffer "^1.0.0"
    typed-array-byte-length "^1.0.0"
    typed-array-byte-offset "^1.0.0"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.13"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/es-array-method-boxes-properly/download/es-array-method-boxes-properly-1.0.0.tgz#873f3e84418de4ee19c5be752990b2e44718d09e"
  integrity sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=

es-set-tostringtag@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.2.tgz#11f7cc9f63376930a5f20be4915834f4bc74f9c9"
  integrity sha1-EffMn2M3aTCl8gvkkVg09Lx0+ck=
  dependencies:
    get-intrinsic "^1.2.2"
    has-tostringtag "^1.0.0"
    hasown "^2.0.0"

es-shim-unscopables@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz#1f6942e71ecc7835ed1c8a83006d8771a63a3763"
  integrity sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M=
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz#e55cd4c9cdc188bcefb03b366c736323fc5c898a"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es5-ext@^0.10.35, es5-ext@^0.10.50, es5-ext@~0.10.14:
  version "0.10.62"
  resolved "http://r.npm.sankuai.com/es5-ext/download/es5-ext-0.10.62.tgz#5e6adc19a6da524bf3d1e02bbc8960e5eb49a9a5"
  integrity sha1-XmrcGabaUkvz0eArvIlg5etJqaU=
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    next-tick "^1.1.0"

es6-error@^4.0.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/es6-error/download/es6-error-4.1.1.tgz#9e3af407459deed47e9a91f9b885a84eb05c561d"
  integrity sha1-njr0B0Wd7tR+mpH5uIWoTrBcVh0=

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "http://r.npm.sankuai.com/es6-promise/download/es6-promise-4.2.8.tgz#4eb21594c972bc40553d276e510539143db53e0a"
  integrity sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/es6-promisify/download/es6-promisify-5.0.0.tgz#5109d62f3e56ea967c4b63505aef08291c8a5203"
  integrity sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM=
  dependencies:
    es6-promise "^4.0.3"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/es6-symbol/download/es6-symbol-3.1.3.tgz#bad5d3c1bcdac28269f4cb331e431c78ac705d18"
  integrity sha1-utXTwbzawoJp9MszHkMceKxwXRg=
  dependencies:
    d "^1.0.1"
    ext "^1.1.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

eslint-config-standard@^12.0.0:
  version "12.0.0"
  resolved "http://r.npm.sankuai.com/eslint-config-standard/download/eslint-config-standard-12.0.0.tgz#638b4c65db0bd5a41319f96bba1f15ddad2107d9"
  integrity sha1-Y4tMZdsL1aQTGflruh8V3a0hB9k=

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz#d4eaac52b8a2e7c3cd1903eb00f7e053356118ac"
  integrity sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.8.0:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.8.0.tgz#e439fee65fc33f6bba630ff621efc38ec0375c49"
  integrity sha1-5Dn+5l/DP2u6Yw/2Ie/DjsA3XEk=
  dependencies:
    debug "^3.2.7"

eslint-plugin-es@^1.3.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz#12acae0f4953e76ba444bfd1b2271081ac620998"
  integrity sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=
  dependencies:
    eslint-utils "^1.4.2"
    regexpp "^2.0.1"

eslint-plugin-import@^2.14.0:
  version "2.29.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.29.0.tgz#8133232e4329ee344f2f612885ac3073b0b7e155"
  integrity sha1-gTMjLkMp7jRPL2Eohawwc7C34VU=
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlastindex "^1.2.3"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.8.0"
    hasown "^2.0.0"
    is-core-module "^2.13.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.7"
    object.groupby "^1.0.1"
    object.values "^1.1.7"
    semver "^6.3.1"
    tsconfig-paths "^3.14.2"

eslint-plugin-node@^8.0.0:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-node/download/eslint-plugin-node-8.0.1.tgz#55ae3560022863d141fa7a11799532340a685964"
  integrity sha1-Va41YAIoY9FB+noReZUyNApoWWQ=
  dependencies:
    eslint-plugin-es "^1.3.1"
    eslint-utils "^1.3.1"
    ignore "^5.0.2"
    minimatch "^3.0.4"
    resolve "^1.8.1"
    semver "^5.5.0"

eslint-plugin-promise@^4.0.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-promise/download/eslint-plugin-promise-4.3.1.tgz#61485df2a359e03149fdafc0a68b0e030ad2ac45"
  integrity sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU=

eslint-plugin-standard@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz#0c3bf3a67e853f8bbbc580fb4945fbf16f41b7c5"
  integrity sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U=

eslint-scope@^5.0.0, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1, eslint-utils@^1.4.2, eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-1.4.3.tgz#74fec7c54d0776b6f67e0251040b5806564e981f"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz#30ebd1ef7c2fdff01c3a4f151044af25fab0523e"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^3.3.0:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^6.0.0:
  version "6.8.0"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-6.8.0.tgz#62262d6729739f9275723824302fb227c8c93ffb"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-6.2.1.tgz#77fc72e1fd744a2052c20f38a5b575832e82734a"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.5.0.tgz#6ce17738de8577694edd7361c57182ac8cb0db0b"
  integrity sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-emitter@^0.3.3:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

ext@^1.1.2:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/ext/download/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

eyes@0.1.x:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/eyes/download/eyes-0.1.8.tgz#62cf120234c683785d902348a800ef3e0cc20bc0"
  integrity sha1-Ys8SAjTGg3hdkCNIqADvPgzCC8A=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^3.2.9:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha1-qQRQHlfP3S/83tRemaVP71XkYSk=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.13.0, fastq@^1.6.0:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.15.0.tgz#d04d07c6a2a68fe4599fea8d2e103a937fae6b3a"
  integrity sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=
  dependencies:
    reusify "^1.0.4"

figures@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filelist@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz#f78978a1e944775ff9e62e744424f215e58352b5"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@3.0.0, find-up@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flat@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/flat/download/flat-4.1.1.tgz#a392059cc382881ff98642f5da4dde0a959f309b"
  integrity sha1-o5IFnMOCiB/5hkL12k3eCpWfMJs=
  dependencies:
    is-buffer "~2.0.3"

flatted@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-2.0.2.tgz#4575b21e2bcee7434aa9be662f4b7b5f9c2b5138"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flatted@^3.2.7:
  version "3.2.9"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.2.9.tgz#7eb4c67ca1ba34232ca9d2d93e9886e611ad7daf"
  integrity sha1-frTGfKG6NCMsqdLZPpiG5hGtfa8=

follow-redirects@^1.14.9:
  version "1.15.3"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.3.tgz#fe2f3ef2690afce7e82ed0b44db08165b207123a"
  integrity sha1-/i8+8mkK/OfoLtC0TbCBZbIHEjo=

for-each@^0.3.3:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.3.tgz#69b447e88a0a5d32c3e7084f3f1710034b21376e"
  integrity sha1-abRH6IoKXTLD5whPPxcQA0shN24=
  dependencies:
    is-callable "^1.1.3"

foreground-child@^1.5.6:
  version "1.5.6"
  resolved "http://r.npm.sankuai.com/foreground-child/download/foreground-child-1.5.6.tgz#4fd71ad2dfde96789b980a5c0a295937cb2f5ce9"
  integrity sha1-T9ca0t/elnibmApcCilZN8svXOk=
  dependencies:
    cross-spawn "^4"
    signal-exit "^3.0.0"

forever-agent@~0.5.0:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.5.2.tgz#6d0e09c4921f94a27f63d3b49c5feff1ea4c5130"
  integrity sha1-bQ4JxJIflKJ/Y9O0nF/v8epMUTA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^2.3.2:
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.5.1.tgz#f2cbec57b5e59e23716e128fe44d4e5dd23895f4"
  integrity sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.0.tgz#93919daeaf361ee529584b9b31664dc12c9fa452"
  integrity sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-0.2.0.tgz#26f8bc26da6440e299cbdcfb69035c4f77a6e466"
  integrity sha1-Jvi8JtpkQOKZy9z7aQNcT3em5GY=
  dependencies:
    async "~0.9.0"
    combined-stream "~0.0.4"
    mime-types "~2.0.3"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

formidable@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/formidable/download/formidable-2.1.1.tgz#81269cbea1a613240049f5f61a9d97731517414f"
  integrity sha1-gSacvqGmEyQASfX2Gp2XcxUXQU8=
  dependencies:
    dezalgo "^1.0.4"
    hexoid "^1.0.0"
    once "^1.4.0"
    qs "^6.11.0"

fresh@~0.5.2:
  version "0.5.2"
  resolved "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"
  integrity sha1-a+Dem+mYzhavivwkSXue6bfM2a0=

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fs-minipass/download/fs-minipass-2.1.0.tgz#7f5036fdbf12c63c169190cbe4199c852271f9fb"
  integrity sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

function-bind@^1.1.1, function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.6.tgz#cdf315b7d90ee77a4c6ee216c3c3362da07533fd"
  integrity sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

generate-function@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.2.tgz#281b7622971123e1ef4b3c90fd7539306da93f3b"
  integrity sha1-KBt2IpcRI+HvSzyQ/XU5MG2pPzs=
  dependencies:
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz#7fdb81c900101fbd564dd5f1a30af5aadc1e58d6"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^5.0.0, glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob@7.1.3:
  version "7.1.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  integrity sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.5, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-12.4.0.tgz#a18813576a41b00a24a97e7f815918c2e19925f8"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

globalthis@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.3.tgz#5852882a52b80dc301b0660273e1ed082f0b6ccf"
  integrity sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8=
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

growl@1.10.5:
  version "1.10.5"
  resolved "http://r.npm.sankuai.com/growl/download/growl-1.10.5.tgz#f2735dc2283674fa67478b10181059355c369e5e"
  integrity sha1-8nNdwig2dPpnR4sQGBBZNVw2nl4=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.0.2.tgz#0871bd3e3d51626f6ca0966668ba35d5602d6eaa"
  integrity sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.1.tgz#52ba30b6c5ec87fd89fa574bc1c39125c6f65340"
  integrity sha1-UrowtsXsh/2J+ldLwcORJcb2U0A=
  dependencies:
    get-intrinsic "^1.2.2"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.0.1.tgz#1885c1305538958aff469fef37937c22795408e0"
  integrity sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA=

has-symbols@^1.0.0, has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

hasha@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/hasha/download/hasha-3.0.0.tgz#52a32fab8569d41ca69a61ff1a214f8eb7c8bd39"
  integrity sha1-UqMvq4Vp1BymmmH/GiFPjrfIvTk=
  dependencies:
    is-stream "^1.0.1"

hasown@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.0.tgz#f4c513d454a57b7c7e1650778de226b11700546c"
  integrity sha1-9MUT1FSle3x+FlB3jeImsRcAVGw=
  dependencies:
    function-bind "^1.1.2"

hawk@1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/hawk/download/hawk-1.1.1.tgz#87cd491f9b46e4e2aeaca335416766885d2d1ed9"
  integrity sha1-h81JH5tG5OKurKM1QWdmiF0tHtk=
  dependencies:
    boom "0.4.x"
    cryptiles "0.2.x"
    hoek "0.9.x"
    sntp "0.2.x"

he@1.2.0, he@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hexoid@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/hexoid/download/hexoid-1.0.0.tgz#ad10c6573fb907de23d9ec63a711267d9dc9bc18"
  integrity sha1-rRDGVz+5B94j2exjpxEmfZ3JvBg=

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "http://r.npm.sankuai.com/highlight.js/download/highlight.js-10.7.3.tgz#697272e3991356e40c3cac566a74eef681756531"
  integrity sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=

hoek@0.9.x:
  version "0.9.1"
  resolved "http://r.npm.sankuai.com/hoek/download/hoek-0.9.1.tgz#3d322462badf07716ea7eb85baf88079cddce505"
  integrity sha1-PTIkYrrfB3Fup+uFuviAec3c5QU=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-minifier@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/html-minifier/download/html-minifier-4.0.0.tgz#cca9aad8bce1175e02e17a8c33e46d8988889f56"
  integrity sha1-zKmq2LzhF14C4XqMM+RtiYiIn1Y=
  dependencies:
    camel-case "^3.0.0"
    clean-css "^4.2.1"
    commander "^2.19.0"
    he "^1.2.0"
    param-case "^2.1.1"
    relateurl "^0.2.7"
    uglify-js "^3.5.1"

http-assert@^1.3.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/http-assert/download/http-assert-1.5.0.tgz#c389ccd87ac16ed2dfa6246fd73b926aa00e6b8f"
  integrity sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@^1.3.1, http-errors@^1.6.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "http://r.npm.sankuai.com/http-errors/download/http-errors-1.8.1.tgz#7c3f28577cbc8a207388455dbd62295ed07bd68c"
  integrity sha1-fD8oV3y8iiBziEVdvWIpXtB71ow=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz#e4821beef5b2142a2026bd73926fe537631c5405"
  integrity sha1-5IIb7vWyFCogJr1zkm/lN2McVAU=
  dependencies:
    agent-base "4"
    debug "3.1.0"

http-signature@~0.10.0:
  version "0.10.1"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-0.10.1.tgz#4fbdac132559aa8323121e540779c0a012b27e66"
  integrity sha1-T72sEyVZqoMjEh5UB3nAoBKyfmY=
  dependencies:
    asn1 "0.1.11"
    assert-plus "^0.1.5"
    ctype "0.5.3"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

humanize-number@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/humanize-number/download/humanize-number-0.0.2.tgz#11c0af6a471643633588588048f1799541489c18"
  integrity sha1-EcCvakcWQ2M1iFiASPF5lUFInBg=

iconv-lite@0.4.24, iconv-lite@^0.4.24, iconv-lite@^0.4.8:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3, iconv-lite@latest:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.2, ignore@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.0.tgz#67418ae40d34d6999c95ff56016759c718c82f78"
  integrity sha1-Z0GK5A001pmclf9WAWdZxxjIL3g=

image-size@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/image-size/download/image-size-1.0.2.tgz#d778b6d0ab75b2737c1556dd631652eb963bc486"
  integrity sha1-13i20Kt1snN8FVbdYxZS65Y7xIY=
  dependencies:
    queue "6.0.2"

import-fresh@^3.0.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

inflation@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/inflation/download/inflation-2.1.0.tgz#9214db11a47e6f756d111c4f9df96971c60f886c"
  integrity sha1-khTbEaR+b3VtERxPnflpccYPiGw=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.5:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@^7.0.0:
  version "7.3.3"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

int64-convert@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/int64-convert/download/int64-convert-0.1.2.tgz#0f00a5a4431268c85291895afb923e4c8d218e26"
  integrity sha1-DwClpEMSaMhSkYla+5I+TI0hjiY=

int64-transform@^0.1.13:
  version "0.1.17"
  resolved "http://r.npm.sankuai.com/int64-transform/download/int64-transform-0.1.17.tgz#1dd3e3f39a64529e413f9ae0db140c065832266b"
  integrity sha1-HdPj85pkUp5BP5rg2xQMBlgyJms=
  dependencies:
    bindings "^1.3.0"
    int64-convert "^0.1.2"
    node-addon-api "^1.4.0"

internal-slot@^1.0.5:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.0.6.tgz#37e756098c4911c5e912b8edbf71ed3aa116f930"
  integrity sha1-N+dWCYxJEcXpErjtv3HtOqEW+TA=
  dependencies:
    get-intrinsic "^1.2.2"
    hasown "^2.0.0"
    side-channel "^1.0.4"

ip@^1.1.5:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/ip/download/ip-1.1.8.tgz#ae05948f6b075435ed3307acce04629da8cdbf48"
  integrity sha1-rgWUj2sHVDXtMweszgRinajNv0g=

ipv4@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/ipv4/download/ipv4-1.0.4.tgz#8e4cfe98ac7b28e6b0d19431da2999e09b4add58"
  integrity sha1-jkz+mKx7KOaw0ZQx2imZ4JtK3Vg=
  dependencies:
    address "^1.0.3"
    chalk "^1.1.3"
    copy-paste "^1.3.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.2.tgz#f2653ced8412081638ecb0ebbd0c41c6e0aecbbe"
  integrity sha1-8mU87YQSCBY47LDrvQxBxuCuy74=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.0.4.tgz#08147a1875bc2b32005d41ccd8291dffc6691df3"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz#5c6dc200246dd9321ae4b885a114bb1f75f63719"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@~1.1.6:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@~2.0.3:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz#a527d31fb23279281dde5f385c77b5de70a72435"
  integrity sha1-pSfTH7IyeSgd3l84XHe13nCnJDU=

is-core-module@^2.13.0, is-core-module@^2.13.1:
  version "2.13.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.13.1.tgz#ad0d7532c6fea9da1ebdc82742d74525c6273384"
  integrity sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q=
  dependencies:
    hasown "^2.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-extendable@^0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz#7bf6f03a28003b8b3965de3ac26f664d765f3150"
  integrity sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.0.7.tgz#59d50ada4c45251784e9904f5246c742f07a42fc"
  integrity sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-property@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz#8f259c573b60b6a32d4058a1a07430c0a7344c79"
  integrity sha1-jyWcVztgtqMtQFihoHQwwKc0THk=
  dependencies:
    call-bind "^1.0.2"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.0.7.tgz#0dd12bf2006f255bb58f695110eff7491eebc0fd"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.0.4.tgz#a6dac93b635b063ca6872236de88910a57af139c"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-type-of@^1.2.0, is-type-of@^1.2.1:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/is-type-of/download/is-type-of-1.4.0.tgz#3ed175a0eee888b1da4983332e7714feb8a8fb2b"
  integrity sha1-PtF1oO7oiLHaSYMzLncU/rio+ys=
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-type-of@~0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/is-type-of/download/is-type-of-0.3.1.tgz#bfabd0730cd3f718e48d2140d7548ddedd36055f"
  integrity sha1-v6vQcwzT9xjkjSFA11SN3t02BV8=
  dependencies:
    core-util-is "^1.0.1"
    isstream "^0.1.0"

is-typed-array@^1.1.10, is-typed-array@^1.1.12, is-typed-array@^1.1.3, is-typed-array@^1.1.9:
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.12.tgz#d0bab5686ef4a76f7a73097b95470ab199c57d4a"
  integrity sha1-0Lq1aG70p296cwl7lUcKsZnFfUo=
  dependencies:
    which-typed-array "^1.1.11"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.0.2.tgz#9529f383a9338205e89765e0392efc2f100f06f2"
  integrity sha1-lSnzg6kzggXol2XgOS78LxAPBvI=
  dependencies:
    call-bind "^1.0.2"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isstream@0.1.x, isstream@^0.1.0, isstream@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz#675f0ab69503fad4b1d849f736baaca803344f49"
  integrity sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k=

istanbul-lib-hook@^2.0.7:
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/istanbul-lib-hook/download/istanbul-lib-hook-2.0.7.tgz#c95695f383d4f8f60df1f04252a9550e15b5b133"
  integrity sha1-yVaV84PU+PYN8fBCUqlVDhW1sTM=
  dependencies:
    append-transform "^1.0.0"

istanbul-lib-instrument@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz#a5f63d91f0bbc0c3e479ef4c5de027335ec6d630"
  integrity sha1-pfY9kfC7wMPkee9MXeAnM17G1jA=
  dependencies:
    "@babel/generator" "^7.4.0"
    "@babel/parser" "^7.4.3"
    "@babel/template" "^7.4.0"
    "@babel/traverse" "^7.4.3"
    "@babel/types" "^7.4.0"
    istanbul-lib-coverage "^2.0.5"
    semver "^6.0.0"

istanbul-lib-report@^2.0.8:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-2.0.8.tgz#5a8113cd746d43c4889eba36ab10e7d50c9b4f33"
  integrity sha1-WoETzXRtQ8SInro2qxDn1QybTzM=
  dependencies:
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    supports-color "^6.1.0"

istanbul-lib-source-maps@^3.0.6:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-3.0.6.tgz#284997c48211752ec486253da97e3879defba8c8"
  integrity sha1-KEmXxIIRdS7EhiU9qX44ed77qMg=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^2.0.5"
    make-dir "^2.1.0"
    rimraf "^2.6.3"
    source-map "^0.6.1"

istanbul-reports@^2.2.4:
  version "2.2.7"
  resolved "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-2.2.7.tgz#5d939f6237d7b48393cc0959eab40cd4fd056931"
  integrity sha1-XZOfYjfXtIOTzAlZ6rQM1P0FaTE=
  dependencies:
    html-escaper "^2.0.0"

jake@^10.8.5:
  version "10.8.7"
  resolved "http://r.npm.sankuai.com/jake/download/jake-10.8.7.tgz#63a32821177940c33f356e0ba44ff9d34e1c7d8f"
  integrity sha1-Y6MoIRd5QMM/NW4LpE/5004cfY8=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

joycon@^2.1.2:
  version "2.2.5"
  resolved "http://r.npm.sankuai.com/joycon/download/joycon-2.2.5.tgz#8d4cf4cbb2544d7b7583c216fcdfec19f6be1615"
  integrity sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU=

js-to-java@^2.3.0, js-to-java@^2.4.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/js-to-java/download/js-to-java-2.7.0.tgz#194fbd831cec3e78ff95a1c3e941ddf199ca6a24"
  integrity sha1-GU+9gxzsPnj/laHD6UHd8ZnKaiQ=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@3.13.1:
  version "3.13.1"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
  integrity sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^3.12.0, js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.0, json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

keygrip@~1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/keygrip/download/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226"
  integrity sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=
  dependencies:
    tsscmp "1.0.6"

kleur@^3.0.0:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

koa-body@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/koa-body/download/koa-body-6.0.1.tgz#46c490033cceebb2874c53cfbb04c45562cf3c84"
  integrity sha1-RsSQAzzO67KHTFPPuwTEVWLPPIQ=
  dependencies:
    "@types/co-body" "^6.1.0"
    "@types/formidable" "^2.0.5"
    "@types/koa" "^2.13.5"
    co-body "^6.1.0"
    formidable "^2.0.1"
    zod "^3.19.1"

koa-bodyparser@^4.2.1:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/koa-bodyparser/download/koa-bodyparser-4.4.1.tgz#a908d848e142cc57d9eece478e932bf00dce3029"
  integrity sha1-qQjYSOFCzFfZ7s5HjpMr8A3OMCk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"
    type-is "^1.6.18"

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/koa-compose/download/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-compose@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/koa-convert/download/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/koa-convert/download/koa-convert-2.0.0.tgz#86a0c44d81d40551bae22fee6709904573eea4f5"
  integrity sha1-hqDETYHUBVG64i/uZwmQRXPupPU=
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa-ejs@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/koa-ejs/download/koa-ejs-4.3.0.tgz#06d8459b1d52f4c41b413d8778f71e1ab2ec5009"
  integrity sha1-BthFmx1S9MQbQT2HePceGrLsUAk=
  dependencies:
    debug "^2.6.1"
    ejs "^2.6.1"
    mz "^2.6.0"

koa-logger@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/koa-logger/download/koa-logger-3.2.1.tgz#ab9db879526db3837cc9ce4fd983c025b1689f22"
  integrity sha1-q524eVJts4N8yc5P2YPAJbFonyI=
  dependencies:
    bytes "^3.1.0"
    chalk "^2.4.2"
    humanize-number "0.0.2"
    passthrough-counter "^1.0.0"

koa-proxy@^1.0.0-alpha.3:
  version "1.0.0-alpha.3"
  resolved "http://r.npm.sankuai.com/koa-proxy/download/koa-proxy-1.0.0-alpha.3.tgz#afc61edc9dc6a195464664beccc162cfe994bf55"
  integrity sha1-r8Ye3J3GoZVGRmS+zMFiz+mUv1U=
  dependencies:
    pause-stream "0.0.11"
    request "^2.88.0"
    request-promise-native "^1.0.5"

koa-router@^7.4.0:
  version "7.4.0"
  resolved "http://r.npm.sankuai.com/koa-router/download/koa-router-7.4.0.tgz#aee1f7adc02d5cb31d7d67465c9eacc825e8c5e0"
  integrity sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=
  dependencies:
    debug "^3.1.0"
    http-errors "^1.3.1"
    koa-compose "^3.0.0"
    methods "^1.0.1"
    path-to-regexp "^1.1.1"
    urijs "^1.19.0"

koa2-cors@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/koa2-cors/download/koa2-cors-2.0.6.tgz#9ad23df3a0b9bb84530b46f5944f3fb576086554"
  integrity sha1-mtI986C5u4RTC0b1lE8/tXYIZVQ=

koa@^2.7.0:
  version "2.14.2"
  resolved "http://r.npm.sankuai.com/koa/download/koa-2.14.2.tgz#a57f925c03931c2b4d94b19d2ebf76d3244863fc"
  integrity sha1-pX+SXAOTHCtNlLGdLr920yRIY/w=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.8.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

lazystream@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/lazystream/download/lazystream-1.0.1.tgz#494c831062f1f9408251ec44db1cba29242a2638"
  integrity sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=
  dependencies:
    readable-stream "^2.0.5"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/load-json-file/download/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz#d09178716ffea4dde9e5fb7b37f6f0802274580c"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.difference@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.difference/download/lodash.difference-4.5.0.tgz#9ccb4e505d486b91651345772885a2df27fd017c"
  integrity sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz#f31c22225a9632d2bbf8e4addbef240aa765a61f"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.flattendeep@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.flattendeep/download/lodash.flattendeep-4.4.0.tgz#fb030917f86a3134e5bc9bec0d69e0013ddfedb2"
  integrity sha1-+wMJF/hqMTTlvJvsDWngAT3f7bI=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz#7c526a52d89b45c45cc690b88163be0497f550cb"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.union@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.union/download/lodash.union-4.6.0.tgz#48bb5088409f16f1821666641c44dd1aaae3cd88"
  integrity sha1-SLtQiECfFvGCFmZkHETdGqrjzYg=

lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.17.5:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@2.2.0, log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log4js@^6.1.2:
  version "6.9.1"
  resolved "http://r.npm.sankuai.com/log4js/download/log4js-6.9.1.tgz#aba5a3ff4e7872ae34f8b4c533706753709e38b6"
  integrity sha1-q6Wj/054cq40+LTFM3BnU3CeOLY=
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    flatted "^3.2.7"
    rfdc "^1.3.0"
    streamroller "^3.1.5"

long@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/long/download/long-4.0.0.tgz#9a7b71cfb7d361a194ea555241c92f7468d5bf28"
  integrity sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=

long@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/long/download/long-2.1.0.tgz#e97939a21a656b7a88ba871ca34aaf32b25c0cb1"
  integrity sha1-6Xk5ohpla3qIuocco0qvMrJcDLE=

lower-case@^1.1.1:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/lower-case/download/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"
  integrity sha1-miyr0bno4K6ZOkv31YdcOcQujqw=

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz#8bbe50ea85bed59bc9e33dcab8235ee9bcf443cd"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.13.1, lru-cache@^7.14.1:
  version "7.18.3"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-7.18.3.tgz#f793896e0fd0e954a59dfdd82f0773808df6aa89"
  integrity sha1-95OJbg/Q6VSlnf3YLwdzgI32qok=

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "http://r.npm.sankuai.com/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

md5@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/md5/download/md5-2.3.0.tgz#c3da9a6aae3a30b46b7b0c349b87b110dc3bda4f"
  integrity sha1-w9qaaq46MLRreww0m4exENw72k8=
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-cache@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/memory-cache/download/memory-cache-0.2.0.tgz#7890b01d52c00c8ebc9d533e1f8eb17e3034871a"
  integrity sha1-eJCwHVLADI68nVM+H46xfjA0hxo=

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/merge-source-map/download/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  integrity sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=
  dependencies:
    source-map "^0.6.1"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.0.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.4:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-db@~1.12.0:
  version "1.12.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.12.0.tgz#3d0c63180f458eb10d325aaa37d7c58ae312e9d7"
  integrity sha1-PQxjGA9FjrENMlqqN9fFiuMS6dc=

mime-types@^2.1.12, mime-types@^2.1.18, mime-types@~2.1.19, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime-types@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-1.0.2.tgz#995ae1392ab8affcbfcb2641dd054e943c0d5dce"
  integrity sha1-mVrhOSq4r/y/yyZB3QVOlDwNXc4=

mime-types@~2.0.3:
  version "2.0.14"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.0.14.tgz#310e159db23e077f8bb22b748dabfa4957140aa6"
  integrity sha1-MQ4VnbI+B3+Lsit0jav6SVcUCqY=
  dependencies:
    mime-db "~1.12.0"

mime2@latest:
  version "0.0.11"
  resolved "http://r.npm.sankuai.com/mime2/download/mime2-0.0.11.tgz#ae75a1386114ffd51d9e97c481464318e97d87c5"
  integrity sha1-rnWhOGEU/9UdnpfEgUZDGOl9h8U=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.0.3, minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
  integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

minimost@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/minimost/download/minimost-1.2.0.tgz#a37f91d60395fc003180d208ca9e0316bcc4e3a2"
  integrity sha1-o3+R1gOV/AAxgNIIyp4DFrzE46I=
  dependencies:
    "@types/minimist" "^1.2.0"
    minimist "^1.2.0"

minipass@^3.0.0:
  version "3.3.6"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-3.3.6.tgz#7bba384db3a1520d18c9c0e5251c3444e95dd94a"
  integrity sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-5.0.0.tgz#3e9788ffb90b694a5d0ec94479a45b5d8738133d"
  integrity sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=

minizlib@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/minizlib/download/minizlib-2.1.2.tgz#e90d3466ba209b932451508a11ce3d3632145931"
  integrity sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
  dependencies:
    minimist "0.0.8"

mkdirp@^0.5.0, mkdirp@^0.5.1:
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.3, mkdirp@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mkdirp@~0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.3.5.tgz#de3e5f8961c88c787ee1368df849ac4413eca8d7"
  integrity sha1-3j5fiWHIjHh+4TaN+EmsRBPsqNc=

mocha@6.2.2:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/mocha/download/mocha-6.2.2.tgz#5d8987e28940caf8957a7d7664b910dc5b2fea20"
  integrity sha1-XYmH4olAyviVen12ZLkQ3Fsv6iA=
  dependencies:
    ansi-colors "3.2.3"
    browser-stdout "1.3.1"
    debug "3.2.6"
    diff "3.5.0"
    escape-string-regexp "1.0.5"
    find-up "3.0.0"
    glob "7.1.3"
    growl "1.10.5"
    he "1.2.0"
    js-yaml "3.13.1"
    log-symbols "2.2.0"
    minimatch "3.0.4"
    mkdirp "0.5.1"
    ms "2.1.1"
    node-environment-flags "1.0.5"
    object.assign "4.1.0"
    strip-json-comments "2.0.1"
    supports-color "6.0.0"
    which "1.3.1"
    wide-align "1.1.3"
    yargs "13.3.0"
    yargs-parser "13.1.1"
    yargs-unparser "1.6.0"

moment@^2.10.6, moment@^2.18.1, moment@^2.19.4, moment@^2.24.0, moment@^2.29.4:
  version "2.29.4"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.1:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mysql2@^2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/mysql2/download/mysql2-2.3.3.tgz#944f3deca4b16629052ff8614fbf89d5552545a0"
  integrity sha1-lE897KSxZikFL/hhT7+J1VUlRaA=
  dependencies:
    denque "^2.0.1"
    generate-function "^2.3.1"
    iconv-lite "^0.6.3"
    long "^4.0.0"
    lru-cache "^6.0.0"
    named-placeholders "^1.1.2"
    seq-queue "^0.0.5"
    sqlstring "^2.3.2"

mysql@^2.18.1:
  version "2.18.1"
  resolved "http://r.npm.sankuai.com/mysql/download/mysql-2.18.1.tgz#2254143855c5a8c73825e4522baf2ea021766717"
  integrity sha1-IlQUOFXFqMc4JeRSK68uoCF2Zxc=
  dependencies:
    bignumber.js "9.0.0"
    readable-stream "2.3.7"
    safe-buffer "5.1.2"
    sqlstring "2.3.1"

mz@^2.4.0, mz@^2.6.0, mz@^2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

named-placeholders@^1.1.2:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/named-placeholders/download/named-placeholders-1.1.3.tgz#df595799a36654da55dda6152ba7a137ad1d9351"
  integrity sha1-31lXmaNmVNpV3aYVK6ehN60dk1E=
  dependencies:
    lru-cache "^7.14.1"

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

nested-error-stacks@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nested-error-stacks/download/nested-error-stacks-2.1.1.tgz#26c8a3cee6cc05fbcf1e333cd2fc3e003326c0b5"
  integrity sha1-JsijzubMBfvPHjM80vw+ADMmwLU=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/next-tick/download/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^2.2.0:
  version "2.3.2"
  resolved "http://r.npm.sankuai.com/no-case/download/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  integrity sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=
  dependencies:
    lower-case "^1.1.1"

node-addon-api@^1.4.0:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-1.7.2.tgz#3df30b95720b53c24e59948b49532b662444f54d"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-addon-api@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-2.0.2.tgz#432cfa82962ce494b132e9d72a15b29f71ff5d32"
  integrity sha1-Qyz6gpYs5JSxMunXKhWyn3H/XTI=

node-addon-api@^3.1.0:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-3.2.1.tgz#81325e0a2117789c0128dab65e7e38f07ceba161"
  integrity sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=

node-environment-flags@1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/node-environment-flags/download/node-environment-flags-1.0.5.tgz#fa930275f5bf5dae188d6192b24b4c8bbac3d76a"
  integrity sha1-+pMCdfW/Xa4YjWGSsktMi7rD12o=
  dependencies:
    object.getownpropertydescriptors "^2.0.3"
    semver "^5.7.0"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-stream-zip@^1.7.0:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz#158adb88ed8004c6c49a396b50a6a5de3bca33ea"
  integrity sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o=

node-uuid@~1.4.0:
  version "1.4.8"
  resolved "http://r.npm.sankuai.com/node-uuid/download/node-uuid-1.4.8.tgz#b040eb0923968afabf8d32fb1f17f1167fdab907"
  integrity sha1-sEDrCSOWivq/jTL7HxfxFn/auQc=

node-zookeeper-client@^0.2.2:
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/node-zookeeper-client/download/node-zookeeper-client-0.2.3.tgz#48c79129c56b8e898df9bd3bdad9e27dcad63851"
  integrity sha1-SMeRKcVrjomN+b072tnifcrWOFE=
  dependencies:
    async "~0.2.7"
    underscore "~1.4.4"

noms@0.0.0:
  version "0.0.0"
  resolved "http://r.npm.sankuai.com/noms/download/noms-0.0.0.tgz#da8ebd9f3af9d6760919b27d9cdc8092a7332859"
  integrity sha1-2o69nzr51nYJGbJ9nNyAkqczKFk=
  dependencies:
    inherits "^2.0.1"
    readable-stream "~1.0.31"

normalize-package-data@^2.3.2:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

nyc@14.1.1:
  version "14.1.1"
  resolved "http://r.npm.sankuai.com/nyc/download/nyc-14.1.1.tgz#151d64a6a9f9f5908a1b73233931e4a0a3075eeb"
  integrity sha1-FR1kpqn59ZCKG3MjOTHkoKMHXus=
  dependencies:
    archy "^1.0.0"
    caching-transform "^3.0.2"
    convert-source-map "^1.6.0"
    cp-file "^6.2.0"
    find-cache-dir "^2.1.0"
    find-up "^3.0.0"
    foreground-child "^1.5.6"
    glob "^7.1.3"
    istanbul-lib-coverage "^2.0.5"
    istanbul-lib-hook "^2.0.7"
    istanbul-lib-instrument "^3.3.0"
    istanbul-lib-report "^2.0.8"
    istanbul-lib-source-maps "^3.0.6"
    istanbul-reports "^2.2.4"
    js-yaml "^3.13.1"
    make-dir "^2.1.0"
    merge-source-map "^1.1.0"
    resolve-from "^4.0.0"
    rimraf "^2.6.3"
    signal-exit "^3.0.2"
    spawn-wrap "^1.4.2"
    test-exclude "^5.2.3"
    uuid "^3.3.2"
    yargs "^13.2.2"
    yargs-parser "^13.0.0"

oauth-sign@~0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.5.0.tgz#d767f5169325620eab2e087ef0c472e773db6461"
  integrity sha1-12f1FpMlYg6rLgh+8MRy53PbZGE=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^1.1.2:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/object-hash/download/object-hash-1.3.1.tgz#fde452098a951cb145f039bb7d455449ddc126df"
  integrity sha1-/eRSCYqVHLFF8Dm7fUVUSd3BJt8=

object-inspect@^1.13.1, object-inspect@^1.9.0:
  version "1.13.1"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.1.tgz#b96c6109324ccfef6b12216a956ca4dc2ff94bc2"
  integrity sha1-uWxhCTJMz+9rEiFqlWyk3C/5S8I=

object-keys@^1.0.11, object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  integrity sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.assign@^4.1.4:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.4.tgz#9673c7c7c351ab8c4d0b516f4343ebf4dfb7799f"
  integrity sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.fromentries@^2.0.7:
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.7.tgz#71e95f441e9a0ea6baf682ecaaf37fa2a8d7e616"
  integrity sha1-celfRB6aDqa69oLsqvN/oqjX5hY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.getownpropertydescriptors@^2.0.3:
  version "2.1.7"
  resolved "http://r.npm.sankuai.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.7.tgz#7a466a356cd7da4ba8b9e94ff6d35c3eeab5d56a"
  integrity sha1-ekZqNWzX2kuouelP9tNcPuq11Wo=
  dependencies:
    array.prototype.reduce "^1.0.6"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    safe-array-concat "^1.0.0"

object.groupby@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/object.groupby/download/object.groupby-1.0.1.tgz#d41d9f3c8d6c778d9cbac86b4ee9f5af103152ee"
  integrity sha1-1B2fPI1sd42cushrTun1rxAxUu4=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"

object.values@^1.1.7:
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.1.7.tgz#617ed13272e7e1071b43973aa1655d9291b8442a"
  integrity sha1-YX7RMnLn4QcbQ5c6oWVdkpG4RCo=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

on-finished@^2.3.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

only@~0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

optionator@^0.8.3:
  version "0.8.3"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^3.0.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-limit@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

package-hash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/package-hash/download/package-hash-3.0.0.tgz#50183f2d36c9e3e528ea0a8605dff57ce976f88e"
  integrity sha1-UBg/LTbJ4+Uo6gqGBd/1fOl2+I4=
  dependencies:
    graceful-fs "^4.1.15"
    hasha "^3.0.0"
    lodash.flattendeep "^4.4.0"
    release-zalgo "^1.0.0"

param-case@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/param-case/download/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  integrity sha1-35T9jPZTHs915r75oIWPvHK+Ikc=
  dependencies:
    no-case "^2.2.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz#2cdf9ad823321140370d4dbf5d3e92c7c8ddc6e6"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-5.1.1.tgz#f68e4e5ba1852ac2cadc00f4555fff6c2abb6178"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parse5@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz#e1a1c085c569b3dc08321184f19a39cc27f7c30b"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@^1.3.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

passthrough-counter@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/passthrough-counter/download/passthrough-counter-1.0.0.tgz#1967d9e66da572b5c023c787db112a387ab166fa"
  integrity sha1-GWfZ5m2lcrXAI8eH2xEqOHqxZvo=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@^1.1.1:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz#887b3ba9d84393e87a0a0b9f4cb756198b53548a"
  integrity sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=
  dependencies:
    isarray "0.0.1"

path-to-regexp@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-3.2.0.tgz#fa7877ecbc495c601907562222453c43cc204a5f"
  integrity sha1-+nh37LxJXGAZB1YiIkU8Q8wgSl8=

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause-stream@0.0.11:
  version "0.0.11"
  resolved "http://r.npm.sankuai.com/pause-stream/download/pause-stream-0.0.11.tgz#fe5a34b0cbce12b5aa6a2b403ee2e73b602f1445"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkginfo@0.3.x:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/pkginfo/download/pkginfo-0.3.1.tgz#5b29f6a81f70717142e09e765bbeab97b4f81e21"
  integrity sha1-Wyn2qB9wcXFC4J52W76rl7T4HiE=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

prompts@^1.2.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prompts/download/prompts-1.2.1.tgz#7fd4116a458d6a62761e3ccb1432d7bbd8b2cb29"
  integrity sha1-f9QRakWNamJ2HjzLFDLXu9iyyyk=
  dependencies:
    kleur "^3.0.0"
    sisteransi "^1.0.0"

properties-parser@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz#1316e9539ffbfd93845e369b211022abd478771a"
  integrity sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=
  dependencies:
    string.prototype.codepointat "^0.2.0"

properties-reader@0.0.15:
  version "0.0.15"
  resolved "http://r.npm.sankuai.com/properties-reader/download/properties-reader-0.0.15.tgz#260a20a122a32aa7fe166bd237a0abeecff53086"
  integrity sha1-JgogoSKjKqf+FmvSN6Cr7s/1MIY=
  dependencies:
    mkdirp "~0.3.5"

properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/properties/download/properties-1.2.1.tgz#0ee97a7fc020b1a2a55b8659eda4aa8d869094bd"
  integrity sha1-Dul6f8AgsaKlW4ZZ7aSqjYaQlL0=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28, psl@^1.1.33:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.9.0.tgz#d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7"
  integrity sha1-0N8qE38AeUVl/K87LADNCfjVpac=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

q@^1.5.0:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@^2.4.1:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/qs/download/qs-2.4.2.tgz#f7ce788e5777df0b5010da7f7c4e73ba32470f5a"
  integrity sha1-9854jld33wtQENp/fE5zujJHD1o=

qs@^6.11.0, qs@^6.5.2:
  version "6.11.2"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.11.2.tgz#64bea51f12c1f5da1bc01496f48ffcff7c69d7d9"
  integrity sha1-ZL6lHxLB9dobwBSW9I/8/3xp19k=
  dependencies:
    side-channel "^1.0.4"

qs@~2.3.1:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/qs/download/qs-2.3.3.tgz#e9e85adbe75da0bbe4c8e0476a086290f863b404"
  integrity sha1-6eha2+ddoLvkyOBHaghikPhjtAQ=

qs@~6.5.2:
  version "6.5.3"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

querystring@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/querystring/download/querystring-0.2.1.tgz#40d77615bb09d16902a85c3e38aa8b5ed761c2dd"
  integrity sha1-QNd2FbsJ0WkCqFw+OKqLXtdhwt0=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

queue@6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/queue/download/queue-6.0.2.tgz#b91525283e2315c7553d2efa18d83e76432fed65"
  integrity sha1-uRUlKD4jFcdVPS76GNg+dkMv7WU=
  dependencies:
    inherits "~2.0.3"

raw-body@^2.3.3:
  version "2.5.2"
  resolved "http://r.npm.sankuai.com/raw-body/download/raw-body-2.5.2.tgz#99febd83b90e08975087e8f1f9419a149366b68a"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-4.0.0.tgz#1b221c6088ba7799601c808f91161c66e58f8978"
  integrity sha1-GyIcYIi6d5lgHICPkRYcZuWPiXg=
  dependencies:
    find-up "^3.0.0"
    read-pkg "^3.0.0"

read-pkg@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/read-pkg/download/read-pkg-3.0.0.tgz#9cbc686978fee65d16c00e2b19c237fcf6e38389"
  integrity sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=
  dependencies:
    load-json-file "^4.0.0"
    normalize-package-data "^2.3.2"
    path-type "^3.0.0"

readable-stream@2.3.7:
  version "2.3.7"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.0.0, readable-stream@^2.0.5, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.1.1, readable-stream@^3.4.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.26, readable-stream@~1.0.31:
  version "1.0.34"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

redent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/redent/download/redent-2.0.0.tgz#c1b2007b42d57eb1389079b3c8333639d5e1ccaa"
  integrity sha1-wbIAe0LVfrE4kHmzyDM2OdXhzKo=
  dependencies:
    indent-string "^3.0.0"
    strip-indent "^2.0.0"

redis-commands@^1.2.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/redis-commands/download/redis-commands-1.7.0.tgz#15a6fea2d58281e27b1cd1acfb4b293e278c3a89"
  integrity sha1-Fab+otWCgeJ7HNGs+0spPieMOok=

redis-parser@^2.6.0:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/redis-parser/download/redis-parser-2.6.0.tgz#52ed09dacac108f1a631c07e9b69941e7a19504b"
  integrity sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs=

redis@^2.7.1:
  version "2.8.0"
  resolved "http://r.npm.sankuai.com/redis/download/redis-2.8.0.tgz#202288e3f58c49f6079d97af7a10e1303ae14b02"
  integrity sha1-ICKI4/WMSfYHnZevehDhMDrhSwI=
  dependencies:
    double-ended-queue "^2.1.0-0"
    redis-commands "^1.2.0"
    redis-parser "^2.6.0"

reflect-metadata@^0.1.13:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/reflect-metadata/download/reflect-metadata-0.1.13.tgz#67ae3ca57c972a2aa1642b10fe363fe32d49dc08"
  integrity sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.0.tgz#5e19d68eb12d486f797e15a3c6a918f7cec5eb45"
  integrity sha1-XhnWjrEtSG95fhWjxqkY987F60U=

regexp.prototype.flags@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.1.tgz#90ce989138db209f81492edd734183ce99f9677e"
  integrity sha1-kM6YkTjbIJ+BSS7dc0GDzpn5Z34=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    set-function-name "^2.0.0"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

relateurl@^0.2.7:
  version "0.2.7"
  resolved "http://r.npm.sankuai.com/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

release-zalgo@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/release-zalgo/download/release-zalgo-1.0.0.tgz#09700b7e5074329739330e535c5a90fb67851730"
  integrity sha1-CXALflB0Mpc5Mw5TXFqQ+2eFFzA=
  dependencies:
    es6-error "^4.0.1"

request-promise-core@1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/request-promise-core/download/request-promise-core-1.1.4.tgz#3eedd4223208d419867b78ce815167d10593a22f"
  integrity sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=
  dependencies:
    lodash "^4.17.19"

request-promise-native@^1.0.5:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/request-promise-native/download/request-promise-native-1.0.9.tgz#e407120526a5efdc9a39b28a5679bf47b9d9dc28"
  integrity sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=
  dependencies:
    request-promise-core "1.1.4"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@*, request@^2.67.0, request@^2.69.0, request@^2.88.0, request@^2.88.2:
  version "2.88.2"
  resolved "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

request@~2.51.0:
  version "2.51.0"
  resolved "http://r.npm.sankuai.com/request/download/request-2.51.0.tgz#35d00bbecc012e55f907b1bd9e0dbd577bfef26e"
  integrity sha1-NdALvswBLlX5B7G9ng29V3v+8m4=
  dependencies:
    aws-sign2 "~0.5.0"
    bl "~0.9.0"
    caseless "~0.8.0"
    combined-stream "~0.0.5"
    forever-agent "~0.5.0"
    form-data "~0.2.0"
    hawk "1.1.1"
    http-signature "~0.10.0"
    json-stringify-safe "~5.0.0"
    mime-types "~1.0.1"
    node-uuid "~1.4.0"
    oauth-sign "~0.5.0"
    qs "~2.3.1"
    stringstream "~0.0.4"
    tough-cookie ">=0.12.0"
    tunnel-agent "~0.4.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve@^1.10.0, resolve@^1.22.4, resolve@^1.8.1:
  version "1.22.8"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.10.1:
  version "0.10.1"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"
  integrity sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=

retry@^0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.6.1.tgz#fdc90eed943fde11b893554b8cc63d0e899ba918"
  integrity sha1-/ckO7ZQ/3hG4k1VLjMY9DombqRg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.3.0.tgz#d0b7c441ab2720d05dc4cf26e01c89631d9da08b"
  integrity sha1-0LfEQasnINBdxM8m4ByJYx2doIs=

rimraf@2.6.3:
  version "2.6.3"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^2.6.2, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^6.6.0:
  version "6.6.7"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-array-concat@^1.0.0, safe-array-concat@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.0.1.tgz#91686a63ce3adbea14d61b14c99572a8ff84754c"
  integrity sha1-kWhqY8462+oU1hsUyZVyqP+EdUw=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.0.0.tgz#793b874d524eb3640d1873aad03596db2d4f2295"
  integrity sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sax@>=0.6.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.3.0.tgz#a5dbe77db3be05c9d1ee7785dbd3ea9de51593d0"
  integrity sha1-pdvnfbO+BcnR7neF29PqneUVk9A=

sdk-base@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/sdk-base/download/sdk-base-3.6.0.tgz#7cee1fa3a81fdc61b2c20d1130c8765f8d90e588"
  integrity sha1-fO4fo6gf3GGywg0RMMh2X42Q5Yg=
  dependencies:
    await-event "^2.1.0"
    await-first "^1.0.0"
    co "^4.6.0"
    is-type-of "^1.2.1"

"semver@2 || 3 || 4 || 5", semver@^5.5.0, semver@^5.6.0, semver@^5.7.0:
  version "5.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.0.0, semver@^6.1.2, semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.7:
  version "7.5.4"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.5.4.tgz#483986ec4ed38e1c6c48c34894a9182dbff68a6e"
  integrity sha1-SDmG7E7TjhxsSMNIlKkYLb/2im4=
  dependencies:
    lru-cache "^6.0.0"

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "http://r.npm.sankuai.com/seq-queue/download/seq-queue-0.0.5.tgz#d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e"
  integrity sha1-1WgS4cAXpuTnw+Ojeh2m143TyT4=

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.1.1.tgz#4bc39fafb0307224a33e106a7d35ca1218d659ed"
  integrity sha1-S8Ofr7AwciSjPhBqfTXKEhjWWe0=
  dependencies:
    define-data-property "^1.1.1"
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

set-function-name@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.1.tgz#12ce38b7954310b9f61faa12701620a0c882793a"
  integrity sha1-Es44t5VDELn2H6oScBYgoMiCeTo=
  dependencies:
    define-data-property "^1.0.1"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

sha.js@^2.4.11:
  version "2.4.11"
  resolved "http://r.npm.sankuai.com/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sisteransi@^1.0.0:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/sisteransi/download/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snappyjs@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/snappyjs/download/snappyjs-0.5.0.tgz#2600e75a50f0799c79b055c3df1b7f7008045838"
  integrity sha1-JgDnWlDweZx5sFXD3xt/cAgEWDg=

snappyjs@^0.6.0:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/snappyjs/download/snappyjs-0.6.1.tgz#9bca9ff8c54b133a9cc84a71d22779e97fc51878"
  integrity sha1-m8qf+MVLEzqcyEpx0id56X/FGHg=

sntp@0.2.x:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/sntp/download/sntp-0.2.4.tgz#fb885f18b0f3aad189f824862536bceeec750900"
  integrity sha1-+4hfGLDzqtGJ+CSGJTa87ux1CQA=
  dependencies:
    hoek "0.9.x"

source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spawn-wrap@^1.4.2:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/spawn-wrap/download/spawn-wrap-1.4.3.tgz#81b7670e170cca247d80bf5faf0cfb713bdcf848"
  integrity sha1-gbdnDhcMyiR9gL9frwz7cTvc+Eg=
  dependencies:
    foreground-child "^1.5.6"
    mkdirp "^0.5.0"
    os-homedir "^1.0.1"
    rimraf "^2.6.2"
    signal-exit "^3.0.2"
    which "^1.3.0"

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.16"
  resolved "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.16.tgz#a14f64e0954f6e25cc6587bd4f392522db0d998f"
  integrity sha1-oU9k4JVPbiXMZYe9TzklItsNmY8=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sqlstring@2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/sqlstring/download/sqlstring-2.3.1.tgz#475393ff9e91479aea62dcaf0ca3d14983a7fb40"
  integrity sha1-R1OT/56RR5rqYtyvDKPRSYOn+0A=

sqlstring@^2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/sqlstring/download/sqlstring-2.3.3.tgz#2ddc21f03bce2c387ed60680e739922c65751d0c"
  integrity sha1-Ldwh8DvOLDh+1gaA5zmSLGV1HQw=

sshpk@^1.7.0:
  version "1.17.0"
  resolved "http://r.npm.sankuai.com/sshpk/download/sshpk-1.17.0.tgz#578082d92d4fe612b13007496e543fa0fbcbe4c5"
  integrity sha1-V4CC2S1P5hKxMAdJblQ/oPvL5MU=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-trace@0.0.x:
  version "0.0.10"
  resolved "http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz#547c70b347e8d32b4e108ea1a2a159e5fdde19c0"
  integrity sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=

statuses@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.5.0 < 2", statuses@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/stealthy-require/download/stealthy-require-1.1.1.tgz#35b09875b4ff49f26a777e509b3090a3226bf24b"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

streamroller@^3.1.5:
  version "3.1.5"
  resolved "http://r.npm.sankuai.com/streamroller/download/streamroller-3.1.5.tgz#1263182329a45def1ffaef58d31b15d13d2ee7ff"
  integrity sha1-EmMYIymkXe8f+u9Y0xsV0T0u5/8=
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    fs-extra "^8.1.0"

string-template@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/string-template/download/string-template-1.0.0.tgz#9e9f2233dc00f218718ec379a28a5673ecca8b96"
  integrity sha1-np8iM9wA8hhxjsN5oopWc+zKi5Y=

"string-width@^1.0.2 || 2", string-width@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0, string-width@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.codepointat@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz#004ad44c8afc727527b108cd462b4d971cd469bc"
  integrity sha1-AErUTIr8cnUnsQjNRitNlxzUabw=

string.prototype.trim@^1.2.8:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.8.tgz#f9ac6f8af4bd55ddfa8895e6aea92a96395393bd"
  integrity sha1-+axvivS9Vd36iJXmrqkqljlTk70=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimend@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.7.tgz#1bb3afc5008661d73e2dc015cd4853732d6c471e"
  integrity sha1-G7OvxQCGYdc+LcAVzUhTcy1sRx4=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimstart@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.7.tgz#d4cdb44b83a4737ffbac2d406e405d43d0184298"
  integrity sha1-1M20S4Okc3/7rC1AbkBdQ9AYQpg=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringstream@~0.0.4:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/stringstream/download/stringstream-0.0.6.tgz#7880225b0d4ad10e30927d167a1d6f2fd3b33a72"
  integrity sha1-eIAiWw1K0Q4wkn0Weh1vL9OzOnI=

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/strip-indent/download/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"
  integrity sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=

strip-json-comments@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

supports-color@6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-6.0.0.tgz#76cfe742cf1f41bb9b1c29ad03068c05b4c0e40a"
  integrity sha1-ds/nQs8fQbubHCmtAwaMBbTA5Ao=
  dependencies:
    has-flag "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

table@^5.2.3:
  version "5.4.6"
  resolved "http://r.npm.sankuai.com/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tar-stream@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz#acad84c284136b060dc3faa64474aa9aebd77287"
  integrity sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

tar@^6.0.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/tar/download/tar-6.2.0.tgz#b14ce49a79cb1cd23bc9b016302dea5474493f73"
  integrity sha1-sUzkmnnLHNI7ybAWMC3qVHRJP3M=
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

test-exclude@^5.2.3:
  version "5.2.3"
  resolved "http://r.npm.sankuai.com/test-exclude/download/test-exclude-5.2.3.tgz#c3d3e1e311eb7ee405e092dac10aefd09091eac0"
  integrity sha1-w9Ph4xHrfuQF4JLawQrv0JCR6sA=
  dependencies:
    glob "^7.1.3"
    minimatch "^3.0.4"
    read-pkg-up "^4.0.0"
    require-main-filename "^2.0.0"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thrift@0.11.0, thrift@^0.11.0:
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/thrift/download/thrift-0.11.0.tgz#256115e4ff87871e12537f4b510bd2b425e13990"
  integrity sha1-JWEV5P+Hhx4SU39LUQvStCXhOZA=
  dependencies:
    node-int64 "^0.4.0"
    q "^1.5.0"
    ws ">= 2.2.3"

through2@^2.0.1:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@^2.3.6, through@~2.3:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

tough-cookie@>=0.12.0:
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.3.tgz#97b9adb0728b42280aa3d814b6b999b2ff0318bf"
  integrity sha1-l7mtsHKLQigKo9gUtrmZsv8DGL8=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tough-cookie@^2.3.3, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

ts-node@^10.4.0:
  version "10.9.2"
  resolved "http://r.npm.sankuai.com/ts-node/download/ts-node-10.9.2.tgz#70f021c9e185bccdca820e26dc413805c101c71f"
  integrity sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths@^3.14.2:
  version "3.14.2"
  resolved "http://r.npm.sankuai.com/tsconfig-paths/download/tsconfig-paths-3.14.2.tgz#6e32f1f79412decd261f92d633a9dc1cfa99f088"
  integrity sha1-bjLx95QS3s0mH5LWM6ncHPqZ8Ig=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.1.0, tslib@^2.4.0:
  version "2.6.2"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.6.2.tgz#703ac29425e7b37cd6fd456e92404d46d1f3e4ae"
  integrity sha1-cDrClCXns3zW/UVukkBNRtHz5K4=

tsscmp@1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/tsscmp/download/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb"
  integrity sha1-hbmVg6w1iexL/vgltQAKqRHWBes=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tunnel-agent@~0.4.0:
  version "0.4.3"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.4.3.tgz#6373db76909fe570e08d73583365ed828a74eeeb"
  integrity sha1-Y3PbdpCf5XDgjXNYM2Xtgop07us=

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.6.16, type-is@^1.6.18:
  version "1.6.18"
  resolved "http://r.npm.sankuai.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

type@^1.0.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/type/download/type-1.2.0.tgz#848dd7698dafa3e54a6c479e759c4bc3f18847a0"
  integrity sha1-hI3XaY2vo+VKbEeedZxLw/GIR6A=

type@^2.7.2:
  version "2.7.2"
  resolved "http://r.npm.sankuai.com/type/download/type-2.7.2.tgz#2376a15a3a28b1efa0f5350dcf72d24df6ef98d0"
  integrity sha1-I3ahWjoose+g9TUNz3LSTfbvmNA=

typed-array-buffer@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.0.tgz#18de3e7ed7974b0a729d3feecb94338d1472cd60"
  integrity sha1-GN4+fteXSwpynT/uy5QzjRRyzWA=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    is-typed-array "^1.1.10"

typed-array-byte-length@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.0.tgz#d787a24a995711611fb2b87a4052799517b230d0"
  integrity sha1-14eiSplXEWEfsrh6QFJ5lReyMNA=
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.0.tgz#cbbe89b51fdef9cd6aaf07ad4707340abbc4ea0b"
  integrity sha1-y76JtR/e+c1qrwetRwc0CrvE6gs=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.4.tgz#89d83785e5c4098bec72e08b319651f0eac9c1bb"
  integrity sha1-idg3heXECYvscuCLMZZR8OrJwbs=
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typeorm@^0.2.22, typeorm@^0.2.45:
  version "0.2.45"
  resolved "http://r.npm.sankuai.com/typeorm/download/typeorm-0.2.45.tgz#e5bbb3af822dc4646bad96cfa48cd22fa4687cea"
  integrity sha1-5buzr4ItxGRrrZbPpIzSL6RofOo=
  dependencies:
    "@sqltools/formatter" "^1.2.2"
    app-root-path "^3.0.0"
    buffer "^6.0.3"
    chalk "^4.1.0"
    cli-highlight "^2.1.11"
    debug "^4.3.1"
    dotenv "^8.2.0"
    glob "^7.1.6"
    js-yaml "^4.0.0"
    mkdirp "^1.0.4"
    reflect-metadata "^0.1.13"
    sha.js "^2.4.11"
    tslib "^2.1.0"
    uuid "^8.3.2"
    xml2js "^0.4.23"
    yargs "^17.0.1"
    zen-observable-ts "^1.0.0"

typescript@^4.4.4:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

uglify-js@^3.5.1:
  version "3.17.4"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-3.17.4.tgz#61678cf5fa3f5b7eb789bb345df29afb8257c22c"
  integrity sha1-YWeM9fo/W363ibs0XfKa+4JXwiw=

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz#29032021057d5e6cdbd08c5129c226dff8ed6f9e"
  integrity sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

underscore@~1.4.4:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/underscore/download/underscore-1.4.4.tgz#61a6a32010622afa07963bf325203cf12239d604"
  integrity sha1-YaajIBBiKvoHljvzJSA88SI51gQ=

undici-types@~5.26.4:
  version "5.26.5"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-5.26.5.tgz#bcd539893d00b56e964fd2657a4866b221a65617"
  integrity sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=

unescape@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/unescape/download/unescape-1.0.1.tgz#956e430f61cad8a4d57d82c518f5e6cc5d0dda96"
  integrity sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY=
  dependencies:
    extend-shallow "^2.0.1"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz#6451760566fa857534745ab1dde952d1b1761be0"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

untildify@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/untildify/download/untildify-4.0.0.tgz#2bc947b953652487e4600949fb091e3ae8cd919b"
  integrity sha1-K8lHuVNlJIfkYAlJ+wkeOujNkZs=

upper-case@^1.1.1:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"
  integrity sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.0:
  version "1.19.11"
  resolved "http://r.npm.sankuai.com/urijs/download/urijs-1.19.11.tgz#204b0d6b605ae80bea54bea39280cdb7c9f923cc"
  integrity sha1-IEsNa2Ba6AvqVL6jkoDNt8n5I8w=

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.1:
  version "0.12.5"
  resolved "http://r.npm.sankuai.com/util/download/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utility@^1.13.1:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/utility/download/utility-1.18.0.tgz#af55f62e6d5a272e0cb02b0ab3e7f37c46435f36"
  integrity sha1-r1X2Lm1aJy4MsCsKs+fzfEZDXzY=
  dependencies:
    copy-to "^2.0.1"
    escape-html "^1.0.3"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    unescape "^1.0.1"

utility@~1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/utility/download/utility-1.1.0.tgz#c3d902d88a54658dce43aeaa162526e070b6430a"
  integrity sha1-w9kC2IpUZY3OQ66qFiUm4HC2Qwo=

uuid@^3.0.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.2:
  version "8.3.2"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz#80d5b5ced271bb9af6c445f21a1a04c606cefbe2"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz#6336e8d71965cb3d35a1bbb7868445a7c05264bf"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-compile-cache@^2.0.3:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.4.0.tgz#cdada8bec61e15865f05d097c5f4fd30e94dc128"
  integrity sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz#13757bc89b209b049fe5d86430e21cf40a89a8e6"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/which-module/download/which-module-2.0.1.tgz#776b1fe35d90aebe99e8ac15eb24093389a4a409"
  integrity sha1-d2sf412Qrr6Z6KwV6yQJM4mkpAk=

which-typed-array@^1.1.11, which-typed-array@^1.1.13, which-typed-array@^1.1.2:
  version "1.1.13"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.13.tgz#870cd5be06ddb616f504e7b039c4c24898184d36"
  integrity sha1-hwzVvgbdthb1BOewOcTCSJgYTTY=
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.4"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

which@1.3.1, which@^1.2.9, which@^1.3.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wide-align@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/wide-align/download/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
  dependencies:
    string-width "^1.0.2 || 2"

winston@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/winston/download/winston-1.0.0.tgz#30e36e0041fc0a864b0029565719e4dc41d026a4"
  integrity sha1-MONuAEH8CoZLAClWVxnk3EHQJqQ=
  dependencies:
    async "0.9.x"
    colors "1.0.x"
    cycle "1.0.x"
    eyes "0.1.x"
    isstream "0.1.x"
    pkginfo "0.3.x"
    stack-trace "0.0.x"

winston@2.4.0:
  version "2.4.0"
  resolved "http://r.npm.sankuai.com/winston/download/winston-2.4.0.tgz#808050b93d52661ed9fb6c26b3f0c826708b0aee"
  integrity sha1-gIBQuT1SZh7Z+2wms/DIJnCLCu4=
  dependencies:
    async "~1.0.0"
    colors "1.0.x"
    cycle "1.0.x"
    eyes "0.1.x"
    isstream "0.1.x"
    stack-trace "0.0.x"

word-wrap@~1.2.3:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrap-ansi@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz#1fd1f67235d5b6d0fee781056001bfb694c03b09"
  integrity sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=
  dependencies:
    ansi-styles "^3.2.0"
    string-width "^3.0.0"
    strip-ansi "^5.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.4.2:
  version "2.4.3"
  resolved "http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz#1fd2e9ae1df3e75b8d8c367443c692d4ca81f481"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write@1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

"ws@>= 2.2.3":
  version "8.15.0"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.15.0.tgz#db080a279260c5f532fc668d461b8346efdfcf86"
  integrity sha1-2wgKJ5JgxfUy/GaNRhuDRu/fz4Y=

xml2js@^0.4.15, xml2js@^0.4.23:
  version "0.4.23"
  resolved "http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xss@^1.0.13:
  version "1.0.14"
  resolved "http://r.npm.sankuai.com/xss/download/xss-1.0.14.tgz#4f3efbde75ad0d82e9921cc3c95e6590dd336694"
  integrity sha1-Tz773nWtDYLpkhzDyV5lkN0zZpQ=
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

xtend@~4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

xttp@*:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/xttp/download/xttp-1.0.5.tgz#26956dca844713695843114ea1c9ce72eee7f7d2"
  integrity sha1-JpVtyoRHE2lYQxFOocnOcu7n99I=
  dependencies:
    iconv-lite latest
    mime2 latest

y18n@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-4.0.3.tgz#b5f259c82cd6e336921efd7bfd8bf560de9eeedf"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@13.1.1:
  version "13.1.1"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-13.1.1.tgz#d26058532aa06d365fe091f6a1fc06b2f7e5eca0"
  integrity sha1-0mBYUyqgbTZf4JH2ofwGsvfl7KA=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^13.0.0, yargs-parser@^13.1.1, yargs-parser@^13.1.2:
  version "13.1.2"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-13.1.2.tgz#130f09702ebaeef2650d54ce6e3e5706f7a4fb38"
  integrity sha1-Ew8JcC667vJlDVTObj5XBvek+zg=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs-unparser@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/yargs-unparser/download/yargs-unparser-1.6.0.tgz#ef25c2c769ff6bd09e4b0f9d7c605fb27846ea9f"
  integrity sha1-7yXCx2n/a9CeSw+dfGBfsnhG6p8=
  dependencies:
    flat "^4.1.0"
    lodash "^4.17.15"
    yargs "^13.3.0"

yargs@13.3.0:
  version "13.3.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-13.3.0.tgz#4c657a55e07e5f2cf947f8a366567c04a0dedc83"
  integrity sha1-TGV6VeB+Xyz5R/ijZlZ8BKDe3IM=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.1"

yargs@^13.2.2, yargs@^13.3.0:
  version "13.3.2"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-13.3.2.tgz#ad7ffefec1aa59565ac915f82dccb38a9c31a2dd"
  integrity sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=
  dependencies:
    cliui "^5.0.0"
    find-up "^3.0.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^3.0.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^13.1.2"

yargs@^16.0.0, yargs@^16.1.0:
  version "16.2.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.0.1:
  version "17.7.2"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

ylru@^1.2.0:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/ylru/download/ylru-1.3.2.tgz#0de48017473275a4cbdfc83a1eaf67c01af8a785"
  integrity sha1-DeSAF0cydaTL38g6Hq9nwBr4p4U=

yn@3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yn/download/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

zen-observable-ts@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/zen-observable-ts/download/zen-observable-ts-1.1.0.tgz#2d1aa9d79b87058e9b75698b92791c1838551f83"
  integrity sha1-LRqp15uHBY6bdWmLknkcGDhVH4M=
  dependencies:
    "@types/zen-observable" "0.8.3"
    zen-observable "0.8.15"

zen-observable@0.8.15:
  version "0.8.15"
  resolved "http://r.npm.sankuai.com/zen-observable/download/zen-observable-0.8.15.tgz#96415c512d8e3ffd920afd3889604e30b9eaac15"
  integrity sha1-lkFcUS2OP/2SCv04iWBOMLnqrBU=

zip-stream@^2.1.2:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/zip-stream/download/zip-stream-2.1.3.tgz#26cc4bdb93641a8590dd07112e1f77af1758865b"
  integrity sha1-JsxL25NkGoWQ3QcRLh93rxdYhls=
  dependencies:
    archiver-utils "^2.1.0"
    compress-commons "^2.1.1"
    readable-stream "^3.4.0"

zod@^3.19.1:
  version "3.22.4"
  resolved "http://r.npm.sankuai.com/zod/download/zod-3.22.4.tgz#f31c3a9386f61b1f228af56faa9255e845cf3fff"
  integrity sha1-8xw6k4b2Gx8iivVvqpJV6EXPP/8=
