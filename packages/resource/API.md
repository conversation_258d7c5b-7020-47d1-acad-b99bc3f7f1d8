# 接口文档

## 工程

### 工程列表接口

- 地址: `/api/node/project/list`
- 方法: `GET`
- 参数
  - page - 页码
  - page_size - 分页大小
  - tech_type - 技术栈类型 1 - Web 2 - MRN 3 - 小程序
  - status - 工程状态
  - org - 组织架构 id

### 工程详情接口

- 地址: `/api/node/project/detail`
- 方法: `GET`
- 参数
  - id - 工程 ID

### 页面流量接口

- 地址: `/api/node/project/flow`
- 方法: `GET`
- 参数
  - project_id - 工程 ID

### 工程更新接口

- 地址: `/api/node/project/update`
- 方法: `PUT`
- 参数
  - id - 工程 ID
  - name - 工程名称
  - description - 工程描述
  - git - Git 地址
  - status - 工程状态
  - org_ids - 工程归属组织架构 id 列表（从到店事业群到具体组织的所有 id 列表）
  - maintainers - 责任人列表

### 工程删除接口

- 地址: `/api/node/project/delete`
- 方法: `DELETE`
- 参数
  - id - 工程 ID

## 页面

### 页面列表接口

- 地址: `/api/node/page/list`
- 方法: `GET`
- 参数
  - page - 页码
  - page_size - 分页大小
  - tech_type - 技术栈类型 1 - Web 2 - MRN 3 - 小程序
  - org - 组织架构 id
  - project - 工程 ID，与 org 参数互斥
  - project_key - 工程 AppKey 或 Owl Key，优先级低于 project
  - start - 页面收录起始时间 (yyyyMMdd 格式)
  - end - 页面收录结束时间 (yyyyMMdd 格式)

### 页面搜索接口

- 地址: `/api/node/page/search`
- 方法: `GET`
- 参数
  - page - 页码
  - page_size - 分页大小
  - keyword - 页面 url/路径/cid 等关键字

### 页面详情接口

- 地址: `/api/node/page/detail`
- 方法: `GET`
- 参数
  - id - 页面 ID

### 页面流量接口

- 地址: `/api/node/page/flow`
- 方法: `GET`
- 参数
  - page_id - 页面 ID

### 页面更新接口

- 地址: `/api/node/page/update`
- 方法: `PUT`
- 参数
  - id - 页面 ID
  - name - 页面名称

### 页面删除接口

- 地址: `/api/node/page/delete`
- 方法: `DELETE`
- 参数
  - id - 页面 ID

## 组织架构

### 全部组织架构接口

- 地址: `/api/node/org/list`
- 方法: `GET`
- 参数: 无
