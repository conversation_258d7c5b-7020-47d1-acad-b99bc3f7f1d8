version: v1
build:
  tools:
    node: 10.16.0
    git:
  cache:
    dirs:
      - node_modules
  run:
    workDir: packages/resource
    cmd:
      - sh deploy/compile.sh
  target:
    distDir: packages/resource
    files:
      - ./*

autodeploy:
  targetDir: /opt/meituan/apps/nest/
  run: sh deploy/run.sh
  check: curl -v localhost:8080/health
  checkRetry: 10
  checkInterval: 10s
  tools:
    node: 10.16.0
