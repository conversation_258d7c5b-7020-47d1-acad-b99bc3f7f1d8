{"name": "resource-system", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "APP_ENV=local nest-cli run --watch --install-off", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --ext .ts --fix", "preinstall": "npx npm-force-resolutions"}, "license": "ISC", "devDependencies": {"@fdfe/era-cloud-uploader": "^0.11.2", "@mtfe/thrift-compiler": "^0.1.5", "@types/koa-bodyparser": "^4.3.5", "@types/lodash": "^4.14.182", "@types/md5": "^2.3.1", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "eslint": "^8.52.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "ts-node": "^10.4.0"}, "dependencies": {"@dp/lion-client": "^3.0.6", "@dp/pigeon-client": "^2.5.13", "@dp/pigeon-util": "^2.1.3", "@dp/squirrel-proxy-client": "^0.0.2", "@fdfe/ecf-http-adapter": "^1.0.21-beta2", "@gfe/moa": "^2.1.0-beta.2", "@gfe/zebra-typeorm-client": "^1.0.2", "@mtfe/ba-auth": "^0.0.5", "@mtfe/daxiang": "^2.3.2", "@mtfe/sc-plugin-ba": "^0.0.7", "@mtfe/sso-client": "^3.3.0", "@mtfe/talostwo-node-sdk": "^1.0.1", "@mtfe/thrift": "^4.0.3", "@mtfe/thrift-api-geo": "^1.0.3", "@mtfe/venus": "^0.0.14", "@types/koa": "^2.13.4", "@types/koa-router": "^7.4.4", "@types/node": "^12.19.0", "axios": "^0.27.2", "copyfiles": "^2.4.1", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "fastq": "^1.13.0", "koa": "^2.7.0", "koa-body": "^6.0.1", "koa-proxy": "^1.0.0-alpha.3", "koa-router": "^7.4.0", "koa2-cors": "^2.0.6", "lodash": "^4.17.21", "lru-cache": "^7.13.1", "md5": "^2.3.0", "mysql2": "^2.3.3", "string-template": "^1.0.0", "tslib": "^2.4.0", "typeorm": "^0.2.45", "typescript": "^4.4.4", "xss": "^1.0.13"}, "resolutions": {"@dp/lion-client": "^3.0.6"}, "prettier": {"semi": false, "singleQuote": true, "printWidth": 85}}