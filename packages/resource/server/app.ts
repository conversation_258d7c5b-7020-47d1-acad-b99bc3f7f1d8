import Moa, { AppOption } from '@gfe/moa'
import { getAppConfig } from './config/app'
import KoaCors from 'koa2-cors'
import { IS_LOCAL, IS_PROD } from './config/env'

const ssoMiddleware = require('@mtfe/sso-client').KoaSSO

export let app
export const createServer = async () => {
  console.log('process.env.APP_ENV -> ', process.env.APP_ENV)
  const appConfig: AppOption = await getAppConfig()
  const moa = new Moa()
  app = moa
  if (!IS_LOCAL) {
    moa.use(
      ssoMiddleware({
        clientId: 'vc-operate-web',
        secret: IS_PROD
          ? '7c2150a6e2fc47a386ad8c6b57718056'
          : 'ca8eece10f6a46029a52c2844b6af11e',
        excludedUriList: '/**/heartbeat,/heartbeat,/api/nrp/ba/**/*',
        callbackUri: '/api/nrp/sso/callback',
        logoutUri: '/api/nrp/sso/logout',
        isDebug: false,
      })
    )
  }
  const start = moa.run(appConfig)
  moa.use(
    KoaCors({
      origin: function (ctx) {
        return 'http://local.test.sankuai.com:3000'
      },
      exposeHeaders: ['WWW-Authenticate', 'Server-Authorization'],
      maxAge: 5,
      credentials: true,
      allowMethods: ['GET', 'POST', 'DELETE'],
      allowHeaders: [
        'Content-Type',
        'Authorization',
        'Accept',
        'x-requested-with',
      ],
    })
  )
  // moa.use(
  //   scPluginBA({
  //     // 存储 BA 字段的 KMS 的 appkey
  //     appkey: 'com.sankuai.dzufebiz.manage',
  //     // 存储 BA 的 secret 字段的 KMS 的字段名
  //     key: 'nrp_livecard',
  //     // // [可选] BA 的 client 名称，不填时默认使用 key 的值
  //     // clientId: 'xxx',
  //     // [可选] 对路径进行匹配，支持 ant 风格字符串或 function（与 matcherAction 结合使用）
  //     matcher: '/api/nrp/ba/**',
  //     // [可选] 匹配上路径后的操作，ignore-跳过该路径，keep-对该路径使用中间件，不填时默认为 keep
  //     matcherAction: 'keep',
  //     // [可选] 自定义异常消息
  //     // errorResBuilder: (ctx, code) => {
  //     //     return {
  //     //         code: code,
  //     //         message: 'BA鉴权失败',
  //     //         error: {}
  //     //     };
  //     // }
  //   })
  // )
  await start
  return moa
}
