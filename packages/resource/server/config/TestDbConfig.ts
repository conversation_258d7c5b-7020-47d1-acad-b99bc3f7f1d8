import { AppOption } from '@gfe/moa'
import { IS_PROD } from './env'
import {
  ResourceEntity,
  ResourceChangeLogEntity,
  DeliveryChangeLogEntity,
  CityMappingEntity,
  ResourceTemplateEntity,
  PermissionEntity,
} from '../entities'

import { APP_NAME } from './constant'
import { getLionKey } from '../utils/lion'

const entities = [
  ResourceEntity,
  ResourceChangeLogEntity,
  DeliveryChangeLogEntity,
  CityMappingEntity,
  ResourceTemplateEntity,
  PermissionEntity,
]

export async function getAppConfig() {
  const zebraString = await getLionKey('mysql')
  console.log(`配置初始化：环境${IS_PROD ? 'prod' : 'test'},sql:${zebraString}`)
  const appConfig: AppOption = {
    dbConfig: {
      appName: APP_NAME,
      jdbcRef: zebraString,
      type: 'mysql',
      entities,
      bigNumberStrings: false,
      supportBigNumbers: true,
      synchronize: false,
    },
  }
  return {
    CONFIG: appConfig,
    logger: { info: () => {} },
  }
}
