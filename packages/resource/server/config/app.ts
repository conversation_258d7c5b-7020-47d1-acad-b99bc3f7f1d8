import { AppOption } from '@gfe/moa'
import path from 'path'
import { IS_PROD, IS_LOCAL } from './env'
import { ResourceEntity, ResourceChangeLogEntity, DeliveryChangeLogEntity, CityMappingEntity, ResourceTemplateEntity, SceneEntity, PermissionEntity, CommonChangeLog } from '../entities'
import {
  LoggerMiddleware,
  SecurityMiddleware,
  BasicAuthMiddleware,
  UserMiddleware,
} from '../middlewares'
import { APP_NAME } from './constant'
import { getLionKey } from '../utils/lion'

const entities = [
  ResourceEntity, ResourceChangeLogEntity, DeliveryChangeLogEntity, CityMappingEntity, ResourceTemplateEntity, SceneEntity, PermissionEntity, CommonChangeLog
]

export async function getAppConfig() {
  const zebraString = await getLionKey('mysql')
  console.log(`配置初始化：环境${IS_PROD ? 'prod' : 'test'},sql:${zebraString}`)
  const appConfig: AppOption = {
    name: APP_NAME,
    controllerDir: path.resolve(__dirname, '../controller'),
    port: 8080,
    middlewares: [
      LoggerMiddleware,
      SecurityMiddleware,
      BasicAuthMiddleware,
      UserMiddleware,
    ],
    notListen: true,
    dbConfig: {
      appName: APP_NAME,
      jdbcRef: zebraString,
      type: 'mysql',
      entities,
      bigNumberStrings: false,
      supportBigNumbers: true,
      synchronize: false,
    },
    pigeonConfigFile: path.resolve(__dirname, './pigeon'),
  }
  return appConfig
}
