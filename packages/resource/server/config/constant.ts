import { DeliveryItemStatusEnum, OperatorStatus } from '../interface/delivery'
import { AuthEnum } from '../interface/resource'
export const APP_NAME = 'com.sankuai.dzufebiz.manage'

export const LionKey = {
  PROJECT_APP_KEY: APP_NAME,
}

export const REDIS_DATABASE = 'redis-swift'

export const DEFAULT_TIME_INTERVAL = 30 * 60 * 1000

export const MUSHROOM_BIZ_CODE = 'rms_material_automation'

export const OPERATOR_STATUS_CONFIG: { [propName: string]: OperatorStatus } = {}
OPERATOR_STATUS_CONFIG[DeliveryItemStatusEnum.PendingAudit] = {
  canEdit: true,
  canOffline: false,
  canDenyAudit: true,
  canAudit: true,
}

OPERATOR_STATUS_CONFIG[DeliveryItemStatusEnum.Audited] = {
  canEdit: false,
  canOffline: true,
  canDenyAudit: false,
  canAudit: false,
}
OPERATOR_STATUS_CONFIG[DeliveryItemStatusEnum.Rejected] = {
  canEdit: true,
  canOffline: false,
  canDenyAudit: false,
  canAudit: false,
}
OPERATOR_STATUS_CONFIG[DeliveryItemStatusEnum.Offline] = {
  canEdit: false,
  canOffline: false,
  canDenyAudit: false,
  canAudit: false,
}

export const OPERATOR_STATUS_AUTH_CONFIG: {
  [propName: string]: OperatorStatus
} = {}
OPERATOR_STATUS_AUTH_CONFIG[AuthEnum.ADMIN] = {
  canEdit: true,
  canOffline: true,
  canDenyAudit: true,
  canAudit: true,
}
// OPERATOR_STATUS_AUTH_CONFIG[AuthEnum.EDIT] = {
//   canEdit: true,
//   canOffline: true,
//   canDenyAudit: false,
//   canAudit: false,
// }

OPERATOR_STATUS_AUTH_CONFIG[AuthEnum.NONE] = {
  canEdit: false,
  canOffline: false,
  canDenyAudit: false,
  canAudit: false,
}
