import Mo<PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
import { isNecessaryParamsReq, logError, setHttpError, setHttpResult } from '../utils'
import { OrgService } from '../service/authorize/Org.service'

@Controller('/api/nrp/org')
export default class OrgController extends BaseController {
  constructor(private orgService: OrgService) {
    super()
  }

  @Get('/pathname/query')
  async queryOrgByPathName(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['orgPathName'], ctx.query)
      const orgPathName = ctx.query.orgPathName as string
      const org = await this.orgService.queryOrgByOrgPathName(orgPathName)
      return setHttpResult(org)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/org/pathname/query', err)
      throw setHttpError(err)
    }
  }
}
