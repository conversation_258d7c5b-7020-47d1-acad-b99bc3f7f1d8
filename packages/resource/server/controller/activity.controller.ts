import <PERSON><PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
const Pigeon = require("@dp/pigeon-client")
const java = Pigeon.java

@Controller('/api/nrp/activity')
export default class ActivityController extends BaseController {
  constructor() {
    super()
  }

  @Get('/list')
  async busyPeriod(ctx: Moa.Context) {
    try {
      return {
        code: 200,
        data: 'hello world'
      }
    } catch (err) {
      console.log(err)
      ctx.cat.logError('api:/api/nrp/activity/list', err)
    }
  }


}
