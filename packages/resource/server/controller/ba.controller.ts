import <PERSON><PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
import { DeliveryService } from '../service/delivery.service'
import { DeliveryDBService } from '../service/DB/DeliveryDB.service'
import { isNecessaryParamsReq, setHttpResult, setHttpError, logError } from '../utils'
import { SubmitDeliveryReq } from '../interface/delivery'
import { CommonService } from '../service'
import { IS_PROD } from '../config/env'

@Controller('/api/nrp/ba')
export default class BaController extends BaseController {
  constructor(
    protected deliveryService: DeliveryService,
    protected deliveryDBS: DeliveryDBService,
    protected commonService: CommonService,
  ) {
    super()
  }

  @Post('/delivery/live/create')
  async createDelivery(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['resourcePositionId', 'deliveryItems', 'resourceType'], ctx.query)
      const query = ctx.query as unknown as SubmitDeliveryReq
      const { deliveryItems } = query

      /**
       * deliveryItem 参数合法校验
       */
      this.deliveryService.verifyDelivery(deliveryItems)

      const res = await this.deliveryService.tranformCityMT2DP(ctx, deliveryItems)
      let { dpDeliveryItems, errorCityList } = res

      if (errorCityList.length) {
        const cityMap = await this.commonService.getMTCityMapByIds(ctx, errorCityList)
        errorCityList = errorCityList.map(c => cityMap[c]?.name).filter(i => i)
      }


      /**
       * 同时创建美团、点评直播大卡资源位
       */
      const param = [query]
      let dpQuery: any = {}
      if (dpDeliveryItems.length) {
        dpQuery = Object.assign({}, query, {
          resourcePositionId: 'dp.app.play.channel.livecard',
          deliveryItems: dpDeliveryItems
        })
        param.push(dpQuery)
      }


      await this.deliveryService.createDelivery(ctx, param)
      this.deliveryDBS.addDeliveryChangelog(0, 'mt.app.play.channel.livecard', -1, JSON.stringify(query),
        JSON.stringify(
          [query, dpQuery]
        )
        , query.creator)
      return setHttpResult({
        jumpUrl: `${IS_PROD ? 'https://jing.sankuai.com' : 'https://jing.dzu.test.sankuai.com'}/res-bit/op/manage-page.html?homeTabKey=home&opKey=dp.app.play.channel.livecard`,
        mtErrorCity: [],
        dpErrorCity: errorCityList,
      })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/ba/delivery/live/create', err)
      throw setHttpError(err)
    }
  }

}
