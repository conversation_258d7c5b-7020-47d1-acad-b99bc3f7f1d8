import <PERSON><PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
import hmacSha1 from 'crypto-js/hmac-sha1'
import Base64 from 'crypto-js/enc-base64'
import { CommonService, ResourceService } from '../service'
import { setHttpError, setHttpResult, getAvatar, isNecessaryParamsReq, logError } from '../utils/index'
import { PlatformEnum } from '../interface'
import { getLionKey } from '../utils/lion'
import { VenusInfo } from '../interface/common'

@Controller('/api/nrp/common')
export default class CommonController extends BaseController {
  constructor(
    protected commonService: CommonService,
    protected resourceService: ResourceService
  ) {
    super()
  }

  @Get('/city')
  async getAllCity(ctx: Moa.Context) {
    try {
      const query = ctx.query
      const resourcePositionId = query.resourcePositionId as string
      // 获取资源位信息
      const resourceItem = await this.resourceService.getResourceByRPId(resourcePositionId)
      let res = []
      if (resourceItem?.platform === PlatformEnum.DP) {
        res = await this.commonService.getAllDpCity(ctx)
      } else {
        res = await this.commonService.getMTAllCity(ctx)
      }
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city', err)
      throw setHttpError(err)
    }
  }
  @Get('/city/province')
  async getProvinceCity(ctx: Moa.Context) {
    try {
      const query = ctx.query
      const resourcePositionId = query.resourcePositionId as string
      // 获取资源位信息
      const resourceItem = await this.resourceService.getResourceByRPId(resourcePositionId)
      let res = []
      if (resourceItem?.platform === PlatformEnum.DP) {
        res = await this.commonService.getAllDpCityProvince(ctx)
      } else {
        res = await this.commonService.getMTProvinceCity(ctx)
      }
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city', err)
      throw setHttpError(err)
    }
  }

  @Get('/city/mt2dp')
  async transMT2DP(ctx: Moa.Context) {
    try {
      let cityid = [] as number[]
      if (ctx.query.cityid) {
        cityid = [ctx.query.cityid as unknown as number]
      }
      if (ctx.query.cityIds) {
        cityid = JSON.parse(ctx.query.cityIds as string)
      }
      const res = await this.commonService.mtCity2DPByHive(ctx, cityid)
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city', err)
      throw setHttpError(err)
    }
  }
  @Get('/city/dp/id')
  async getDpCityInfoById(ctx: Moa.Context) {
    try {
      const cityid = ctx.query.cityid as unknown as number
      const res = await this.commonService.getDpCityinfoById(ctx, cityid)
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city/dp', err)
      throw setHttpError(err)
    }
  }
  @Get('/city/dp/list')
  async getAllDpCity(ctx: Moa.Context) {
    try {
      const res = await this.commonService.getAllDpCity(ctx)
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city/dp', err)
      throw setHttpError(err)
    }
  }
  @Get('/city/dp/province')
  async getAllDpCityByProvince(ctx: Moa.Context) {
    try {
      const res = await this.commonService.getAllDpCityProvince(ctx)
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/city/dp', err)
      throw setHttpError(err)
    }
  }

  @Get('/user')
  async getUserInfo(ctx: Moa.Context) {
    try {
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      return setHttpResult({
        mis: ssomis,
        avatar: getAvatar(ssomis)
      })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/resource/list', err)
      throw setHttpError(err)
    }
  }

  @Get('/lion')
  async getLionKey(ctx: Moa.Context) {
    try {
      const key = ctx.query.key as string
      const keyMap = JSON.parse(await getLionKey("resource.get_lion_key_info"))
      if (!keyMap.includes(key)) {
        return setHttpResult({
          key,
          value: ''
        })
      }
      const value = await getLionKey(key)
      return setHttpResult({
        key,
        value: value
      })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/getLionKey', err)
      throw setHttpError(err)
    }
  }

  @Get('/venus/auth')
  async getVenusBAToken(ctx: Moa.Context) {
    try {
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      if (!ssomis) {
        throw new Error('请先登录')
      }
      const venusInfo: VenusInfo = JSON.parse(await getLionKey("resource.venus_info"))
      const requestURI = '/storage/nrpresourcevenus'
      const id = venusInfo.client_id
      const secret = venusInfo.client_secret
      const httpVerb = 'POST'

      const date = new Date().toUTCString()
      const stringToSign = httpVerb + ' ' + requestURI + '\n' + date
      const signature = Base64.stringify(hmacSha1(stringToSign, secret))
      return setHttpResult({
        token: `MWS ${id}:${signature}`,
        date
      })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/common/venus/auth', err)
      throw setHttpError(err)
    }
  }
  // @Get('/org/fussy/mis')
  // async searchMisOrg(ctx: Moa.Context) {
  //   try {
  //     const query = ctx.query
  //     // 获取sso鉴权的mis号，而非参数上的mis号
  //     const keyword: string = query.keyword as unknown as string

  //     const result = await this.commonService.searchKeyWorldOrg(
  //       keyword,
  //     )
  //     return setHttpResult(result)
  //   } catch (err) {
  //     ctx.cat.logError('api:/common/org/fussy/mis', err)
  //     throw setHttpError(err)
  //   }
  // }
}
