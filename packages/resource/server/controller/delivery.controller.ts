import <PERSON><PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
import { DeliveryService } from '../service/delivery.service'
import { ResourceService } from '../service/resource.service'
import { DeliveryDBService } from '../service/DB/DeliveryDB.service'
import { CommonService } from '../service/common.service'
import {
  isNecessaryParamsReq,
  setHttpResult,
  setHttpError,
  getDataBykeyFromArray,
  uniqueArr,
  checkDeliveryRulesConflict,
  logError,
} from '../utils'

import {
  SubmitDeliveryReq,
  DeliveryActivityReq,
  UpdateDeliveryReq,
  UpdateDeliveryRuleReq,
  QueryByItemRequest,
  DeliveryItemStatusEnum,
  UpdateItemStatusRequest,
  ScheduleRes,
  ScheduleShowTypeEnum,
  DeliveryItemDTO,
} from '../interface'
import { ResourcePermissionService } from '../service/authorize/ResourcePermission.service'

@Controller('/api/nrp/delivery')
export default class DeliveryController extends BaseController {
  constructor(
    protected deliveryService: DeliveryService,
    protected deliveryDBS: DeliveryDBService,
    protected resourceService: ResourceService,
    protected commonService: CommonService,
    protected resourcePermissionService: ResourcePermissionService
  ) {
    super()
  }

  @Post('/create')
  async createDelivery(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['deliveryActivities'], ctx.query)
      const { deliveryActivities } = ctx.query as unknown as {
        deliveryActivities: SubmitDeliveryReq[];
      }

      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      if (deliveryActivities.length === 0) {
        throw new Error('提交活动内容不能为空')
      }
      const rpIdforPermCheck = new Set<string>()
      deliveryActivities.forEach((it) => {
        isNecessaryParamsReq(
          ['resourcePositionId', 'deliveryItems', 'resourceType'],
          it
        )
        rpIdforPermCheck.add(it.resourcePositionId)
      })

      for (const id of rpIdforPermCheck.values()) {
        const canAccess = this.resourcePermissionService.canAccessPermission(ssomis, id)
        if (!canAccess) {
          throw new Error(`用户没有在资源位(key: ${id})下创建投放信息的权限`)
        }
      }




      const submitData: Array<SubmitDeliveryReq> = []

      for (let index = 0; index < deliveryActivities.length; index++) {
        const { deliveryItems, resourcePositionId } = deliveryActivities[index]
        let { auditFlowControlKey } = deliveryActivities[index]
        /**
         * deliveryItem 参数合法校验
         * 审批流参数组装
         */
        const resourceItem = await this.resourceService.getResourceByRPId(
          resourcePositionId
        )
        if (!resourceItem) {
          throw Error(`${resourcePositionId}资源位不存在`)
        }
        if (resourceItem.extra?.permission?.auditFlowTemplate) {
          if (!auditFlowControlKey && resourceItem.extra?.permission?.auditFlowTemplate?.['default']) {
            auditFlowControlKey = 'default'
          }
          if (auditFlowControlKey) {
            const approvalFlowConfig = await this.deliveryService.getDeliveryApprovalFlow(resourceItem, auditFlowControlKey)
            this.deliveryService.verifyDelivery(deliveryItems)
            deliveryActivities[index].approvalFlowConfig = approvalFlowConfig
          }
        }

        submitData.push(deliveryActivities[index])
      }

      await this.deliveryService.createDelivery(ctx, submitData, ssomis)
      this.deliveryDBS.addDeliveryChangelog(
        0,
        submitData.map((data) => data.resourcePositionId).join(','),
        -1,
        JSON.stringify({}),
        JSON.stringify({
          query: submitData,
        }),
        ssomis
      )

      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/create', err)
      throw setHttpError(err)
    }
  }
  @Get('/list')
  async queryDeliveryList(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['pageIndex', 'pageSize'], ctx.query)
      const query = ctx.query as unknown as DeliveryActivityReq

      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      if (query.ids) {
        query.ids = (query.ids as string)
          .split(',')
          ?.filter((id) => id)
          .map((id) => Number(id))
      } else {
        query.ids = null
      }
      const processQuery = { ...query }
      delete processQuery.pageIndex
      delete processQuery.pageSize
      Object.keys(processQuery).forEach((key) => {
        if (typeof processQuery[key] === 'string') {
          if (processQuery[key][0] === '[' && processQuery[key][processQuery[key].length - 1] === ']') {
            processQuery[key] = JSON.parse(processQuery[key])
          }
        }
      })

      let { total, list } = await this.deliveryService.getDeliveryList(
        ctx,
        { ...query, ...processQuery }
      )

      /**
       * 点评资源位再前端展示时都换成 美团城市体系
       */
      const resIds = list.map((r) => r.resourcePositionId)
      const rpList = await this.resourceService.getResourceListByIds(resIds)

      /**
       * 加一些前端 ui 用的字段
       */
      list = await this.deliveryService.assembleDeliveryList(
        list,
        ssomis,
        rpList
      )

      return setHttpResult({ total, list })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/list', err)
      throw setHttpError(err)
    }
  }
  @Get('/query/id')
  async queryDeliveryByid(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['id'], ctx.query)
      const query = ctx.query as unknown as DeliveryActivityReq
      const TYPE = 100
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      const deliveryActivities = await this.deliveryService.getDeliveryById(ctx, {
        type: TYPE,
        ids: [query.id],
      })

      const item = deliveryActivities?.[0]

      const resourceItem = await this.resourceService.getResourceByRPId(
        item.resourcePositionId
      )

      const res = await this.deliveryService.assembleDeliveryList(
        deliveryActivities,
        ssomis,
        [resourceItem]
      )
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/query/id', err)
      throw setHttpError(err)
    }
  }
  @Get('/rule/query/id')
  async queryDeliveryRuleByid(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['id'], ctx.query)
      const query = ctx.query as unknown as DeliveryActivityReq
      const TYPE = 200
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      const deliveryActivities = await this.deliveryService.getDeliveryById(ctx, {
        type: TYPE,
        ids: [query.id],
      })
      const item = deliveryActivities?.[0]
      if (!item) {
        throw Error(`id:${query.id} 不存在`)
      }
      const resourceItem = await this.resourceService.getResourceByRPId(
        item?.resourcePositionId
      )

      // let repList = []
      // if (resourceItem.platform === PlatformEnum.DP) {
      //   repList.push(item?.resourcePositionId)
      // }
      // deliveryActivities = await this.deliveryService.tranformCityDP2MT(ctx, deliveryActivities, repList)

      const res = await this.deliveryService.assembleDeliveryList(
        deliveryActivities,
        ssomis,
        [resourceItem]
      )

      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/rule/query/id', err)
      throw setHttpError(err)
    }
  }

  @Post('/update')
  async upodateDelivery(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['id'], ctx.query)
      const query = ctx.query as unknown as UpdateDeliveryReq
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      const TYPE = 100
      /**
       * 权限校验
       * 更新状态的是否是活动创建人、或者管理员
       */
      const deliveryActivities = await this.deliveryService.getDeliveryById(
        ctx,
        {
          type: TYPE,
          ids: [query.id],
        }
      )
      const item = deliveryActivities?.[0]
      if (!item) {
        throw new Error(`id:${query.id}活动不存在`)
      }
      const resourceItem = await this.resourceService.getResourceByRPId(
        item.resourcePositionId
      )
      const canUpdate = await this.resourcePermissionService.canUpdateAndOfflinePermission(ssomis, item.creator, resourceItem.resourcePositionId)
      if (!canUpdate) {
        throw new Error(`用户非创建者或没有在该资源位(key: ${resourceItem.resourcePositionId})下进行更新的权限。`)
      }

      /**
       * 校验当前活动的状态是否可以编辑
       */
      if (
        item.deliveryItems.some((item) => {
          return item.status === DeliveryItemStatusEnum.Audited
        })
      ) {
        throw new Error(`有已审核投放规则，无法修改投放信息`)
      }

      const params = {
        id: query.id,
        type: TYPE,
        materialInfo: query.materialInfo,
        extra: query.extra,
      }
      await this.deliveryService.updateDelivery(ctx, params)
      this.deliveryDBS.addDeliveryChangelog(
        1,
        item.resourcePositionId,
        item.id,
        JSON.stringify(deliveryActivities),
        JSON.stringify({
          params,
        }),
        ssomis
      )
      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/update', err)
      throw setHttpError(err)
    }
  }
  @Post('/update/rule/info')
  async upodateDeliveryRuleItem(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['id'], ctx.query)
      const query = ctx.query as unknown as UpdateDeliveryRuleReq
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      if (
        query.startTime &&
        query.endTime &&
        query.endTime <= query.startTime
      ) {
        throw new Error(`投放结束时间应大于开始时间`)
      }
      const TYPE = 200

      /**
       * 权限校验
       * 更新状态的是否是活动创建人
       */
      const deliveryActivities = await this.deliveryService.getDeliveryById(
        ctx,
        {
          type: TYPE,
          ids: [query.id],
        }
      )
      const item = deliveryActivities?.[0]
      if (!item) {
        throw new Error(`活动(id:${query.id})不存在`)
      }
      const resourceItem = await this.resourceService.getResourceByRPId(
        item.resourcePositionId
      )
      if (!resourceItem) {
        throw new Error(`活动(id:${query.id})所属资源位不存在`)
      }

      const canUpdate = await this.resourcePermissionService.canUpdateAndOfflinePermission(ssomis, item.creator, item.resourcePositionId)

      if (!canUpdate) {
        throw new Error(`用户非创建者或没有在该资源位(key: ${resourceItem.resourcePositionId})下进行更新的权限。`)
      }


      const params = {
        id: query.id,
        type: TYPE,
        startTime: query.startTime,
        endTime: query.endTime,
        cityIds: query.cityIds,
        priority: query?.priority,
        extra: query?.extra
      }

      /**
       * 城市信息转换
       */
      // if (resourceItem.platform === PlatformEnum.DP) {
      //   params.cityIds = await this.deliveryService.mt2dpCity(ctx, query.cityIds)
      // }

      await this.deliveryService.updateDelivery(ctx, params)

      this.deliveryDBS.addDeliveryChangelog(
        1,
        item.resourcePositionId,
        item.id,
        JSON.stringify(deliveryActivities),
        JSON.stringify({
          params,
        }),
        ssomis
      )

      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/update/rule/info', err)
      throw setHttpError(err)
    }
  }
  // 仅用手动更新单条数据
  @Post('/update/rule/status')
  async upodateDeliveryRuleStatus(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['approvalInfos', 'status'], ctx.query)
      const query = ctx.query as unknown as UpdateItemStatusRequest
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      query.approver = query.approver || ssomis

      const status = Number(query.status)
      const TYPE = 200


      if (status === DeliveryItemStatusEnum.Audited || status === DeliveryItemStatusEnum.Rejected) {
        for (let index = 0; index < query.approvalInfos.length; index++) {
          if (query.approvalInfos[index].level === undefined) {
            throw new Error('审核流层级配置出错')
          }
        }
      }


      const deliveryActivities = await this.deliveryService.getDeliveryById(
        ctx,
        {
          type: TYPE,
          ids: query.approvalInfos.map(info => info.itemId),
        }
      )
      const resourceStrs = uniqueArr(
        deliveryActivities.map((i) => i.resourcePositionId)
      )
      const resourceList = await this.resourceService.getResourceListByIds(
        resourceStrs
      )

      if (!resourceList.length) {
        throw Error(`id 对应的资源位${JSON.stringify(resourceStrs)}不存在`)
      }

      /**
       * 权限校验
       * 审核、拒绝需要是管理员
       */
      if (
        status === DeliveryItemStatusEnum.Audited ||
        status === DeliveryItemStatusEnum.Rejected
      ) {
        for (const res of resourceList) {
          const canApprove = await this.resourcePermissionService.canAprrovePermission(ssomis, res.resourcePositionId)
          if (!canApprove) {
            throw Error(
              `${ssomis} 没有资源位:${res.resourcePositionId}的审核权限，无法变更状态`
            )
          }
        }
      }
      /**
       * 权限校验
       * 提审、删除需要是管理员或本人
       */
      if (
        status === DeliveryItemStatusEnum.PendingAudit ||
        status === DeliveryItemStatusEnum.Offline
      ) {
        for (const item of deliveryActivities) {
          const resourceItem = getDataBykeyFromArray(
            resourceList,
            'resourcePositionId',
            item.resourcePositionId
          )
          if (!resourceItem) {
            throw new Error(`资源位(key: ${item.resourcePositionId})不存在`)
          }
          const canUpdate = await this.resourcePermissionService.canUpdateAndOfflinePermission(ssomis, item.creator, item.resourcePositionId)
          if (!canUpdate) {
            throw Error(
              `${ssomis} 不是待修改活动(id: ${item.id})创建者或资源位:${item.resourcePositionId}的管理员，无法变更状态`
            )
          }
        }
      }

      await this.deliveryService.updateItemStatus(ctx, query)

      this.deliveryDBS.addDeliveryChangelog(
        status === DeliveryItemStatusEnum.Offline ? 2 : 1,
        JSON.stringify(resourceStrs),
        -1,
        JSON.stringify(deliveryActivities),
        JSON.stringify({
          query,
        }),
        ssomis
      )

      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/update/rule/status', err)
      throw setHttpError(err)
    }
  }
  // 用于批量更新数据状态
  @Post('/update/rule/batch/status')
  async batchUpdateDeliveryRule(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['status', 'approvalInfos'], ctx.query)
      const query = ctx.query as unknown as UpdateItemStatusRequest
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      query.approver = query.approver || ssomis

      const status = Number(query.status)
      const TYPE = 200

      if (status === DeliveryItemStatusEnum.Audited || status === DeliveryItemStatusEnum.Rejected) {
        for (let index = 0; index < query.approvalInfos.length; index++) {
          if (query.approvalInfos[index].level === undefined) {
            throw new Error('审核流层级配置出错')
          }
        }
      }
      if (query.approvalInfos.length === 0) {
        throw new Error('要审批的记录个数不能为空')
      }
      const deliveryActivities = await this.deliveryService.getDeliveryById(
        ctx,
        {
          type: TYPE,
          ids: query.approvalInfos.map(item => item.itemId),
        }
      )
      const resourceStrs = uniqueArr(
        deliveryActivities.map((i) => i.resourcePositionId)
      )
      const resourceList = await this.resourceService.getResourceListByIds(
        resourceStrs
      )

      if (!resourceList.length) {
        throw Error(`id 对应的资源位${JSON.stringify(resourceStrs)}不存在`)
      }

      /**
       * 权限校验
       * 审核、拒绝需要是管理员
       */
      if (
        status === DeliveryItemStatusEnum.Audited ||
        status === DeliveryItemStatusEnum.Rejected
      ) {
        for (const res of resourceList) {
          const canAudit = await this.resourcePermissionService.canAprrovePermission(ssomis, res.resourcePositionId)
          if (!canAudit) {
            throw Error(
              `${ssomis} 无资源位:${res.resourcePositionId}的管理权限，无法变更状态`
            )
          }
        }
      }
      /**
       * 权限校验
       * 提审、删除需要是管理员或本人
       */
      if (
        status === DeliveryItemStatusEnum.PendingAudit ||
        status === DeliveryItemStatusEnum.Offline
      ) {
        for (const item of deliveryActivities) {
          const resourceItem = getDataBykeyFromArray(
            resourceList,
            'resourcePositionId',
            item.resourcePositionId
          )

          if (!resourceItem) {
            throw Error(`投放(id: ${item.id}) 对应的资源位${item.resourcePositionId}不存在`)
          }

          const canUpdate = await this.resourcePermissionService.canUpdateAndOfflinePermission(ssomis, item.creator, item.resourcePositionId)

          if (!canUpdate) {
            throw Error(
              `${ssomis} 不是创建者：${item.creator}或无资源位:${item.resourcePositionId}管理权限，无法变更状态`
            )
          }
        }
      }

      // 按城市分组
      const rules = deliveryActivities
      const ruleCityMap = new Map<number, DeliveryItemDTO[]>()
      const ids = new Set<number>()
      rules.forEach(act => {
        act.deliveryItems.forEach(it => {
          it.cityIds.forEach(city => {
            // 提取需要改变状态的规则
            // 通过和驳回只能从待审核转变过来
            // 下线只能从通过转变
            // 待审核从驳回转变
            if ((query.status === DeliveryItemStatusEnum.Audited || query.status === DeliveryItemStatusEnum.Rejected) && it.status === DeliveryItemStatusEnum.PendingAudit) {
              ids.add(it.id)
            } else if (query.status === DeliveryItemStatusEnum.Offline && it.status === DeliveryItemStatusEnum.Audited) {
              ids.add(it.id)
            } else if (query.status === DeliveryItemStatusEnum.PendingAudit && it.status === DeliveryItemStatusEnum.Rejected) {
              ids.add(it.id)
            }
            if (ruleCityMap.has(city)) {
              ruleCityMap.get(city)!.push(it)
            } else {
              ruleCityMap.set(city, [it])
            }
          })
        })
      })
      // 如果是审批通过 需要判断当前规则无冲突
      if (query.status === DeliveryItemStatusEnum.Audited && !query?.usePriority) {
        const allRules = [...ruleCityMap.entries()]
        for (const cityRules of allRules) {
          const cityRule = cityRules[1]
          for (let i = 0; i < cityRule.length; i++) {
            for (let j = i + 1; j < cityRule.length; j++) {
              if (!checkDeliveryRulesConflict(cityRule[i], cityRule[j])) {
                throw Error(
                  `一键审批失败：规则(id:${cityRule[i].activityId})与规则(id:${cityRule[j].activityId})生效时间冲突。`
                )
              }
            }
          }
        }
      }
      const approvalInfos = query.approvalInfos.filter(info => {
        return ids.has(info.itemId)
      })
      if (!approvalInfos.length) {
        throw Error("请检查状态，无可审批规则")
      }
      await this.deliveryService.updateItemStatus(ctx, {
        approver: query.approver,
        status,
        approvalInfos
      })

      this.deliveryDBS.addDeliveryChangelog(
        status === DeliveryItemStatusEnum.Offline ? 2 : 1,
        JSON.stringify(resourceStrs),
        -1,
        JSON.stringify(deliveryActivities),
        JSON.stringify({
          query,
        }),
        ssomis
      )

      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/update/rule/batch/status', err)
      throw setHttpError(err)
    }
  }

  @Post('/schedule')
  async getSchedule(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(
        ['resourcePositionId', 'startTime', 'endTime'],
        ctx.query
      )
      const query = ctx.query as unknown as QueryByItemRequest & { isNew?: boolean }

      // 获取资源位信息
      const resourceItem = await this.resourceService.getResourceByRPId(
        query.resourcePositionId
      )

      // 查询资源位resourcePositionId 投放规则
      const deliveryActivities = await this.deliveryService.queryByItem(
        ctx,
        query
      )

      // deliveryActivities = await this.deliveryService.tranformCityDP2MT(ctx, deliveryActivities, repList)
      // 获取时间范围
      let startTime = parseInt(ctx.query.startTime as string)
      let endTime = parseInt(ctx.query.endTime as string)
      deliveryActivities.forEach((activity) => {
        activity.deliveryItems.forEach(delivery => {
          const itStartTime = delivery.startTime
          const itEndTime = delivery.endTime
          if (itStartTime < startTime) {
            startTime = itStartTime
          }
          if (itEndTime > endTime) {
            endTime = itEndTime
          }
        })
      })
      const startDate = new Date(startTime)
      startDate.setHours(0, 0, 0)
      const endDate = new Date(endTime)
      endDate.setHours(23, 59, 59)


      // const scheduleCityItems = await this.deliveryService.formatScheduleOneLine(ctx, deliveryActivities, resourceItem)
      const scheduleCityItems = await this.deliveryService.formatScheduleMoreLine(ctx, deliveryActivities, resourceItem)


      const res: ScheduleRes = {
        type: ScheduleShowTypeEnum.Separate,
        startTime: startDate.getTime(),
        endTime: endDate.getTime(),
        timeInterval: resourceItem.timeInterval,
        items: scheduleCityItems
      }

      return setHttpResult(res)

    } catch (err) {
      logError(ctx, 'api:/api/nrp/delivery/schedule', err)
      throw setHttpError(err)
    }
  }
}
