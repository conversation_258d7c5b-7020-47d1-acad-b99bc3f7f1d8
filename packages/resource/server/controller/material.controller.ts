import Mo<PERSON>, { Controller, BaseController, Post, Get } from '@gfe/moa'
import {
  ImgGenerateReq,
  XiuyuChannelBannerBizDataMap,
} from 'server/interface/material'
import { MaterialService, ImageAutoService } from '../service'
import {
  setHttpError,
  setHttpResult,
  isNecessaryParamsReq,
  logError,
} from '../utils/index'
import { IS_PROD } from '../config/env'
import { getLionKey } from '../utils/lion'
import {
  XiuBannerCanvasBgColorList,
  XiuBannerVisualMainPicList,
} from '../config/BannerMaterial'

@Controller('/api/nrp/material')
export default class MaterialController extends BaseController {
  constructor(
    protected ms: MaterialService,
    protected imageService: ImageAutoService
  ) {
    super()
  }

  @Get('/live')
  async getLiveInfo(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['id'], ctx.query)
      const id = ctx.query.id as unknown as number
      const res = await this.ms.getLiveInfo(id)
      return setHttpResult(res.data)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/material/live', err)
      throw setHttpError(err)
    }
  }

  @Post('/autogenimage/xiuyuchannelbanner')
  async autoGenImage(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['title', 'subtitle'], ctx.query)
      const title = ctx.query.title as string
      const subtitle = ctx.query.subtitle as string
      if (title.length < 4 || title.length > 13) {
        throw new Error('直播活动主题最少输入4字，最多可输入13个字')
      }
      if (subtitle.length < 4 || subtitle.length > 9) {
        throw new Error('直播利益点最少输入4字，最多可输入9字')
      }
      const templateId = IS_PROD ? 6216 : 4017
      const canvasBgColor =
        XiuBannerCanvasBgColorList[
          Math.floor(Math.random() * XiuBannerCanvasBgColorList.length)
        ]
      const visualMainPic =
        XiuBannerVisualMainPicList[
          Math.floor(Math.random() * XiuBannerVisualMainPicList.length)
        ]
      const req: ImgGenerateReq<XiuyuChannelBannerBizDataMap> = {
        templateId,
        imageDpi: 72,
        imageType: 'jpeg',
        bizDataMap: {
          canvasBgColor,
          visualMainPic,
          title: ctx.query.title as string,
          subtitle: ctx.query.subtitle as string,
        },
      }
      const image = await this.imageService.getImageFromMushroom(req)
      return setHttpResult(image)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/material/autogenimage/xiuyuchannelbanner', err)
      throw setHttpError(err)
    }
  }
  @Post('/autogenimage/xiuyubanner')
  // @Post('/autogenimage/xiuyuchannelbanner')
  async autoGenImageNew(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['title', 'subtitle', 'resourcePositionId'], ctx.query)
      const title = ctx.query.title as string
      const subtitle = ctx.query.subtitle as string
      const resourcePositionId = ctx.query.resourcePositionId as string
      if (title.length < 4 || title.length > 13) {
        throw new Error('直播活动主题最少输入4字，最多可输入13个字')
      }
      if (subtitle.length < 4 || subtitle.length > 9) {
        throw new Error('直播利益点最少输入4字，最多可输入9字')
      }

      const templateIds = JSON.parse(
        (await getLionKey('resource.autogenimage.templateids')) || '[]'
      ) as {
        resourcePositionId: string
        templateId: number
      }[]
      const templateId = templateIds?.filter?.(
        (t) => t.resourcePositionId === resourcePositionId
      )?.[0]?.templateId

      if (!templateId) {
        throw new Error('未找到对应的图片模板ID')
      }

      const canvasBgColor =
        XiuBannerCanvasBgColorList[
          Math.floor(Math.random() * XiuBannerCanvasBgColorList.length)
        ]
      const visualMainPic =
        XiuBannerVisualMainPicList[
          Math.floor(Math.random() * XiuBannerVisualMainPicList.length)
        ]
      const req: ImgGenerateReq<XiuyuChannelBannerBizDataMap> = {
        templateId,
        imageDpi: 72,
        imageType: 'jpeg',
        bizDataMap: {
          canvasBgColor,
          visualMainPic,
          title: ctx.query.title as string,
          subtitle: ctx.query.subtitle as string,
        },
      }
      const image = await this.imageService.getImageFromMushroom(req)
      return setHttpResult(image)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/material/autogenimage/xiuyuchannelbanner', err)
      throw setHttpError(err)
    }
  }
}
