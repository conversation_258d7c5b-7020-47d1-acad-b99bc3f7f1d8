import <PERSON><PERSON>, { <PERSON>, BaseController, <PERSON>, Get } from '@gfe/moa'
import { Like } from '@gfe/zebra-typeorm-client'
import { setHttpResult, setHttpError, isNecessaryParamsReq, getAvatar, string2Ary, logError } from '../utils'

import { ResourceService } from '../service'
import { ResourceItemCreateReqDTO, ResourceAuthReqDTO } from "../interface/resource"
import { ChangeLogTypeEnum, updateOpBizPermissionStructureRequest } from '../interface/scene'
import { ResourcePermissionService } from '../service/authorize/ResourcePermission.service'
import { ResourceDBService } from '../service/DB/ResourceDB.service'
@Controller('/api/nrp/resource')
export default class ResourceController extends BaseController {
  constructor(
    protected resourceService: ResourceService,
    protected resourcePermissionService: ResourcePermissionService,
    protected resourceDBService: ResourceDBService,
  ) {
    super()
  }
  @Get('/list')
  async getList(ctx: Moa.Context) {
    try {
      // isNecessaryParamsReq(['pageNo',], ctx.query)
      const pageNo = (ctx.query.pageNo || 1) as number
      const pageSize = (ctx.query.pageSize || 10) as number
      const keyword = ctx.query.keyword
      let sqlQuery: any = { deleted: 0 }
      if (keyword) {
        sqlQuery = [
          { name: Like(`%${keyword}%`), deleted: 0 },
          { resourcePositionId: Like(`%${keyword}%`), deleted: 0 },
        ]
      }
      // 获取sso鉴权的mis号，而非参数上的mis号
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      const res = await this.resourceService.getResourceListWithPermission(ssomis, { where: sqlQuery }, (pageNo - 1) * pageSize, pageSize)
      return setHttpResult(res)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/resource/list', err)
      throw setHttpError(err)
    }
  }

  @Post('/create')
  async create(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['resourcePositionId', 'name', 'platform', 'system', 'business', 'page',], ctx.query)
      const query = ctx.query as unknown as ResourceItemCreateReqDTO

      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      await this.resourceService.createReourcePosition(query, ssomis)
      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/resource/create', err)
      throw setHttpError(err)
    }
  }
  @Get('/query/id')
  async getResourceItem(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['resourcePositionId'], ctx.query)

      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      const hasPermission = await this.resourcePermissionService.canAccessPermission(ssomis, ctx.query.resourcePositionId as string)
      if (!hasPermission) {
        throw Error(`当前用户没有访问该资源位(key: ${ctx.query.resourcePositionId})的权限`)
      }
      const resourcePositionId = ctx.query.resourcePositionId as string
      const res = await this.resourceService.getResourceByRPIdWithTemplate(resourcePositionId)
      res.operation = {
        canAuditAll: await this.resourcePermissionService.canAprrovePermission(ssomis, resourcePositionId),
        canAccess: await this.resourcePermissionService.canAccessPermission(ssomis, resourcePositionId),

      }
      if (res.extra?.permission?.permStruct) {
        const permissionStructure = await this.resourceService.getPermissionStructure(res.id, res.extra.permission.permStruct)
        return setHttpResult({ ...res, permissionStructure })

      }
      return setHttpResult({ ...res })
    } catch (err) {
      logError(ctx, 'api:/api/nrp/resource/query/id', err)
      throw setHttpError(err)
    }
  }

  @Post('/permission/update')
  async updateOpBizPermissionStructure(ctx: Moa.Context) {
    try {
      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username
      isNecessaryParamsReq(['bizId', 'permissionStructure'], ctx.query)
      const query = ctx.query as unknown as updateOpBizPermissionStructureRequest

      const hasPermission = await this.resourcePermissionService.canManagePermission(ssomis, query.bizId as string)
      if (!hasPermission) {
        throw Error(`当前用户(mis: ${ssomis})没有管理权限`)
      }
      const resourceItem = await this.resourceDBService.findResourceRecord({ resourcePositionId: query.bizId as string })
      if (!resourceItem) {
        throw Error(`资源位(key: ${query.bizId})不存在`)
      }

      const action = await this.resourceService.updateResourcePermission(resourceItem, query.permissionStructure)
      await this.resourceService.addResourceChangeLog(ChangeLogTypeEnum.Update, resourceItem.id, -1, 'permission', ssomis, '{}', JSON.stringify(action))

      return setHttpResult(true)

    } catch (error) {
      logError(ctx, 'api:/api/nrp/resource/permission/update', error)
      throw setHttpError(error)
    }

  }

  @Post('/auth')
  async addAuth(ctx: Moa.Context) {
    try {
      isNecessaryParamsReq(['resourcePositionId', 'owners'], ctx.query)
      const query = ctx.query as unknown as ResourceAuthReqDTO

      const owners = query.owners
      const resourcePositionId = query.resourcePositionId

      const ssomis: string =
        ctx.state.initialData.userInfo &&
        ctx.state.initialData.userInfo.username

      await this.resourceService.isRPAdmin(resourcePositionId, ssomis)

      if (!string2Ary(owners).length) {
        throw Error(`至少保留一个管理员`)
      }

      await this.resourceService.addAuth(resourcePositionId, owners)
      return setHttpResult(true)
    } catch (err) {
      logError(ctx, 'api:/api/nrp/resource/auth', err)
      throw setHttpError(err)
    }
  }
}
