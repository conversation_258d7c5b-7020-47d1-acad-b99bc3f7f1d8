import Mo<PERSON>, { <PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, Get, Post } from "@gfe/moa"
import { CommonService, ResourceService, SceneService } from "../service"
import { isNecessaryParamsReq, logError, setHttpError, setHttpResult } from "../../server/utils"
import { SubmitSceneDeliveryRequest, LiveInfoExportRequest, QuerySceneDeliveryRequest, ScenePageResponseDTO } from "../interface/pegion"
import { ChangeLogTypeEnum, QuerySceneDetailRequest, QuerySceneListRequest, updateOpBizPermissionStructureRequest } from "../interface/scene"
import { ScenePermissionService } from "../service/authorize/ScenePermission.service"
import { DeliveryService } from "../service/delivery.service"

@Controller('/api/nrp/scene')
export default class SceneController extends BaseController {
    constructor(
        protected sceneService: SceneService,
        protected resourceService: ResourceService,
        protected scenePermissionService: ScenePermissionService,
        protected deliveryService: DeliveryService
    ) {
        super()
    }
    @Get('/list')
    async getScenesList(ctx: Moa.Context) {
        try {
            isNecessaryParamsReq(['pageIndex', 'limit'], ctx.query)
            const query = ctx.query as unknown as QuerySceneListRequest
            const ssomis: string =
                ctx.state.initialData.userInfo &&
                ctx.state.initialData.userInfo.username
            const res = await this.sceneService.getScenesListWithPermission(ssomis, {
                where: { deleted: 0 },
                skip: ((query.pageIndex || 1) - 1) * query.limit,
                take: query.limit,
            })
            return setHttpResult(res)

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/list', error)
            throw setHttpError(error)
        }
    }
    @Get('/id')
    async getSceneDetailById(ctx: Moa.Context) {
        try {
            isNecessaryParamsReq(['sceneId'], ctx.query)
            const ssomis: string =
                ctx.state.initialData.userInfo &&
                ctx.state.initialData.userInfo.username
            const canAccess = await this.scenePermissionService.canAccessPermission(ssomis, parseInt(ctx.query.sceneId as string))
            if (!canAccess) {
                throw new Error(`您没有该场景(id:${ctx.query.sceneId})的访问权限，请联系相关场景管理员获取。`)
            }
            const query = ctx.query as unknown as QuerySceneDetailRequest
            const scene = await this.sceneService.getSceneById(query.sceneId)
            const permissionStructure = await this.sceneService.getPermissionStructure(query.sceneId, scene.extra?.permission?.permStruct)
            return setHttpResult({
                ...scene,
                permissionStructure
            })

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/id', error)
            throw setHttpError(error)
        }
    }
    @Get('/delivery/list')
    async getSceneDeliveriesList(ctx: Moa.Context) {
        try {
            isNecessaryParamsReq(['pageIndex', 'limit'], ctx.query)
            const query = ctx.query
            let res: ScenePageResponseDTO = {
                list: [], pageInfo: {
                    currentPageNum: 0,
                    totalCount: 0,
                    totalPageCount: 0,
                    pageSize: 0
                }
            }
            if (query.id) {
                res = await this.sceneService.querySceneDelivery(ctx, {
                    id: parseInt(query.id as string),
                    pageSize: parseInt(query.limit as string),
                    pageIndex: parseInt(query.pageIndex as string)
                })
                return setHttpResult(res)
            } else {
                res = await this.sceneService.querySceneDelivery(ctx, {
                    ...query,
                    sceneId: parseInt(query.sceneId as string),
                    pageSize: parseInt(query.limit as string),
                    pageIndex: parseInt(query.pageIndex as string)
                })
            }

            return setHttpResult(res)

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/list', error)
            throw setHttpError(error)
        }
    }
    @Post('/delivery/create')
    async createSceneDelivery(ctx: Moa.Context) {
        try {
            const ssomis: string =
                ctx.state.initialData.userInfo &&
                ctx.state.initialData.userInfo.username
            // TODO 权限相关
            isNecessaryParamsReq(['sceneId', 'resourceId', 'resourceKind', 'resourceName', 'startTime', 'endTime', 'activities'], ctx.query)
            const query = ctx.query as unknown as SubmitSceneDeliveryRequest
            query.creator = ssomis

            for (let i = 0; i < query.activities.length; i++) {
                const act = query.activities[i]
                isNecessaryParamsReq(['resourcePositionId', 'resourceType', 'items'], act)
                const resource = await this.resourceService.getResourceByRPId(act.resourcePositionId)
                if (!resource) {
                    throw Error(`资源位${act.resourcePositionId}}不存在`)
                }
                if (!act.items.length) {
                    throw Error('请填写资源位投放规则')
                }
                if (resource.extra?.permission?.auditFlowTemplate) {
                    if (!act.auditFlowControlKey && resource.extra?.permission?.auditFlowTemplate?.['default']) {
                        act.auditFlowControlKey = 'default'
                    }
                    if (act.auditFlowControlKey) {
                        const approvalFlowConfig = await this.deliveryService.getDeliveryApprovalFlow(resource, act.auditFlowControlKey)
                        act.approvalFlowConfig = approvalFlowConfig
                    }

                }
                for (let itemIndex = 0; itemIndex < act.items.length; itemIndex++) {
                    const item = act.items[itemIndex]
                    isNecessaryParamsReq(['cityIds', 'startTime', 'endTime'], item)
                    if (!item.cityIds.length) {
                        throw Error('城市不能为空')
                    }
                    if (item.endTime - item.startTime < 0) {
                        throw Error('投放结束时间不能早于开始时间')
                    }
                }
            }

            await this.sceneService.createSceneDelivery(ctx, query)
            await this.sceneService.addSceneChangeLog(ChangeLogTypeEnum.Create, query.sceneId, 0, 'scene', ssomis, '{}', JSON.stringify(query))
            return setHttpResult(true)

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/delivery/create', error)
            throw setHttpError(error)
        }
    }
    @Get('/export')
    async exportLiveInfo(ctx: Moa.Context) {
        try {
            isNecessaryParamsReq(['sceneId'], ctx.query)
            const query = ctx.query as unknown as LiveInfoExportRequest

            const res = await this.sceneService.liveInfoExport(ctx, query)
            return setHttpResult({ url: res })

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/export', error)
            throw setHttpError(error)
        }
    }

    @Post('/permission/update')
    async updateOpBizPermissionStructure(ctx: Moa.Context) {
        try {
            const ssomis: string =
                ctx.state.initialData.userInfo &&
                ctx.state.initialData.userInfo.username
            isNecessaryParamsReq(['bizId', 'permissionStructure'], ctx.query)
            const query = ctx.query as unknown as updateOpBizPermissionStructureRequest

            const hasPermission = await this.scenePermissionService.canManagePermission(ssomis, query.bizId as number)
            if (!hasPermission) {
                throw Error('当前用户没有管理权限')
            }
            const action = await this.sceneService.updateScenePermission(query.bizId as number, query.permissionStructure)
            await this.sceneService.addSceneChangeLog(ChangeLogTypeEnum.Update, query.bizId as number, -1, 'permission', ssomis, '{}', JSON.stringify(action))

            return setHttpResult(true)

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/permission/update', error)
            throw setHttpError(error)
        }

    }
    @Post('/delivery/update')
    async updateSceneDelivery(ctx: Moa.Context) {
        try {
            const ssomis: string =
                ctx.state.initialData.userInfo &&
                ctx.state.initialData.userInfo.username

            isNecessaryParamsReq(['id', 'extra', 'resourceKind', 'resourceName'], ctx.query)
            const sceneActivityList = await this.sceneService.querySceneDelivery(ctx, {
                id: parseInt(ctx.query.id as string),
                pageSize: 1,
                pageIndex: 1
            })
            if (sceneActivityList.list.length === 0) {
                throw new Error("找不到场景投放记录")
            }
            const sceneActivity = sceneActivityList.list[0]
            const hasPermission = await this.scenePermissionService.canUpdatePermission(ssomis, sceneActivity.creator, sceneActivity.sceneId)
            if (!hasPermission) {
                throw Error('当前用户没有更新该投放记录信息的权限')
            }
            await this.sceneService.updateDelivery(ctx, { type: 300, ...ctx.query })
            await this.sceneService.addSceneChangeLog(ChangeLogTypeEnum.Update, sceneActivity.sceneId, sceneActivity.id, 'scene', ssomis, JSON.stringify(sceneActivity), JSON.stringify({ type: 300, ...ctx.query }))

            return setHttpResult(true)

        } catch (error) {
            logError(ctx, 'api:/api/nrp/scene/delivery/update', error)
            throw setHttpError(error)
        }
    }
}