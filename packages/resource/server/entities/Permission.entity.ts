import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from '@gfe/zebra-typeorm-client'
import { PermissionBizType, PermissionType } from '../interface/Permission'

@Entity('nrp_perm')
export class PermissionEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ name: 'perm_type', nullable: false, comment: '权限类型' })
  permType: PermissionType

  @Column({ name: 'biz_type', nullable: false, comment: '业务类型' })
  bizType: PermissionBizType

  @Column({ name: 'biz_id', nullable: false, comment: '业务ID' })
  bizId: number

  @Column({ nullable: true, comment: 'mis号' })
  mis?: string

  @Column({ name: 'org_id', nullable: false, comment: 'org ID' })
  orgId?: number

  @Column({ name: 'org_path_name', nullable: false, comment: 'org path name' })
  orgPathName?: string

  @CreateDateColumn({ comment: '创建时间' })
  createTime: Date

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime: Date

  @Column({ name: 'perm_key', nullable: true, comment: '业务key' })
  permKey: string
}
