import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
} from '@gfe/zebra-typeorm-client'

@Entity('nrp_mt_dp_citymapping')
export class CityMappingEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', comment: '自增id' })
  id: number

  @Column({ type: 'bigint', comment: '标准adcode' })
  ad_code: number

  @Column({ type: 'bigint', comment: '美团城市id' })
  mt_cityid: number

  @Column({ type: 'bigint', comment: '点评城市id' })
  dp_cityid: number

  @Column({ nullable: false, comment: '更新时间' })
  updatetime: string

  @Column({ nullable: false, comment: '更新时间' })
  _update_timestamp: string

}
