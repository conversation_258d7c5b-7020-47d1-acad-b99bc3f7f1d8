import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from '@gfe/zebra-typeorm-client'
import { ChangeLogTypeEnum } from '../interface/scene'

@Entity('nrp_common_changelog')
export class CommonChangeLog {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ nullable: false, type: 'bigint', comment: '操作类型（0-新增，1-修改，2-删除 3-查询）' })
  type: ChangeLogTypeEnum

  @Column({ nullable: false, comment: '修改前状态', type: 'json' })
  beforeStatus: string

  @Column({ nullable: false, comment: '修改后状态', type: 'json' })
  afterStatus: string

  @Column({ nullable: false, comment: '操作人' })
  operator: string

  @CreateDateColumn({ comment: '创建时间' })
  createTime: Date

  @Column({ nullable: false, comment: '额外识别信息', type: 'json' })
  extra: string

}
