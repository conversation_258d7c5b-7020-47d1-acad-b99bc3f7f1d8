import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn
} from '@gfe/zebra-typeorm-client'

@Entity('nrp_delivery_changelog')
export class DeliveryChangeLogEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ nullable: false, type: 'bigint', comment: '操作类型（0-新增，1-修改，2-删除）' })
  type: number

  @Column({ nullable: false, comment: '资源位标识' })
  resourcePositionId: string

  @Column({ nullable: false, type: 'bigint', comment: '投放记录Id' })
  deliveryId: number

  @Column({ nullable: false, comment: '修改前状态', type: 'json' })
  beforeStatus: string

  @Column({ nullable: false, comment: '修改后状态', type: 'json' })
  afterStatus: string

  @Column({ nullable: false, comment: '操作人' })
  operator: string

  @CreateDateColumn({ comment: '创建时间' })
  createTime: Date

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime: Date

}
