import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn
} from '@gfe/zebra-typeorm-client'
import {
  PlatformEnum, PageEnum, SystemEnum, BusinessEnum, Boolean2NumEnum, ResourceExtra
} from "../interface/resource"
import { ResourceTemplateEntity } from './resourceTemplate.entity'

@Entity('nrp_resource')
export class ResourceEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ nullable: false, comment: '资源位标识' })
  resourcePositionId: string

  @Column({ nullable: false, comment: '创建人' })
  creator: string

  @Column({ nullable: false, comment: '资源位名称' })
  name: string

  @Column({ nullable: false, comment: '管理员', type: 'text' })
  owners: string

  @Column({ nullable: false, comment: '平台，美团/点评', })
  platform: PlatformEnum

  @Column({ nullable: false, comment: '端，app/h5/wxxcx', })
  system: SystemEnum

  @Column({ nullable: false, comment: '业务线', })
  business: BusinessEnum

  @Column({ nullable: false, comment: '页面', })
  page: PageEnum

  @Column({ nullable: false, comment: '示意图链接', })
  imgUrl: string

  @Column({ nullable: false, comment: '支持同时绑定多个投放规则', })
  multiDelivery: Boolean2NumEnum

  @Column({ nullable: false, comment: '是否删除', })
  deleted: Boolean2NumEnum

  @Column({ nullable: false, comment: '资源位type', })
  type: number

  @Column({ nullable: false, comment: '物料模型json', type: 'json' })
  materialModel?: string

  @Column({ nullable: false, comment: 'dsl描述json', type: 'json' })
  dslInfo?: string

  @Column({ nullable: false, comment: '额外信息', type: 'json' })
  extra?: ResourceExtra

  @Column({ nullable: false, type: 'bigint', comment: '最小时间间隔', })
  timeInterval: number

  @CreateDateColumn({ comment: '创建时间' })
  createTime: Date

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime: Date

  @Column({ nullable: true, type: 'bigint', comment: '模版id', })
  templateId: number

  template: ResourceTemplateEntity
}
