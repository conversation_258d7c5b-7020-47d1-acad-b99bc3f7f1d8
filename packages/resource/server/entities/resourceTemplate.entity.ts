import {
  <PERSON>ti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from '@gfe/zebra-typeorm-client'
import { TemplateInfoModel } from '../interface/template'

@Entity('nrp_template')
export class ResourceTemplateEntity {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ nullable: false, comment: '模版名称' })
  name: string

  @Column({ nullable: false, comment: '管理员mis', type: 'text' })
  creator: string

  @Column({ nullable: false, comment: '额外信息', type: 'json' })
  extra?: string

  @CreateDateColumn({ comment: '创建时间' })
  create_time: Date

  @UpdateDateColumn({ comment: '更新时间' })
  update_time: Date

  @Column({ nullable: false, comment: '模版信息', type: 'json' })
  template_info: TemplateInfoModel
}
