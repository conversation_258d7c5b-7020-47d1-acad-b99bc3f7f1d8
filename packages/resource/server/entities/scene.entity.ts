import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
} from '@gfe/zebra-typeorm-client'
import { SceneExtra } from '../interface/scene'

@Entity('nrp_resource_scene')
export class SceneEntity {
    @PrimaryGeneratedColumn({ type: 'bigint', comment: '自增id' })
    id: number

    @Column({ comment: '创建人' })
    creator: string

    @Column({ comment: '场景名称' })
    name: string

    @Column({ comment: '行业' })
    business: string

    @Column({ comment: '创建时间' })
    createTime: number

    @Column({ comment: '更新时间' })
    updatetime: string

    @Column({ comment: '是否删除' })
    deleted: string

    @Column({ type: 'json', comment: '额外信息' })
    extra: SceneExtra

    @Column({ type: 'json', comment: '场景信息' })
    templateInfo: string
}
