import { PermissionEntity } from "../entities"

export enum PermissionType {
  Manage = 100,
  Approve = 200,
  Access = 300,
}

export enum PermissionBizType {
  Resource = 100,
  Scene = 200,
}


export interface PermissionAddAction {
  action: 'add',
  perm_type: PermissionType,
  payload: string | {
    id: number
    path: string
  }
  permKey: string
}

export interface PermissionDeleteAction {
  action: 'delete',
  payload: number
}

export interface PermissionStructureModel {
  [permKey: string]: PermissionEntity[]


}

export type PermissionAction = PermissionAddAction | PermissionDeleteAction