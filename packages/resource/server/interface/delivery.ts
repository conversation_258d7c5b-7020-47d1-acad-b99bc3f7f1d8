
import { DeliveryActivityDTO, DeliveryItemDTO, QueryByActivityRequest } from './pegion'
import { OpResourceTypeEnum } from "./resource"

export interface UpdateDeliveryRuleReq {
  id: number
  cityIds?: number[]
  startTime?: number
  endTime?: number
  priority?: number
  extra?: string
}
export interface UpdateDeliveryReq {
  id: number
  materialInfo?: string
  extra?: string
}

export interface DeliveryActivityReq extends QueryByActivityRequest {
  id?: number   // 投放记录ID
}

export interface DeliveryActivityRes {
  total: number
  list: DeliveryActivityItem[]
}



export interface DeliveryActivityItem extends DeliveryActivityDTO {
  styleType: DeliveryItemStyleTypeEnum // 1 平铺；2 数组展开
  statusInfo: string // 投放记录数组状态描述，x条审核通过，x条待审核
}



export interface DeliveryRuleItem extends DeliveryItemDTO {
  operator: OperatorStatus
}

/**
 * 投放记录可流转状态
 */
export interface OperatorStatus {
  canEdit: boolean
  canOffline: boolean
  canDenyAudit: boolean
  canAudit: boolean
}

export enum DeliveryItemStyleTypeEnum {
  Single = 1, // 一次投放记录只支持单个投放规则
  Multi = 2 // 一次投放记录支持多个投放规则
}

export enum DeliveryItemStatusEnum {
  PendingAudit = 100, // 待审核
  Audited = 200, // 已审核
  Rejected = 300, // 已驳回
  Offline = 400, // 已删除
}

/**
 * 直播大卡
 * 直播广场状态枚举
 */
export enum LiveDeliverySquareStatusEnum {
  LivePreview = 1, // 直播预告
  Living = 2, // 直播中
  LivePlayBack = 3, // 直播回放
}
/**
 * 直播大卡
 * 直播入口状态枚举
 */
export enum LiveDeliveryEntranceStatusEnum {
  LivePreview = 1, // 直播预告
  Living = 2, // 直播中
}


export interface SubmitDeliveryReq {
  resourceType: OpResourceTypeEnum // 资源类型 直播：100
  resourcePositionId: string // 资源位id
  materialInfo?: string // 物料信息
  extra?: string // 额外信息
  creator?: string// 创建人
  approvalFlowConfig?: SubmitApprovalFlowConfigModel
  deliveryItems: DeliveryRuleItem[] // 投放记录
  auditFlowControlKey?: string
}

export interface SubmitDeliveryPegionReq {
  deliveryActivities: SubmitDeliveryReq[]
}

export enum ScheduleShowTypeEnum {
  Overlap = 1, // 重叠
  Separate = 2 // 分开
}
export interface ScheduleRes {
  type?: ScheduleShowTypeEnum
  startTime: number
  endTime: number
  timeInterval: number
  items: ScheduleCityItem[]
}

export interface ScheduleCityMap {
  [propName: string]: {
    name: string
    cityId: number
    schedule: ScheduleTimeItem[]
    contained?: boolean
    hasConflict?: boolean
  }
}

export interface ScheduleItem {
  name: string
  id: number
  activityId: number
  extra: string
  status: DeliveryItemStatusEnum
  key?: string
  startTime?: number
  endTime?: number
  conflict?: boolean
}

export interface ScheduleTimeItem {
  // name: string
  // cityId: number
  key: string
  children: ScheduleItem[]
  contained?: boolean
  hasConflict?: boolean
  startTime: number
  endTime: number
}
export interface ScheduleCityItem {
  name: string //城市名称
  cityId: number
  // mtCityId: number
  timeInterval: number //时间间隔
  children: ScheduleTimeItem[] | ScheduleItem[][] | ScheduleItem[]
  // contained?: boolean
  // hasConflict?: boolean
}
export interface ScheduleListItemRes {
  timeInterval: number // //时间间隔
  list: ScheduleCityItem[]
}


export interface SubmitApprovalFlowConfigModel {
  candidateApprover: Array<Array<string>> // 每一层审批人信息
  levelCnt: number // 审批级数
}