export interface LiveCardMaterialDTO {
  liveId: number // 直播id
  liveEntryStatusList: number[] // 频道入口直播状态限制，逗号分隔
  liveSquareStatusList: number[] //  直播广场直播状态限制
}

export interface ImgGenerateReq<T> {
  /**
   * 蘑菇模版
   */
  templateId: number

  /**
   * 分辨率，默认32
   */
  imageDpi?: number

  /**
   * 图片格式，默认jpeg
   */
  imageType?: string

  /**
   * 模版标签数据
   */
  bizDataMap: T
}

export interface ImgGenerateRes {
  code: number
  message: string
  content: string
}

export interface Color {
  pst: number
  color: string
}

export interface CanvasBgColor {
  type: string
  colors: { pst: number; color: string }[]
  angle: number
}

export interface XiuyuChannelBannerBizDataMap {
  canvasBgColor: CanvasBgColor
  subtitle: string
  title: string
  visualMainPic: string
}
