import { DeliveryItemStatusEnum, DeliveryItemStyleTypeEnum, SubmitApprovalFlowConfigModel } from './delivery'
import { OpResourceTypeEnum } from './resource'


/**
 * piegon 接口
 * method:   queryDeliveryInfo
 * request:  QueryDeliveryInfoRequest
 * response: QueryDeliveryInfoResponse
 */
export interface QueryDeliveryInfoRequest {
  type?: number
  ids: number[]
}
export interface QueryDeliveryInfoResponse {
  code: number
  msg: string
  deliveryActivities: DeliveryActivityDTO[]
}

/**
 * piegon 接口
 * method:   updateItemStatus
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.UpdateItemStatusRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.UpdateItemStatusResponse
 */
export interface UpdateItemStatusRequest {
  status?: DeliveryItemStatusEnum // 变更状态
  action?: DeliveryItemActionEnum
  approvalInfos: ApprovalInfoDTO[] //待修改的记录id,
  approver: string // 审核人
  usePriority?: boolean // 是否使用了优先级概念，启动优先级概念后无需校验冲突
}
export enum DeliveryItemActionEnum {
  Approve = 1, // 通过
  Reject = 2, // 驳回
  Reset = 3, // 重置审批流
  Offline = 4 //下线
}
export interface UpdateItemStatusResponse {
  code: number
  msg: string
  errIds: number[]  // 无法变更状态的记录id
}

/**
 * piegon 接口
 * method:   submitDelivery
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.SubmitDeliveryRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.SubmitDeliveryResponse
 */
export interface SubmitDeliveryRequest {
  deliveryActivities: DeliveryActivityDTO[] // 变更状态
}
export interface SubmitDeliveryResponse {
  code: number
  msg: string
}


/**
 * piegon 接口
 * method:   queryByActivity
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.QueryByActivityRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.QueryByActivityResponse
 */
export interface QueryByActivityRequest {
  creator?: string // 创建人 mis
  ids: number[] | string | string[]
  resourcePositionId?: string //
  startCtime?: string // 活动创建开始时间    默认add_time在最近半年
  endCtime?: string // 活动创建结束时间（毫秒时间戳）默认add_time在最近半年
  statusFilter?: DeliveryItemStatusEnum[] // 记录状态过滤，不传查所有状态
  pageIndex: number // 页码
  pageSize: number // 分页大小
}
export interface QueryByActivityResponse {
  code: number
  msg: string
  data: PageResponseDTO
}


export interface PageResponseDTO {
  pageInfo: PageInfoDTO
  list: DeliveryActivityDTO[]
}

export interface PageInfoDTO {
  currentPageNum: number // 当前页
  pageSize: number // // 每页数量
  totalPageCount: number // 总页数
  totalCount: number // 总数量
}



/**
 * piegon 接口
 * method:   queryByItem
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.QueryByItemRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.QueryByItemResponse
 */
export interface QueryByItemRequest {
  resourcePositionId: string //
  cityIds?: number[]
  startTime?: string // 活动创建开始时间    默认add_time在最近半年
  endTime?: string // 活动创建结束时间（毫秒时间戳）默认add_time在最近半年
  statusFilter?: DeliveryItemStatusEnum[] // 记录状态过滤，不传查所有状态
}
export interface QueryByItemResponse {
  code: number
  msg: string
  deliveryActivities: DeliveryActivityDTO[] // 变更状态
}

/**
 * piegon 接口
 * method:   updateDelivery
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.UpdateDeliveryRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.UpdateDeliveryResponse
 */
export interface UpdateDeliveryRequest {
  type?: number
  id: number
  materialInfo?: string
  cityIds?: number[]
  startTime?: number
  endTime?: number
  extra?: string
}
export interface UpdateDeliveryResponse {
  code: number
  msg: string
}

export interface DeliveryActivityDTO {
  id: number // 活动id
  ctime: string // 创建时间
  utime: string // 更新时间

  creator: string// 创建人
  resourceType: OpResourceTypeEnum // 资源类型 直播：100
  resourcePositionId: string // 资源位id
  extra: string // 额外信息
  statusInfo: string // 投放记录数组状态描述，x条审核通过，x条待审核
  deliveryItems: DeliveryItemDTO[]  // 投放记录

  styleType: DeliveryItemStyleTypeEnum // 1 平铺；2 数组展开
  materialInfo: string // 物料信息
  editable?: boolean
}


export interface DeliveryItemDTO {
  id: number //记录id
  activityId: number // 归属活动id
  resourcePositionId: string //资源位id
  cityIds: number[] // 城市
  mtCityIds: number[] // 美团城市ID
  startTime: number //投放开始时间
  endTime: number //投放结束时间
  creator: string //创建人
  ctime: string //创建时间
  utime: string //更新时间
  extra: string // 额外信息
  status: DeliveryItemStatusEnum // 状态   记录状态变更可传 待审核：100， 已审核：200，已驳回：300，已删除：400
  priority: number // 优先级
  approvalFlow?: ApprovalFlowNode[]
}
export interface ApprovalFlowNode {
  id: number
  itemId: number
  candidateApprover: string[]
  actualApprover: string
  approveTime: number
  ctime: number
  utime: number
  status: number
  level: number
}
export enum ApprovalFlowNodeStatus {
  Pending = 100,
  Approved = 300,
  Auditing = 200,  // 待当前用户审批
  Rejected = 400
}
/**
 * * piegon 接口
 * method:   submitSceneDelivery
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.SubmitSceneDeliveryRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.SubmitSceneDeliveryResponse
 */



export interface SubmitSceneDeliveryRequest {
  sceneId: number
  creator: string
  resourceId: number
  resourceKind: ResourceKindEnum
  resourceName: string
  startTime: number
  endTime: number
  extra: string
  activities: Array<SubmitActivityDTO>
}

export interface SubmitActivityDTO {
  resourcePositionId: string
  materialInfo: string
  resourceType: OpResourceTypeEnum
  auditFlowControlKey: string
  extra: string
  approvalFlowConfig: SubmitApprovalFlowConfigModel
  items: Array<SubmitItemDTO>
}
export interface SubmitItemDTO {
  cityIds: Array<number>
  startTime: number
  endTime: number
  extra: string
}
export interface SubmitSceneDeliveryResponse {
  code: number
  msg: string
}

export enum ResourceKindEnum {
  LIVE_GUANBO_PLATFORM = 1101, // 官播/平台
  LIVE_GUANBO_CITY = 1102, // 官播/城市
  LIVE_GUANBO_ZHONGFA = 1103, //  官播/中发
  LIVE_GUANBO_OTHER = 1104, // 官播/其他
  LIVE_DIANBO_KA = 1201, // 店播/KA
  LIVE_DIANBO_CKA = 1202, // 店播/CKA
  LIVE_DIANBO_SMB = 1203, // 店播/SMB
  LIVE_DABO_DABO = 1301, // 达播/达播
}


/**
 * 
 * piegon 接口
 * method:   querySceneDelivery
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.QuerySceneDeliveryRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.QuerySceneDeliveryResponse
 */
export interface QuerySceneDeliveryRequest {
  id?: number
  sceneId?: number
  creator?: string
  resourceId?: number
  resourceKind?: ResourceKindEnum
  resourceName?: string
  startTime?: number
  endTime?: number
  statusFilter?: [number]
  pageIndex?: number
  pageSize?: number
}
export interface QuerySceneDeliveryResponse {
  code: number
  msg: string
  data: ScenePageResponseDTO

}
export interface ScenePageResponseDTO {
  pageInfo: PageInfoDTO
  list: DeliverySceneActivityDTO[]
}
export interface DeliverySceneActivityDTO {
  id: number
  sceneId: number
  creator: string
  resourceId: number
  resourceKind: ResourceKindEnum
  resourceName: string
  startTime: number
  endTime: number
  ctime: number
  utime: number
  extra: string
  operation: {
    canEdit: boolean
  }
  deliveryActivities: Array<DeliveryActivityDTO>
}


/**
 * 
 * piegon 接口
 * method:   liveInfoExport
 * request:  com.sankuai.dzviewscene.delivery.api.request.manage.LiveInfoExportRequest
 * response: com.sankuai.dzviewscene.delivery.api.response.manage.LiveInfoExportResponse
 */

export interface LiveInfoExportRequest {
  id?: number
  sceneId: number
  creator?: string
  resourceId?: number
  resourceKind?: ResourceKindEnum
  resourceName?: string
  startTime?: number
  endTime?: number
}

export interface LiveInfoExportResponse {
  code: number
  msg: string
  url: string
}