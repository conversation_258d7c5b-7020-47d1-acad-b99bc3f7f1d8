import { TemplateInfoDTO } from "./template"

export interface ResourceRes {
  list: ResourceItemDTO[]
  total: number
}

export interface UserItem {
  mis: string,
  avatar: string
}
export interface ResourceItemDTO {
  id: number // 资源位 id
  name: string //名称
  resourcePositionId: string //资源位标识
  creator: string //创建者
  owners: UserItem[] //管理员， 逗号分隔
  platform: PlatformEnum
  system: SystemEnum
  business: BusinessEnum
  page: PageEnum
  imgUrl: string // 示意图
  timeInterval: number //最小时间间隔，ms
  createTime: Date //创建时间
  updateTime: Date //更新时间
  multiDelivery?: Boolean2NumEnum // 改资源位是否支持同时提交多个投放规则
  materialModel?: string //物料模型json
  dslInfo?: string //dsl描述json
  extra?: ResourceExtra
  templateId?: number
  operation?: ResourceOperation
}

export interface ResourceOperation {
  canAuditAll: boolean // 能否一键审批
  canAccess: boolean // 能否新增提报
}
export interface ResourceItemDetailDTO extends ResourceItemDTO {
  templateInfo?: TemplateInfoDTO
}
export interface ResourceItemCreateReqDTO {
  resourcePositionId: string //资源位标识
  name: string //名称
  owners?: string //管理员， 逗号分隔
  platform: PlatformEnum
  system: SystemEnum
  business: BusinessEnum
  page: PageEnum
  imgUrl?: string // 示意图
  multiDelivery?: Boolean2NumEnum // 资源位是否支持同时提交多个投放规则
  timeInterval?: number //最小时间间隔，ms 默认 30*60*1000
  materialModel?: string //物料模型json
  dslInfo?: string //dsl描述json
  extra?: string
}

export interface ResourceAuthReqDTO {
  resourcePositionId: string //资源位标识
  owners: string //管理员， 逗号分隔
}
/**
 * 平台
 */
export enum PlatformEnum {
  MT = 'mt',
  DP = 'dp',
}

/**
 * 投放的端
 */
export enum SystemEnum {
  APP = 'app', // app
  H5 = 'h5', // h5
  WXXCX = 'wxxcx' // 微信小程序
}

/**
 * 业务线
 */
export enum BusinessEnum {
  Play = 'play', // 休娱
  Beauty = 'beauty', // 丽人
}

/**
 * 页面
 */
export enum PageEnum {
  Channel = 'channel', // 频道页
  POI = 'poi', // 商户详情页
}



/**
 * 资源位变更记录落库
 */
export enum ResourceChangeLogTypeEnum {
  ADD = 0,
  UPDATE = 1,
  DELETE = 2,
}

/**
 * boolean 2 number
 */

export enum Boolean2NumEnum {
  TRUE = 1,
  FALSE = 0
}

/**
 * boolean 2 number
 */

export enum AuthEnum {
  ADMIN = 'admin',
  EDIT = 'edit',
  NONE = 'none'
}

export enum OpResourceTypeEnum {
  LiveCard = 100, // 直播大卡
  CrossChannel = 200, // Cross渠道
  MidBanner = 300 // 中通
}

export interface ResourceExtra {
  previewUrl?: string
  jumpUrl?: string // 投放直播间链接
  jumpUrlQuery?: string[]
  template: {
    [key: string]: any
  }
  permission: {
    permStruct: PermStructure
    auditFlowTemplate: AuditFlowModel
  }
}
export interface AuditFlowModel {
  [key: string]: AuditFlowNodeModel[] // key用于业务字段控制走不同的审批流
}
export interface AuditFlowNodeModel {
  type: AuditFlowNodeType
  userRole?: string // AuditFlowNodeType.PermStruct使用 权限结构里的permKey
  xLevel?: number // 代表该用户的第N级上级
}

export enum AuditFlowNodeType {
  PermStruct = 0, // 代表该节点的结构可以从permStruct中找
  XN = 1, // 代表该用户的第N级上级
}
export interface PermStructure {
  [key: string]: PermStructureModel // key为permKey
}

export interface PermStructureModel {
  label: string // 	前端展示名称
  permType: number // 权限id
  isUse: boolean // 当前资源位是否使用该
  permKey: string // 权限在业务下的别名，例如一级审核 二级审核
  type: PermStructureType
}

export enum PermStructureType {
  ORG = 'org',
  MIS = 'mis'
}