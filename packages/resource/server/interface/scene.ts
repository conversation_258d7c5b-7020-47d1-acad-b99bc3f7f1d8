import { PermStructure } from "./resource"

export interface QuerySceneDetailRequest {
    sceneId: number
}
export interface QuerySceneListRequest {
    pageIndex: number
    limit: number
    sceneId?: number
}

export interface updateOpBizPermissionStructureRequest {
    permissionStructure: PermissionStructureDTO
    bizId: number | string
}

export interface PermissionStructureDTO {
    [permKey: string]: Array<string | {
        id: number
        path: string
    }>
}

export interface SceneExtra {
    template: {
        [key: string]: any
    }
    permission: {
        permStruct: PermStructure
    }
}

export enum ChangeLogTypeEnum {
    Create = 0,
    Update = 1,
    Delete = 2,
    Read = 3
}