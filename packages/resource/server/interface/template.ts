export interface TemplateInfoItemModel {
  renderType: TemplateRenderTypeEnum;
  dslJson: string; // renderType为TEMPLATE_JSON时使用，用于定义表单的json，其他时候为'{}',
  staticTemplateFormKey: string; // renderType为 TEMPLATE_STATIC时使用，与前端对应的模版组件标识一样, 其他时候 ''
  staticTemplateTableKey: string; // renderType为 TEMPLATE_STATIC时使用，与前端对应的模版组件标识一样, 其他时候 ''
}

export enum TemplateRenderTypeEnum {
  DIRECT = 'direct', // 直播大卡，直接写死的类型
  TEMPLATE_STATIC = 'template_static', //模版资源在前端，靠id索引组件进行渲染
  TEMPLATE_JSON = 'template_json', // 下发json动态渲染
}

export interface TemplateInfoModel {
  tableTemplateInfo: TemplateInfoItemModel
  formTemplateInfo: TemplateInfoItemModel;
}

export type TemplateInfoDTO = TemplateInfoModel
