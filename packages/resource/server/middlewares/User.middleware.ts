import Moa, { Middleware, BaseMiddleware } from '@gfe/moa'
import { IS_LOCAL } from '../config/env'

@Middleware()
export class UserMiddleware implements BaseMiddleware {
  async use(ctx: Moa.Context, next) {
    ctx.state.initialData = {}
    if (IS_LOCAL) {
      ctx.state.initialData.userInfo = {
        username: 'chenkai<PERSON>03',
        nickname: '陈凯明',
      }
      return next()
    }
    if (ctx.userInfo && !ctx.userInfo.message && !ctx.userInfo.isExpired) {
      ctx.state.initialData.userInfo = {
        username: ctx.userInfo.loginName || ctx.userInfo.login,
        nickname: ctx.userInfo.name,
      }
    }
    return next()
  }
}
