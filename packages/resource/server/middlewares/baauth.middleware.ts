import Moa, { Middleware, BaseMiddleware } from '@gfe/moa'
import { Context } from 'koa'
import { getLion<PERSON>ey } from '../utils/lion'
import crypto from 'crypto'

function verify(params, authClient) {
  if (!authClient || !authClient.length) {
    return false
  }
  let auth = false

  const { authorization, method, path, date } = params

  if (authorization) {
    auth = authClient.some((item) => {
      const stringToSign = method + ' ' + path + '\n' + date
      const signature = crypto
        .createHmac('sha1', item.secret)
        .update(stringToSign, 'utf8')
        .digest('base64')

      console.log(`MWS ${item.name}:${signature}`)
      if (`MWS ${item.name}:${signature}` === authorization) {
        return true
      }
    })
  }

  return auth
}

@Middleware({
  exclude: [],
})
export class BasicAuthMiddleware implements BaseMiddleware {
  async use(ctx: Context | Moa.Context, next) {
    if (!ctx.path.startsWith('/api/nrp/ba/')) {
      return next()
    }
    const clients = (await getLion<PERSON>ey('bas', '')).split('|').map((item) => {
      const pieces = item.split(',')
      return {
        name: pieces[0],
        secret: pieces[1],
      }
    })

    // const clients = 'nrp_livecard,@RfE9qpB4!E5Rb%u'.split('|').map((item) => {
    //   const pieces = item.split(',');
    //   return {
    //     name: pieces[0],
    //     secret: pieces[1],
    //   };
    // });

    const result = verify(
      {
        authorization: ctx.get('authorization'),
        date: ctx.get('date'),
        method: ctx.method.toUpperCase(),
        path: ctx.path,
      },
      clients
    )

    if (!result) {
      ctx.status = 403
      return
    }

    return next()
  }
}
