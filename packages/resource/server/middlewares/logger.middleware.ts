import Moa, { Middleware, BaseMiddleware } from '@gfe/moa'

@Middleware()
export class LoggerMiddleware implements BaseMiddleware {
  async use(ctx: Moa.Context, next) {

    const path = ctx.path
    const query = ctx.query
    await next()
    const res = ctx.response.body

    console.log(`__traceid__:${ctx?.header?.['m-traceid']};__path__:${JSON.stringify(path)};req:${JSON.stringify(query)};res:${JSON.stringify(res)};`)

  }
}
