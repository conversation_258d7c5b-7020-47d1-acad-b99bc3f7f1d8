import Moa, { Middleware, BaseMiddleware } from '@gfe/moa'
import { getLion<PERSON>ey } from '../utils/lion'

@Middleware({
  exclude: [],
})
export class SecurityMiddleware implements BaseMiddleware {
  async use(ctx: Moa.Context, next) {
    const ua = ctx.get('user-agent')
    if (ua && ua.indexOf('mtdp-infosec/scan') > -1) {
      ctx.status = 403
      return
    }
    if ((await getLionKey('resource.slience')) === 'true' && ctx.method.toLocaleLowerCase() !== 'get') {
      ctx.status = 503
      ctx.body = {
        code: 503,
        msg: '系统服务暂时不可用，请刷新后重试或咨询系统负责人。'
      }
      return
    }
    return next()
  }
}
