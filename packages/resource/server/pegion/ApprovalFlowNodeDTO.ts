'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class ApprovalFlowNodeDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.ApprovalFlowNodeDTO'
    }
}

ApprovalFlowNodeDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.ApprovalFlowNodeDTO'

ApprovalFlowNodeDTO.fields = {
    id: 'Long',
    itemId: 'Long',
    candidateApprover: '[string]',
    actualApprover: 'string',
    approveTime: 'Long',
    ctime: 'Long',
    utime: 'Long',
    status: 'Integer',
    level: 'Integer'
}

module.exports = ApprovalFlowNodeDTO