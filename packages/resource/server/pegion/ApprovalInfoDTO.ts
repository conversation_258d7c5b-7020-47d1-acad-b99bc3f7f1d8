'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class ApprovalInfoDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.ApprovalInfoDTO'
    }
}

ApprovalInfoDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.ApprovalInfoDTO'

ApprovalInfoDTO.fields = {
    itemId: 'Long',
    level: 'Integer'
}

module.exports = ApprovalInfoDTO