'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class DeliveryActivityDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.DeliveryActivityDTO'
    }
}

DeliveryActivityDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.DeliveryActivityDTO'

DeliveryActivityDTO.fields = {
    id: 'Long',
    resourcePositionId: 'string',
    creator: 'string',
    ctime: 'Long',
    utime: 'Long',
    materialInfo: 'string',
    resourceType: 'Integer',
    sceneActivityId: 'Long',
    extra: 'string',
    deliveryItems: '[object]'  // object为com.sankuai.dzviewscene.delivery.api.dto.DeliveryItemDTO的实例;
}

module.exports = DeliveryActivityDTO