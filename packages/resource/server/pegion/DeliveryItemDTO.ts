'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class DeliveryItemDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.DeliveryItemDTO'
    }
}

DeliveryItemDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.DeliveryItemDTO'

DeliveryItemDTO.fields = {
    id: 'Long',
    activityId: 'Long',
    resourcePositionId: 'string',
    cityIds: '[Integer]',
    startTime: 'Long',
    endTime: 'Long',
    creator: 'string',
    ctime: 'Long',
    utime: 'Long',
    extra: 'string',
    status: 'Integer',
    approver: 'string',
    approvalFlow: '[object]',  // object为com.sankuai.dzviewscene.delivery.api.dto.ApprovalFlowNodeDTO的实例;
    priority: 'Integer'
}

module.exports = DeliveryItemDTO