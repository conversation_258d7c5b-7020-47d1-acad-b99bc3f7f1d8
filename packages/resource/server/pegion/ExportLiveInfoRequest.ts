'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class ExportLiveInfoRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.ExportLiveInfoRequest'
    }
}

ExportLiveInfoRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.ExportLiveInfoRequest'

ExportLiveInfoRequest.fields = {
    id: 'Long',
    sceneId: 'Long',
    creator: 'string',
    resourceId: 'Long',
    resourceKind: 'Integer',
    resourceName: 'string',
    startTime: 'Long',
    endTime: 'Long',
    statusFilter: '[Integer]'
}

module.exports = ExportLiveInfoRequest