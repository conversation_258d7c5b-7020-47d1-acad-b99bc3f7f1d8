'use strict';

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class QueryByActivityRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryByActivityRequest'
    }
}

QueryByActivityRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryByActivityRequest'

QueryByActivityRequest.fields = {
    resourcePositionId: 'string',
    creator: 'string',
    startCtime: 'Long',
    endCtime: 'Long',
    statusFilter: '[Integer]',
    ids: '[Long]',
    pageIndex: 'int',
    pageSize: 'int',
    activityType: 'string',
    activityName: 'string',
    approver: 'string',
    approveLevel: 'string',
    cityId: 'int',
    orderBy: 'string',
    orderDir: 'string',
    deployStartCtime: 'Long',
    deployEndCtime: 'Long'
}

module.exports = QueryByActivityRequest