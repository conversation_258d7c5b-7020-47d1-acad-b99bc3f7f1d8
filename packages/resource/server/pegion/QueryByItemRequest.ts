'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class QueryByItemRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryByItemRequest'
    }
}

QueryByItemRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryByItemRequest'

QueryByItemRequest.fields = {
    resourcePositionId:'string',
    cityIds:'[Integer]',
    startTime:'Long',
    endTime:'Long',
    statusFilter:'[Integer]'
}

module.exports = QueryByItemRequest
