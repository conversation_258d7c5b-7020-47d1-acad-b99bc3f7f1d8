'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class QueryDeliveryInfoRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryDeliveryInfoRequest'
    }
}

QueryDeliveryInfoRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.QueryDeliveryInfoRequest'

QueryDeliveryInfoRequest.fields = {
    type:'Integer',
    ids:'[Long]'
}

module.exports = QueryDeliveryInfoRequest
