'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class QuerySceneDeliveryRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.QuerySceneDeliveryRequest'
    }
}

QuerySceneDeliveryRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.QuerySceneDeliveryRequest'

QuerySceneDeliveryRequest.fields = {
    id: 'Long',
    sceneId: 'Long',
    creator: 'string',
    resourceId: 'Long',
    resourceKind: 'Integer',
    resourceName: 'string',
    startTime: 'Long',
    endTime: 'Long',
    statusFilter: '[Integer]',
    pageIndex: 'int',
    pageSize: 'int'
}

module.exports = QuerySceneDeliveryRequest