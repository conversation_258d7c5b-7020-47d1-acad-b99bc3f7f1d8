'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base
class SubmitActivityDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitActivityDTO'
    }
}

SubmitActivityDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitActivityDTO'

SubmitActivityDTO.fields = {
    resourcePositionId: 'string',
    creator: 'string',
    materialInfo: 'string',
    resourceType: 'Integer',
    extra: 'string',
    approvalFlowConfig: 'object',  // object为com.sankuai.dzviewscene.delivery.api.dto.SubmitApprovalFlowConfigDTO的实例;
    items: '[object]'  // object为com.sankuai.dzviewscene.delivery.api.dto.SubmitItemDTO的实例;
}

module.exports = SubmitActivityDTO