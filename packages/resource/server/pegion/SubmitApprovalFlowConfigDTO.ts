'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class SubmitApprovalFlowConfigDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitApprovalFlowConfigDTO'
    }
}

SubmitApprovalFlowConfigDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitApprovalFlowConfigDTO'

SubmitApprovalFlowConfigDTO.fields = {
    candidateApprover: '[[string]]',  // 存在嵌套,注意查看源代码;
    levelCnt: 'Integer'
}

module.exports = SubmitApprovalFlowConfigDTO