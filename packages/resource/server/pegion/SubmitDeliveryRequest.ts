'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class SubmitDeliveryRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.SubmitDeliveryRequest'
    }
}

SubmitDeliveryRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.SubmitDeliveryRequest'

SubmitDeliveryRequest.fields = {
    deliveryActivities: '[object]',  // object为com.sankuai.dzviewscene.delivery.api.dto.DeliveryActivityDTO的实例;
    activities: '[object]'  // object为com.sankuai.dzviewscene.delivery.api.dto.SubmitActivityDTO的实例;
}

module.exports = SubmitDeliveryRequest