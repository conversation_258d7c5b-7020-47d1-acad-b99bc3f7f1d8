'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class SubmitItemDTO extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitItemDTO'
    }
}

SubmitItemDTO.className = 'com.sankuai.dzviewscene.delivery.api.dto.SubmitItemDTO'

SubmitItemDTO.fields = {
    cityIds: '[Integer]',
    startTime: 'Long',
    endTime: 'Long',
    priority: 'Integer',
    extra: 'string'
}

module.exports = SubmitItemDTO