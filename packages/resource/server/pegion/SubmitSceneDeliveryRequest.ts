'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class SubmitSceneDeliveryRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.SubmitSceneDeliveryRequest'
    }
}

SubmitSceneDeliveryRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.SubmitSceneDeliveryRequest'

SubmitSceneDeliveryRequest.fields = {
    sceneId: 'Long',
    creator: 'string',
    resourceId: 'Long',
    resourceKind: 'Integer',
    resourceName: 'string',
    startTime: 'Long',
    endTime: 'Long',
    extra: 'string',
    activities: '[object]'  // object为com.sankuai.dzviewscene.delivery.api.dto.SubmitActivityDTO的实例;
}

module.exports = SubmitSceneDeliveryRequest