'use strict';

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class UpdateDeliveryRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.UpdateDeliveryRequest'
    }
}

UpdateDeliveryRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.UpdateDeliveryRequest'

UpdateDeliveryRequest.fields = {
    type: 'Integer',
    id: 'Long',
    materialInfo: 'string',
    cityIds: '[Integer]',
    priority: 'Integer',
    resourceName: 'string',
    resourceKind: 'Integer',
    startTime: 'Long',
    endTime: 'Long',
    extra: 'string'
}

module.exports = UpdateDeliveryRequest