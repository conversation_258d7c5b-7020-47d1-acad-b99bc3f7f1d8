'use strict'

// @ts-ignore
const Base = require('@dp/pigeon-util').base

class UpdateItemStatusRequest extends Base {
    /* eslint no-useless-constructor: "off" */
    constructor(options) {
        super(options)
        this['$value'] = options
        this['$type'] = 'com.sankuai.dzviewscene.delivery.api.request.manage.UpdateItemStatusRequest'
    }
}

UpdateItemStatusRequest.className = 'com.sankuai.dzviewscene.delivery.api.request.manage.UpdateItemStatusRequest'

UpdateItemStatusRequest.fields = {
    itemIds: '[Long]',
    approvalInfos: '[object]',  // object为com.sankuai.dzviewscene.delivery.api.dto.ApprovalInfoDTO的实例;
    status: 'Integer',
    action: 'Integer',
    approver: 'string'
}

module.exports = UpdateItemStatusRequest