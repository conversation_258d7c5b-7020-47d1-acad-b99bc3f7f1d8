'use strict'

//@ts-ignore
const Base = require('@dp/pigeon-util').base

class ImgGenerateRequest extends Base {
  /* eslint no-useless-constructor: "off" */
  constructor(options) {
    super(options)
    this['$value'] = options
    this['$type'] =
      'com.sankuai.materials.api.model.normalmaterial.ImgGenerateRequest'
  }
}

ImgGenerateRequest.className =
  'com.sankuai.materials.api.model.normalmaterial.ImgGenerateRequest'

ImgGenerateRequest.fields = {
  bizCode: 'string',
  templateId: 'Integer',
  imageDpi: 'Integer',
  imageType: 'string',
  bizDataMap: 'object', // object为一个map;object的key为string
}

export default ImgGenerateRequest
