import { Service, BaseService, DBService } from '@gfe/moa'
import { CommonChangeLog } from '../../entities'

export interface ListQuery {
  where: object | object[]
  select?: any
  skip?: number
  take?: number
  order?: object
}

@Service()
export class BaseDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super()
  }

  // 更新记录
  async updateRecord<T>(
    RecordEntity: {
      new(...args: Array<any>): T
    },
    query: object,
    params: object,
  ) {
    const repository = this.dbService.getRepository(RecordEntity)
    const result = await repository.update(query, params)
    return result
  }

  // 查询记录
  async findRecord<T>(
    RecordEntity: {
      new(...args: Array<any>): T
    },
    query: object,
    params?: object,
  ) {
    const Repository = this.dbService.getRepository(RecordEntity)
    const result = await Repository.findOne(query, params)
    return result
  }

  // 查询记录列表
  async findListRecord<T>(
    RecordEntity: {
      new(...args: Array<any>): T
    },
    query: ListQuery,
  ) {
    const Repository = this.dbService.getRepository(RecordEntity)
    const result = await Repository.find(query)
    return result
  }

  // 添加记录
  async addRecord<T>(
    RecordEntity: {
      new(...args: Array<any>): T
    },
    params: any,
  ) {
    const Repository = this.dbService.getRepository(RecordEntity)
    Repository.manager.transaction(async (manager) => {
      manager.save
    })
    const result = await Repository.save(params)
    return result
  }

  // 添加通用changelog
  async addCommonChangeLog(record: CommonChangeLog) {
    const repository = this.dbService.getRepository(
      CommonChangeLog,
    )
    const res = await repository.save(record)
    return res
  }
}
