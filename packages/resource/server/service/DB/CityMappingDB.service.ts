import { Service, BaseService, DBService } from '@gfe/moa'
import { ListQuery } from '../../interface/db'
import {
  CityMappingEntity
} from '../../entities'


@Service()
export class CityMappingDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super()
  }

  // 查询单个城市映射
  async findCityMapping(query: object, params?: object) {
    const repository = this.dbService.getRepository(
      CityMappingEntity,
    )
    const result = await repository.findOne(query, params)
    return result
  }

  // 查询多个城市映射
  async findCityListMapping(query: ListQuery) {
    const repository = this.dbService.getRepository(
      CityMappingEntity,
    )
    const result = await repository.find(query)
    return result
  }

}
