import { Service, BaseService, DBService } from '@gfe/moa'
import { format } from 'date-fns'
import { ListQuery } from '../../interface/db'
import {
  DeliveryChangeLogEntity
} from '../../entities'


@Service()
export class DeliveryDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super()
  }

  async addDeliveryChangelog(type: number, resourcePositionId: string, deliveryId: number, beforeStatus: string, afterStatus: string, operator: string) {
    const record = new DeliveryChangeLogEntity()
    record.type = type
    record.resourcePositionId = resourcePositionId
    record.deliveryId = deliveryId
    record.beforeStatus = beforeStatus
    record.afterStatus = afterStatus
    record.operator = operator
    await this.addDeliveryChangelogRecord(record)
  }

  // 查询 资源位操作记录
  async findDeliveryChangelogRecord(query: object, params?: object) {
    const repository = this.dbService.getRepository(
      DeliveryChangeLogEntity,
    )
    const result = await repository.findOne(query, params)
    return result
  }

  // 查询 资源位操作记录
  async findDeliveryChangelogListRecord(query: ListQuery) {
    const repository = this.dbService.getRepository(
      DeliveryChangeLogEntity,
    )
    const result = await repository.find(query)
    return result
  }

  // 添加 资源位操作记录
  async addDeliveryChangelogRecord(params: DeliveryChangeLogEntity) {
    const repository = this.dbService.getRepository(
      DeliveryChangeLogEntity,
    )
    repository.manager.transaction(async (manager) => {
      manager.save
    })
    const result = await repository.save(params)
    return result
  }


}
