import { BaseService, DBService, Service } from "@gfe/moa"
import { PermissionEntity } from "../../entities/index"
import { PermissionAction, PermissionBizType, PermissionType } from "../../interface/Permission"
import { FindConditions, In, getManager } from "typeorm"
import { OrgService } from "../authorize/Org.service"
import { BaseDBService } from "./BaseDB.service"

@Service()
export class PermissionDBService extends BaseService {
    constructor(
        protected dbService: DBService,
        protected orgService: OrgService,
        protected baseDBService: BaseDBService,


    ) {
        super()
    }

    // 获取某业务id下所有的权限
    async getPermissionsByBiz(bizId: number, bizType: PermissionBizType, permKey?: string, permList?: PermissionType[]): Promise<PermissionEntity[]> {
        const repository = this.dbService.getRepository(PermissionEntity)

        const qb = repository.createQueryBuilder().andWhere(`biz_type=${bizType}`).andWhere(`biz_id=${bizId}`)
        if (permKey) {
            qb.andWhere(`perm_key= '${permKey}'`)
        }
        if (permList && permList.length) {
            qb.andWhere(`perm_type in (${permList.join(',')})`)
        }
        return await qb.getMany()
    }

    // 获取某业务场景下某个用户/组织架构的全部权限
    async getPermissionsByUser(mis: string, bizType: PermissionBizType, permTypeList: PermissionType[]) {
        const orgPath = await this.orgService.queryOrgPathByMis(mis)
        let orgPathList: number[] = null
        if (orgPath) {
            orgPathList = orgPath.split('-').map((id) => Number(id))
        }
        const baseWhere = {
            permType: In(permTypeList),
            bizType,
        }
        const _where = [{ ...baseWhere, mis }]
        if (orgPathList) {
            _where.push({ ...baseWhere, orgId: In(orgPathList) } as any)
        }
        const scenePermissionList = await this.baseDBService.findListRecord(
            PermissionEntity,
            {
                where: _where,
            }
        )
        return scenePermissionList.map(scene => ({ bizType: scene.bizType, bizId: scene.bizId }))
    }


    // 根据当前权限结构刷新关系表中属于bizId的部分
    async flushPermissionByBiz(bizId: number, bizType: PermissionBizType, permsActions: PermissionAction[]) {
        const permissionEntities = [] as PermissionEntity[]
        const deleteIdList = [] as number[]
        for (const action of permsActions) {
            if (action.action === 'add') {
                if (typeof action.payload === 'string') {
                    const entity = new PermissionEntity()
                    entity.permType = action.perm_type
                    entity.bizId = bizId
                    entity.bizType = bizType
                    entity.mis = action.payload
                    entity.permKey = action.permKey

                    permissionEntities.push(entity)
                } else {
                    const entity = new PermissionEntity()
                    entity.permType = action.perm_type
                    entity.bizId = bizId
                    entity.bizType = bizType
                    entity.orgId = action.payload.id
                    entity.orgPathName = action.payload.path.split('/').join('-')
                    entity.permKey = action.permKey
                    permissionEntities.push(entity)

                }
            } else if (action.action === 'delete') {
                deleteIdList.push(action.payload)
            }
        }

        return await getManager().transaction('SERIALIZABLE', async (transManager) => {
            const repository = transManager.getRepository(PermissionEntity)
            if (permissionEntities.length) {
                await repository.save(permissionEntities)

            }
            if (deleteIdList.length) {
                await repository.delete(deleteIdList)

            }

        })

    }
}