import { Service, BaseService, DBService } from '@gfe/moa'
import { format } from 'date-fns'
import { ListQuery } from '../../interface/db'
import {
  ResourceEntity,
  ResourceChangeLogEntity
} from '../../entities'
import { ResourceChangeLogTypeEnum } from "../../interface/resource"
import { ResourceTemplateEntity } from '../../entities/resourceTemplate.entity'
import { ObjectLiteral, OrderByCondition } from 'typeorm'
export interface WhereParamExperssion {
  query: string
  params?: ObjectLiteral
}
interface GeneralSelectBuilderParams {
  andWhere?: WhereParamExperssion[]
  orWhere?: WhereParamExperssion[]
  order?: OrderByCondition
  take?: number
  skip?: number
}

@Service()
export class ResourceDBService extends BaseService {
  constructor(protected dbService: DBService) {
    super()
  }

  // 更新资源位记录
  async updateResourceRecord(query: object, params: object) {
    const repository = this.dbService.getRepository(
      ResourceEntity,
    )
    const result = await repository.update(query, params)
    return result
  }

  // 查询资源位记录
  async findResourceRecord(query: object, params?: object) {
    const repository = this.dbService.getRepository(
      ResourceEntity,
    )
    const result = await repository.findOne(query, params)
    return result
  }

  // 查询资源位记录列表
  async findResourceListRecord(query: ListQuery) {
    const repository = this.dbService.getRepository(
      ResourceEntity,
    )
    const result = await repository.find(query)
    return result
  }

  // 带template查询资源位，兼容单个或列表
  async findResourceRecordWithTemplate(searchType: "ONE" | "MANY", query: GeneralSelectBuilderParams) {
    const queryBuilder = this.getResourceRecordQueryBuilderWithTemplate(query)
    if (searchType === 'ONE') {
      return await queryBuilder.getOne()
    }
    if (searchType === 'MANY') {
      return await queryBuilder.getMany()
    }
  }

  async getResourceRecordWithTemplateListCount(query: GeneralSelectBuilderParams) {
    const queryBuilder = this.getResourceRecordQueryBuilderWithTemplate(query)
    return await queryBuilder.getCount()

  }

  // 添加 资源位 记录
  async addResourceRecord(params: ResourceEntity) {


    const logRecord = new ResourceChangeLogEntity()
    logRecord.resourcePositionId = params.resourcePositionId
    logRecord.operator = params.creator
    logRecord.type = ResourceChangeLogTypeEnum.ADD
    logRecord.beforeStatus = ''
    logRecord.afterStatus = JSON.stringify(params)
    this.addResourceChangelogRecord(logRecord)

    const repository = this.dbService.getRepository(
      ResourceEntity,
    )
    repository.manager.transaction(async (manager) => {
      manager.save
    })
    const result = await repository.save(params)
    return result
  }

  // 查询 资源位操作记录 部署记录
  async findResourceChangelogRecord(query: object, params?: object) {
    const repository = this.dbService.getRepository(
      ResourceChangeLogEntity,
    )
    const result = await repository.findOne(query, params)
    return result
  }

  // 查询 资源位操作记录 部署记录列表
  async findResourceChangelogListRecord(query: ListQuery) {
    const repository = this.dbService.getRepository(
      ResourceChangeLogEntity,
    )
    const result = await repository.find(query)
    return result
  }

  // 添加 资源位操作记录
  async addResourceChangelogRecord(params: ResourceChangeLogEntity) {
    const repository = this.dbService.getRepository(
      ResourceChangeLogEntity,
    )
    repository.manager.transaction(async (manager) => {
      manager.save
    })
    const result = await repository.save(params)
    return result
  }

  //生成带模版列表查询通用过程
  getResourceRecordQueryBuilderWithTemplate(params: GeneralSelectBuilderParams = {}) {
    const repository = this.dbService.getRepository(
      ResourceEntity,
    )
    let query = repository.createQueryBuilder('res').leftJoinAndMapOne('res.template', ResourceTemplateEntity, 'tmpl', "res.templateId=tmpl.id")

    // 执行各类查询参数

    if (params.orWhere) {
      query = params.orWhere.reduce((pre, now) => {
        return pre.orWhere(now.query, now.params)
      }, query)
    }
    if (params.andWhere) {
      query = params.andWhere.reduce((pre, now) => {
        return pre.andWhere(now.query, now.params)
      }, query)
    }

    if (params.order) {
      query = query.orderBy(params.order)
    }

    if (params.take) {
      query = query.take(params.take)
    }
    if (params.skip) {
      query = query.offset(params.skip)
    }
    return query
  }
}
