import { BaseService, DBService, Service } from "@gfe/moa"
import { SceneEntity } from "../../entities/scene.entity"
import { ListQuery } from "./BaseDB.service"

@Service()
export class SceneDBService extends BaseService {
    constructor(protected dbService: DBService) {
        super()
    }

    async getScenesList(query: ListQuery) {
        const repository = this.dbService.getRepository(
            SceneEntity,
        )
        const result = await repository.find(query)
        return result
    }

    async getSceneById(id: number) {
        const repository = this.dbService.getRepository(SceneEntity)
        const result = await repository.findOne(id)
        return result
    }

    async getSceneListCount(query: ListQuery) {
        const repository = this.dbService.getRepository(
            SceneEntity,
        )
        const result = await repository.count({ where: query.where })
        return result
    }
}