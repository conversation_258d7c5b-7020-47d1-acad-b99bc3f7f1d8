import { Service, BaseService, DBService } from '@gfe/moa'
import { BaseDBService } from '../DB/BaseDB.service'
import { PermissionEntity } from '../../entities/Permission.entity'
import { PermissionType, PermissionBizType } from '../../interface/Permission'
import { In } from 'typeorm'
import { OrgService } from './Org.service'

@Service()
export class BasePermissionService extends BaseService {
  constructor(
    private baseDBService: BaseDBService,
    private orgService: OrgService
  ) {
    super()
  }

  /**
   *  判断操作用户在业务场景下是否具有相应的权限
   * @param mis 操作用户
   * @param bizType 业务类型
   * @param bizId 具体业务类型下的ID
   * @param permTypeList 权限列表
   * @returns
   */
  async judgPermission(
    mis: string,
    bizType: PermissionBizType,
    bizId: number,
    permTypeList: PermissionType[]
  ) {
    try {
      if (!(mis && bizType && bizId && permTypeList?.length)) return false
      const orgPath = await this.orgService.queryOrgPathByMis(mis)
      let orgPathList: number[] = null
      if (orgPath) {
        orgPathList = orgPath.split('-').map((id) => Number(id))
      }
      const baseWhere = {
        permType: In(permTypeList),
        bizType,
        bizId,
      }
      const _where = [{ ...baseWhere, mis }]
      if (orgPathList) {
        _where.push({ ...baseWhere, orgId: In(orgPathList) } as any)
      }
      const scenePermissionList = await this.baseDBService.findListRecord(
        PermissionEntity,
        {
          where: _where,
        }
      )
      return (scenePermissionList?.length || 0) > 0
      // return false
    } catch (err) {
      throw err
    }
  }
}
