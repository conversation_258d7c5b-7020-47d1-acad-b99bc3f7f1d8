import { Service, BaseService } from '@gfe/moa'
import axios from 'axios'
import baAuth from '@mtfe/ba-auth'
import {
  CLIENT_ID,
  CLIENT_SECRET,
  OrgOpenApiHost,
  OrgOpenApiName,
} from '../../config/OrgApi'
import { OrgItem } from '../../interface/Org'

// const ORG_HOST = IS_BETA ? OrgOpenApiHost.TEST : OrgOpenApiHost.PROD
const ORG_HOST = OrgOpenApiHost.PROD

type CreateAuthHeaderOption = {
  method?: 'POST' | 'GET' | 'PUT' | 'DELETE' | 'OPTZIONS' | 'HEAD'
  path: string
  clientId?: string
  clientSecret?: string
}

type TCreateAuthHeader = (
  options: CreateAuthHeaderOption
) => Record<string, string>

const createAuthHeader: TCreateAuthHeader = (options) => {
  return {
    'data-scope': 'tenantId=1;sourceMT',
    ...baAuth.create({
      ...options,
    }),
  }
}

@Service()
export class OrgService extends BaseService {
  private account?: { clientId: string; clientSecret: string } = {
    clientId: CLIENT_ID,
    clientSecret: CLIENT_SECRET,
  }
  /**
   * 根据orgNamePath精确查找org信息
   * @param orgNamePath orgNamePath（精确查询）
   */
  async queryOrgByOrgPathName(orgNamePath: string) {
    if (!orgNamePath) {
      return []
    }
    const orgName = orgNamePath.substring(orgNamePath.lastIndexOf('/') + 1)
    const ret = await axios({
      url: `${ORG_HOST}${OrgOpenApiName.ORG_SEARCH}`,
      method: 'GET',
      params: {
        keyword: orgName,
        searchFields: 'name',
        matchType: 1, // 0 前缀匹配、 1 全匹配
        filter: 'status=1',
      },
      headers: createAuthHeader({
        method: 'GET',
        path: OrgOpenApiName.ORG_SEARCH,
        clientId: this.account.clientId,
        clientSecret: this.account.clientSecret,
      }),
    }).catch((err) => {
      throw err
    })
    const _orgItemsOfRes = (ret && (ret?.data?.data?.items as OrgItem[])) || []
    const _orgItem = _orgItemsOfRes.filter((org) => {
      const newOrgPathName = org.orgNamePath.replace(/-/g, '/')
      return newOrgPathName === `公司/${orgNamePath}`
    })?.[0]
    if (_orgItem) {
      // 仅取符合条件
      return [
        {
          name: _orgItem.name,
          orgId: _orgItem.orgId,
          orgNamePath: _orgItem.orgNamePath,
        },
      ] as OrgItem[]
    }
    return []
  }

  /**
   * 根据mis查询组织链
   * @param mis
   * @returns
   */
  async queryOrgPathByMis(mis: string) {
    try {
      const empOrgInfo = await this.queryEmploysOrgByMis(mis)
      if (!empOrgInfo) {
        return null
      }
      const _urlPath = `${OrgOpenApiName.ORG_SEARCH}/${empOrgInfo.orgId}`
      const ret = await axios({
        url: `${ORG_HOST}${_urlPath}`,
        method: 'GET',
        headers: createAuthHeader({
          method: 'GET',
          path: _urlPath,
          clientId: this.account.clientId,
          clientSecret: this.account.clientSecret,
        }),
      })
      const _orgPath = ret && (ret?.data?.data?.orgPath as string)
      if (_orgPath) {
        return _orgPath.substring(_orgPath.indexOf('-') + 1)
      }
      return ''
    } catch (err) {
      throw err
    }
  }

  /**
   *  根据mis号查询组织架构信息
   * @param mis
   * @returns
   */
  async queryEmploysOrgByMis(mis: string): Promise<OrgItem> {
    const headers = createAuthHeader({
      method: 'GET',
      path: OrgOpenApiName.EMP_SEARCH,
      clientId: this.account.clientId,
      clientSecret: this.account.clientSecret,
    })
    const ret = await axios({
      url: `${ORG_HOST}${OrgOpenApiName.EMP_SEARCH}`,
      method: 'GET',
      params: {
        keyword: mis,
        searchFields: 'mis',
        matchType: 1, // 0 前缀匹配、 1 全匹配
        filter: 'jobStatusId=15', // 在职状态；在职：15，离职：16
      },
      headers,
    }).catch((err) => {
      throw err
    })

    const _emp = ((ret && ret?.data?.data?.items) || [])?.[0]
    if (_emp) {
      return {
        orgId: _emp.orgId,
        name: _emp.orgName,
      }
    }
    return null
  }

  async searchEmploysByMis(params) {
    const headers = createAuthHeader({
      method: 'GET',
      path: '/api/org2/emps/_batch',
      clientId: this.account.clientId,
      clientSecret: this.account.clientSecret,
    })
    const ret = await axios({
      url: 'https://org2.vip.sankuai.com/api/org2/emps/_batch',
      method: 'GET',
      params,
      headers,
    }).catch(() => {})

    return ((ret && ret?.data?.data) || []).map((user) => {
      return {
        mis: user.mis,
        name: user.name,
        orgId: user.orgId,
        orgName: user.orgName,
      }
    })
  }
}
