import { Service, BaseService } from '@gfe/moa'
import { ResourceEntity } from 'server/entities'
import { PermissionType, PermissionBizType } from '../../interface/Permission'
import { ResourceDBService } from '../DB/ResourceDB.service'
import { BasePermissionService } from './BasePermission.service'

@Service()
export class ResourcePermissionService extends BaseService {
  constructor(
    private basePermissionService: BasePermissionService,
    private resourceDBService: ResourceDBService
  ) {
    super()
  }

  /**
   * 根据资源位标识查询资源位信息
   * @param resourcePositionId 资源位标识
   */
  private getResourceInfo(resourcePositionId: string) {
    const resoureItem = this.resourceDBService.findResourceRecord({
      resourcePositionId,
    })
    return resoureItem
  }
  /**
   * mis 是否在资源位ownser字段中
   * @param mis
   * @param resourceItem
   * @returns
   */
  private async misInOwnser(mis: string, resourceItem: ResourceEntity) {
    const ownser =
      (resourceItem?.owners || '').split(',').filter((owner) => owner) || []
    if (ownser.indexOf(mis) > -1) {
      return true
    }
    return false
  }

  /**
   * 资源位权限判断基础方法
   * @param mis 操作mis号
   * @param resourcePositionId 资源位标识
   * @param permTypeList 权限列表
   */
  private async judgResourcePermisse(
    mis: string,
    resourcePositionId: string,
    permTypeList: PermissionType[]
  ) {
    try {
      const resourceItem = await this.getResourceInfo(resourcePositionId)
      if (await this.misInOwnser(mis, resourceItem)) {
        // 兼容资源位管理员在owner的情况，如果mis在ownser直接返回
        return true
      }
      const resourceId = resourceItem.id
      const res = await this.basePermissionService.judgPermission(
        mis,
        PermissionBizType.Resource,
        resourceId,
        permTypeList
      )
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断操作用户是否有访问权限
   * @param mis mis
   * @param resourceId 资源位标识
   * @desc 支持设置mis白名单和Org
   * 操作用户在[使用者、审批者、管理员]
   */
  async canAccessPermission(mis: string, resourcePositionId: string) {
    try {
      const res = await this.judgResourcePermisse(mis, resourcePositionId, [
        PermissionType.Access,
        PermissionType.Approve,
        PermissionType.Manage,
      ])
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断用户是否有审批与驳回权限
   * @param mis
   * @param resourcePositionId 资源位标识
   * @desc 支持设置mis白名单
   * 操作用户在[审批者、管理员]
   */
  async canAprrovePermission(mis: string, resourcePositionId: string) {
    try {
      const res = await this.judgResourcePermisse(mis, resourcePositionId, [
        PermissionType.Approve,
        PermissionType.Manage,
      ])
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断用户是否有更新和下线权限
   * @param mis
   * @param resourcePositionId 资源位标识
   * @desc 支持设置mis白名单
   * 操作用户等于创建者 or 操作用户在[审批者、管理员]
   */
  async canUpdateAndOfflinePermission(
    mis: string,
    creator: string,
    resourcePositionId: string
  ) {
    if (mis === creator) return true
    try {
      const res = await this.judgResourcePermisse(mis, resourcePositionId, [
        PermissionType.Approve,
        PermissionType.Manage,
      ])
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断用户是否具备管理权限
   * @param mis
   * @param resourcePositionId 资源位标识
   * @returns
   * @desc 支持设置mis白名单
   * 操作用户在[管理员]
   */
  async canManagePermission(mis: string, resourcePositionId: string) {
    try {
      const res = await this.judgResourcePermisse(mis, resourcePositionId, [
        PermissionType.Manage,
      ])
      return res
    } catch (err) {
      throw err
    }
  }
}
