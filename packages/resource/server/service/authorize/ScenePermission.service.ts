import { Service, BaseService } from '@gfe/moa'
import { PermissionType, PermissionBizType } from '../../interface/Permission'
import { BasePermissionService } from './BasePermission.service'

@Service()
export class ScenePermissionService extends BaseService {
  constructor(private basePermissionService: BasePermissionService) {
    super()
  }

  /**
   * 判断操作用户是否有查询权限
   * @param mis mis
   * @param sceneId 场景id
   * @desc 支持设置mis白名单和Org
   * 操作用户在[使用者、管理员]
   */
  async canAccessPermission(mis: string, sceneId: number) {
    try {
      const res = await this.basePermissionService.judgPermission(
        mis,
        PermissionBizType.Scene,
        sceneId,
        [PermissionType.Access, PermissionType.Manage]
      )
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断操作用户是否具备更新权限
   * @param mis mis
   * * @param creator 创建者
   * @param sceneId 场景id
   * @desc 仅支持设置mis白名单
   * 操作用户等于创建者
   * 操作用户在[管理员]
   */
  async canUpdatePermission(mis: string, creator: string, sceneId: number) {
    if (mis === creator) return true
    try {
      const res = await this.basePermissionService.judgPermission(
        mis,
        PermissionBizType.Scene,
        sceneId,
        [PermissionType.Manage]
      )
      return res
    } catch (err) {
      throw err
    }
  }

  /**
   * 判断操作用户是否有管理权限
   * @param mis mis
   * @param sceneId 场景id
   * @desc 仅支持设置mis白名单
   * 操作用户在[使用者、管理员]
   */
  async canManagePermission(mis: string, sceneId: number) {
    try {
      const res = await this.basePermissionService.judgPermission(
        mis,
        PermissionBizType.Scene,
        sceneId,
        [PermissionType.Manage]
      )
      return res
    } catch (err) {
      throw err
    }
  }
}
