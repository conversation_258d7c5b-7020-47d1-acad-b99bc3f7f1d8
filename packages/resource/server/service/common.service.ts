import Moa, { BaseService, Service } from '@gfe/moa'
import { Not } from 'typeorm'
import { RPCGroupGeoService, CityInfoMsg, ProvinceCityInfoMsg } from '@mtfe/thrift-api-geo'
import { CityMappingDBService } from "./DB/CityMappingDB.service"
import { OrgOpenApiName } from '../config/OrgApi'
import { orgHttp } from '../utils/orgHttp'
import { getDataBykeyFromArray } from '../utils'
import { PlatformEnum } from '../interface'
import { BaseDBService } from './DB/BaseDB.service'
import { ChangeLogTypeEnum } from '../interface/scene'
import { CommonChangeLog } from '../../server/entities'
const geoService = new RPCGroupGeoService({
  appkey: 'com.sankuai.dzufebiz.manage',
})


@Service()
export class CommonService extends BaseService {
  constructor(
    protected cityMapDBS: CityMappingDBService,
    protected baseDBS: BaseDBService
  ) {
    super()
  }
  async getMTAllCity(ctx: Moa.Context): Promise<CityInfoMsg[]> {
    const squirrel = ctx.sdk.squirrel
    const StoreKey = squirrel.StoreKey
    const key = new StoreKey('resource', 'mtcity', 'all',)


    try {
      const res = await squirrel.get(key)
      if (!res) {
        throw new Error('Failed to get MT all city data')
      }
      return res
    } catch (error) {
      const res = await geoService.listCity()
      squirrel.set(key, res)
      return res
    }

  }

  async getMTProvinceCity(ctx: Moa.Context): Promise<ProvinceCityInfoMsg[]> {
    const squirrel = ctx.sdk.squirrel
    const StoreKey = squirrel.StoreKey
    const key = new StoreKey('resource', 'city', 'province',)
    try {
      const res = await squirrel.get(key)
      if (!res) {
        throw new Error('Failed to get MT province city Data')
      }
      return res
    } catch (error) {
      const res = await geoService.getProvinceCityInfo()
      squirrel.set(key, res)
      return res
    }

  }

  async getCityMapByIds(ctx: Moa.Context, platform: string, cityIds: number[]) {
    let cityMap = {}
    if (platform === PlatformEnum.DP) {
      cityMap = await this.getDPCityMapByIds(ctx, cityIds)
    } else {
      cityMap = await this.getMTCityMapByIds(ctx, cityIds)
    }
    return cityMap
  }
  async getMTCityMapByIds(ctx: Moa.Context, cityIds: number[]) {
    const all = await this.getMTAllCity(ctx)
    const res = {}
    all.filter(city => {
      if (cityIds.includes(city.id)) {
        res[city.id] = city
      }
    })
    return res
  }
  async getDPCityMapByIds(ctx: Moa.Context, cityIds: number[]) {
    const all = await this.getAllDpCity(ctx)
    const res = {}
    all.filter(city => {
      if (cityIds.includes(city.id)) {
        res[city.id] = city
      }
    })
    return res
  }

  async mtCity2DPByPigeon(ctx: Moa.Context, cityid: number[]) {
    const params = cityid.map(city => ctx.pigeon.java.int(city))
    const res = await ctx.pigeon.services.cityService.invoke('getBatchDpCityByMtCity', params)
    return res
  }
  async mtCity2DPByHive(ctx: Moa.Context, cityid: number[]) {

    const sqlQuery = cityid.map(id => {
      return {
        mt_cityid: id
      }
    })
    const res = (await this.cityMapDBS.findCityListMapping({
      where: sqlQuery
    }))

    const cityMap = {}
    res.forEach(i => {
      if (i.dp_cityid) {
        cityMap[i.mt_cityid] = i.dp_cityid
      }
    })


    return cityMap
  }
  async dpCity2MT(ctx: Moa.Context, cityid: number[]) {
    const params = cityid.map(city => ctx.pigeon.java.int(city))
    const res = await ctx.pigeon.services.cityService.invoke('getBatchMtCityByDpCity', params)
    return res
  }

  async getDpCityinfoById(ctx: Moa.Context, cityid: number) {
    const params = ctx.pigeon.java.int(cityid)
    const res = await ctx.pigeon.services.dpCityInfoService.invoke('loadCity', params)
    return res
  }
  async getAllDpCity(ctx: Moa.Context) {

    const squirrel = ctx.sdk.squirrel
    const StoreKey = squirrel.StoreKey
    const key = new StoreKey('resource', 'dpcity', 'all',)
    try {
      const res = await squirrel.get(key)
      if (!res) {
        throw new Error('Failed to get all dp city data')
      }
      return res
    } catch (error) {
      const skips = [0, 1000, 2000, 3000]
      const promises = skips.map(skip => {
        const limit = ctx.pigeon.java.int(1000)
        const _skip = ctx.pigeon.java.int(skip)
        return ctx.pigeon.services.dpCityInfoService.invoke('getOpenCityList', _skip, limit)
      })
      let res = (await Promise.all(promises)).reduce((pre, cur) => {
        return pre.concat(cur)
      }, []).map(i => Object.assign({
        cityId: i.cityId,
        cityName: i.cityName,
        provinceId: i.provinceId,
        parentCityId: i.parentCityId,
        cityOrderId: i.cityOrderId,
        isActiveCity: i.isActiveCity,
        cityEnName: i.cityEnName,
        cityAbbrCode: i.cityAbbrCode,
        isOverseasCity: i.isOverseasCity,
        TuanGouFlag: i.TuanGouFlag,
        cityLevel: i.cityLevel,

      }, { name: i.cityName, id: i.cityId }))

      const mtCitys = await this.getMTAllCity(ctx)
      res = await this.dpCityRank(res, mtCitys)
      squirrel.set(key, res)
      return res

    }

  }
  async getAllDpCityProvince(ctx: Moa.Context) {

    const squirrel = ctx.sdk.squirrel
    const StoreKey = squirrel.StoreKey
    const key = new StoreKey('resource', 'dpcity', 'allbyprovince',)
    try {
      const res = await squirrel.get(key)
      if (!res) {
        throw new Error('Failed to get all dp city province data')
      }
      return res
    } catch (error) {
      const allCity = await this.getAllDpCity(ctx)
      const citys = this.convertCityByParent(allCity)
      const allProvince: any[] = await this.getAllDpProvince(ctx)
      for (const city of citys) {
        const index = allProvince.findIndex(i => i.provinceID === city.provinceId)
        if (index > -1) {
          if (allProvince[index]?.children) {
            allProvince[index]?.children.push(city)
          } else {
            allProvince[index].children = [city]
          }
        }
      }
      const res = allProvince
      squirrel.set(key, res)
      return res

    }
  }
  async getAllDpProvince(ctx: Moa.Context) {
    const squirrel = ctx.sdk.squirrel
    const StoreKey = squirrel.StoreKey
    const key = new StoreKey('resource', 'dpcity', 'province',)
    try {
      const res = await squirrel.get(key)
      if (!res) {
        throw new Error('Failded to get all dp province data')
      }
      return res
    } catch (error) {
      const limit = ctx.pigeon.java.int(34)
      const _skip = ctx.pigeon.java.int(0)
      const res = await ctx.pigeon.services.dpProvinceInfoService.invoke('findAllProvinces', _skip, limit)
      squirrel.set(key, res)
      return res
    }
  }
  convertCityByParent(cityArray) {
    const cityMap = new Map() // 用于存储城市信息的 Map
    const rootCities = [] // 存储根级城市的数组

    // 遍历城市信息数组，将每个城市按照 id 存储到 cityMap 中
    for (const city of cityArray) {
      const cityId = city.cityId
      city.children = [] // 初始化子城市数组
      cityMap.set(cityId, city)
    }

    // 遍历城市信息数组，将每个城市添加到其父级城市的 children 字段中
    for (const city of cityArray) {
      const parentId = city.parentCityId
      if (parentId === 0) {
        // 根级城市
        rootCities.push(city)
      } else {
        const parentCity = cityMap.get(parentId)
        if (parentCity) {
          parentCity.children.push(city)
        }
      }
    }
    return rootCities
  }
  async searchKeyWorldOrg(keyword: string,) {
    const requestUrl = `${OrgOpenApiName.SEARCH}`
    const params = {
      keyword,
      searchFields: 'mis',
    }
    const result = await orgHttp(requestUrl, params)
    return result?.data
  }

  async dpCityRank(dpCitys, mtCitys) {
    const res = await this.cityMapDBS.findCityListMapping({
      mt_cityid: Not(0),
      dp_cityid: Not(0),
    })
    const dpCityMap = {}
    res.forEach(i => {
      dpCityMap[i.dp_cityid] = i
    })

    for (let i = 0; i < dpCitys.length; i++) {
      const element = dpCitys[i]
      const mtId = dpCityMap[element.id]?.mt_cityid
      if (mtId) {
        dpCitys[i].rank = (getDataBykeyFromArray(mtCitys, 'id', mtId) as any)?.rank || '其他'
      } else {
        dpCitys[i].rank = '其他'
      }
    }

    return dpCitys
  }

  async addCommonChangeLog(type: ChangeLogTypeEnum, beforeStatus: string, afterStatus: string, operator: string, extra: string) {
    const record = new CommonChangeLog()
    record.type = type
    record.beforeStatus = beforeStatus
    record.afterStatus = afterStatus
    record.operator = operator
    record.extra = extra
    const res = await this.baseDBS.addCommonChangeLog(record)
    return res
  }
}
