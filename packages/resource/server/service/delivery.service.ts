import Moa, { BaseService, Service } from '@gfe/moa'
import { toJava } from '@dp/pigeon-util'
import { format } from 'date-fns'
import {
  SubmitDeliveryReq,
  QueryByActivityRequest,
  QueryByActivityResponse,
  DeliveryItemStyleTypeEnum,
  DeliveryItemStatusEnum,
  DeliveryActivityDTO,
  DeliveryActivityRes,
  DeliveryItemDTO,
  DeliveryRuleItem,
  UpdateDeliveryRequest,
  OperatorStatus,
  QueryDeliveryInfoResponse,
  QueryDeliveryInfoRequest,
  UpdateItemStatusRequest,
  UpdateItemStatusResponse,
  QueryByItemRequest,
  QueryByItemResponse,
  SubmitDeliveryResponse,
  ScheduleCityItem,
  ScheduleTimeItem,
  ScheduleItem,
  SubmitApprovalFlowConfigModel,
  DeliveryItemActionEnum,
  ApprovalFlowNodeStatus
} from "../interface"
import { AuditFlowModel, AuditFlowNodeModel, AuditFlowNodeType, AuthEnum, PermStructure } from "../interface/resource"
import { ResourceDBService } from './DB/ResourceDB.service'
import { CommonService } from "./common.service"
import { ResourceEntity } from '../entities/resource.entity'
import { OPERATOR_STATUS_CONFIG, OPERATOR_STATUS_AUTH_CONFIG } from '../config/constant'
import { getDataBykeyFromArray, isNecessaryParamsReq, uniqueArr, hasConflictForTimes, checkDeliveryRulesConflict } from '../utils'
import { ResourcePermissionService } from './authorize/ResourcePermission.service'
import { PermissionBizType, PermissionType } from '../interface/Permission'
import { PermissionDBService } from './DB/PermissionDB.service'
const QueryDeliveryInfoRequestJava = require('../pegion/QueryDeliveryInfoRequest')
const QueryByItemRequestjava = require('../pegion/QueryByItemRequest')
const QueryByActivityRequestJava = require('../pegion/QueryByActivityRequest')
const SubmitDeliveryRequestJava = require('../pegion/SubmitDeliveryRequest')
const UpdateDeliveryRequestJava = require('../pegion/UpdateDeliveryRequest')
const SubmitActivityDTO = require('../pegion/SubmitActivityDTO')
const SubmitItemDTOJava = require('../pegion/SubmitItemDTO')
const UpdateItemStatusRequestJava = require('../pegion/UpdateItemStatusRequest')
const ApprovalInfoDTO = require('../pegion/ApprovalInfoDTO')
const SubmitApprovalFlowConfigDTO = require('../pegion/SubmitApprovalFlowConfigDTO')
@Service()
export class DeliveryService extends BaseService {
  constructor(
    protected resourceDBS: ResourceDBService,
    protected commonService: CommonService,
    protected resourcePermissionService: ResourcePermissionService,
    protected permissionDBS: PermissionDBService
  ) {
    super()
  }


  async getDeliveryList(ctx: Moa.Context, query?: QueryByActivityRequest,): Promise<DeliveryActivityRes> {
    const params = new QueryByActivityRequestJava(query)
    const res: QueryByActivityResponse = await ctx.pigeon.services.DeliveryManageService.invoke('queryByActivity', toJava(params))
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.queryByActivity 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    console.log(`调用外部 Pegion 接口：queryByActivity =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(query)};__res__:${JSON.stringify(res)}`)
    const deliveryActivities = res.data?.list || []
    return {
      total: res.data?.pageInfo?.totalCount || 0,
      list: deliveryActivities
    }
  }

  async createDelivery(ctx: Moa.Context, query: SubmitDeliveryReq[], mis?: string) {
    const deliveryActivities = query.map(i => new SubmitActivityDTO(Object.assign({ creator: mis }, i, {
      items: i.deliveryItems?.map(item => new SubmitItemDTOJava(item))
    }, { approvalFlowConfig: i.approvalFlowConfig ? new SubmitApprovalFlowConfigDTO({ ...i.approvalFlowConfig }) : undefined })))
    const b = new SubmitDeliveryRequestJava({
      activities: deliveryActivities
    })
    const params = toJava(b)

    const res: SubmitDeliveryResponse = await ctx.pigeon.services.DeliveryManageService.invoke('submitDelivery', params)
    console.log(`调用外部 Pegion 接口：submitDelivery =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.submitDelivery 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    return true
  }

  async updateDelivery(ctx: Moa.Context, query: UpdateDeliveryRequest) {
    const params = new UpdateDeliveryRequestJava(query)
    const res = await ctx.pigeon.services.DeliveryManageService.invoke('updateDelivery', toJava(params))
    console.log(`调用外部 Pegion 接口: updateDelivery =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.updateDelivery 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    return true

  }

  async getDeliveryById(ctx: Moa.Context, query: QueryDeliveryInfoRequest) {
    const params = new QueryDeliveryInfoRequestJava(query)
    const res: QueryDeliveryInfoResponse = await ctx.pigeon.services.DeliveryManageService.invoke('queryDeliveryInfo', toJava(params))
    console.log(`调用外部 Pegion 接口: queryDeliveryInfo =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.queryDeliveryInfo 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    return res.deliveryActivities || []
  }

  async updateItemStatus(ctx: Moa.Context, query: UpdateItemStatusRequest) {
    const mapStatusToAction = {
      [DeliveryItemStatusEnum.PendingAudit]: DeliveryItemActionEnum.Reset,
      [DeliveryItemStatusEnum.Audited]: DeliveryItemActionEnum.Approve,
      [DeliveryItemStatusEnum.Rejected]: DeliveryItemActionEnum.Reject,
      [DeliveryItemStatusEnum.Offline]: DeliveryItemActionEnum.Offline
    }
    const params = new UpdateItemStatusRequestJava({ approver: query.approver, action: mapStatusToAction[query.status], approvalInfos: query.approvalInfos.map(info => new ApprovalInfoDTO(info)) })
    const res: UpdateItemStatusResponse = await ctx.pigeon.services.DeliveryManageService.invoke('updateItemStatus', toJava(params))
    console.log(`调用外部 Pegion 接口: updateItemStatus =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.updateItemStatus 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    return res.errIds
  }

  // 排期表用，可以查询投放生效和结束的时间
  async queryByItem(ctx: Moa.Context, query: QueryByItemRequest) {
    const params = new QueryByItemRequestjava(query)
    const res: QueryByItemResponse = await ctx.pigeon.services.DeliveryManageService.invoke('queryByItem', toJava(params))
    console.log(`调用外部 Pegion 接口: queryByItem =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
    if (res.code !== 0) {
      throw new Error(`pigeon.DeliveryManageService.queryByItem 请求失败:${res.msg || JSON.stringify(res)}`)
    }
    return res.deliveryActivities
  }

  confirmOperationStatus(deliveryItem: DeliveryItemDTO, resourceItem: ResourceEntity, mis: string): OperatorStatus {
    const byStatus = OPERATOR_STATUS_CONFIG[deliveryItem.status]
    let auth = AuthEnum.NONE
    if (resourceItem?.owners.includes(mis + ',')) {
      auth = AuthEnum.ADMIN
    }
    const byAuth = OPERATOR_STATUS_AUTH_CONFIG[auth]
    return {
      canEdit: !!byStatus.canEdit && !!byAuth.canEdit,
      canOffline: !!byStatus.canOffline && !!byAuth.canOffline,
      canDenyAudit: !!byStatus.canDenyAudit && !!byAuth.canDenyAudit,
      canAudit: !!byStatus.canAudit && !!byAuth.canAudit,
    }
  }

  async assembleDeliveryList(deliveryActivities: DeliveryActivityDTO[], mis: string, rpList: ResourceEntity[]): Promise<DeliveryActivityDTO[]> {
    const resourceMap: { [propName: string]: ResourceEntity } = {}
    rpList.forEach(resource => {
      resourceMap[resource.resourcePositionId] = resource
    })

    const activitiesMapByRP = new Map<string, DeliveryActivityDTO[]>()
    deliveryActivities.forEach(item => {
      if (activitiesMapByRP.has(item.resourcePositionId)) {
        activitiesMapByRP.get(item.resourcePositionId).push(item)
      } else {
        activitiesMapByRP.set(item.resourcePositionId, [item])
      }
    })

    for (const [resourcePositionId, activities] of activitiesMapByRP.entries()) {
      const canUpdateResourcePosition = await this.resourcePermissionService.canAprrovePermission(mis, resourcePositionId)
      activities.forEach(act => {
        act.deliveryItems.forEach((item: DeliveryRuleItem) => {
          const byStatus = OPERATOR_STATUS_CONFIG[item.status]
          const canUpdate = item.creator === mis || canUpdateResourcePosition
          let auditByMe = undefined
          if (item.approvalFlow && item.approvalFlow.length) {
            auditByMe = item.approvalFlow.some(it => it.status === ApprovalFlowNodeStatus.Auditing && it.candidateApprover.includes(mis))
          } else {
            auditByMe = true
          }
          item.operator = {
            canEdit: !!byStatus.canEdit && canUpdate,
            canOffline: !!byStatus.canOffline && canUpdate,
            canDenyAudit: auditByMe && !!byStatus.canDenyAudit && canUpdateResourcePosition, // 创建者本身不能审核
            canAudit: auditByMe && !!byStatus.canAudit && canUpdateResourcePosition,
          }
        })
      })
    }

    deliveryActivities.forEach(item => {
      const deliveryItems = item.deliveryItems as DeliveryRuleItem[]
      const statusList = [0, 0, 0, 0] // 待审核、通过、驳回、删除
      for (const deliveryItem of deliveryItems) {
        switch (deliveryItem.status) {
          case DeliveryItemStatusEnum.PendingAudit:
            statusList[0]++
            break
          case DeliveryItemStatusEnum.Audited:
            statusList[1]++
            break
          case DeliveryItemStatusEnum.Rejected:
            statusList[2]++
            break
          case DeliveryItemStatusEnum.Offline:
            statusList[3]++
            break
        }
        // 根据当前投放规则状态、访问人的权限设置可操作状态表
        if (!resourceMap[item.resourcePositionId]) {
          throw new Error(`${item.resourcePositionId} 资源位不存在`)
        }
        if (resourceMap[item.resourcePositionId]?.extra?.permission === undefined) {
          deliveryItem.operator = this.confirmOperationStatus(deliveryItem, resourceMap[item.resourcePositionId], mis)
        }
      }
      item.styleType = resourceMap[item.resourcePositionId]?.multiDelivery ? DeliveryItemStyleTypeEnum.Multi : DeliveryItemStyleTypeEnum.Single
      item.statusInfo = `${statusList[0] ? statusList[0] + '条待审核;' : ''}${statusList[1] ? statusList[1] + '条审核通过;' : ''}${statusList[2] ? statusList[2] + '条被驳回;' : ''}${statusList[3] ? statusList[3] + '条已下线;' : ''}`
      item.editable = statusList[1] === 0 && statusList[3] !== deliveryItems.length
    })
    return deliveryActivities
  }

  async confirmResourceAuth(resourceItem: ResourceEntity, mis: string) {
    const owners = JSON.parse(resourceItem.owners || '{}')
    const auth = owners[mis]
    const isAuth = auth === AuthEnum.ADMIN || auth === AuthEnum.EDIT
    return {
      auth: isAuth,
      msg: isAuth ? 'success' : `请联系管理员添加权限：${Object.keys(owners).join(',')}`
    }
  }

  /**
   * 美团城市体系 => 点评城市体系
   * @param ctx
   * @param deliveryItems  投放规则
   * @returns
   */
  async tranformCityMT2DP(ctx: Moa.Context, deliveryItems: DeliveryRuleItem[]) {
    let cityList = []
    const errorCityList = []

    deliveryItems.forEach(item => {
      cityList = cityList.concat(item.cityIds || [])
    })
    cityList = uniqueArr(cityList)
    const cityMap = await this.commonService.mtCity2DPByHive(ctx, cityList)

    const dpDeliveryItems = deliveryItems.map(item => {
      // 筛掉不能转的城市，并记录
      const cityIds = item.cityIds.map(city => {
        if (!cityMap[city]) errorCityList.push(city)
        return cityMap[city]
      }).filter(i => i)

      return cityIds.length ?
        Object.assign(
          {}, item, { cityIds },
        )
        :
        undefined
    }).filter(i => i)

    return { dpDeliveryItems, errorCityList }
  }

  /**
 * 点评城市体系 => 美团城市体系
 * @param ctx
 * @param deliveryActivities
 * @returns
 */
  async tranformCityDP2MT(ctx: Moa.Context, deliveryActivities: DeliveryActivityDTO[], dpRpList: string[]) {
    let cityList = []

    deliveryActivities.forEach(activity => {
      if (dpRpList.includes(activity.resourcePositionId)) {
        activity.deliveryItems.forEach(item => {
          cityList = cityList.concat(item.cityIds || [])
        })
      }
    })

    cityList = uniqueArr(cityList)
    const cityMap = await this.commonService.dpCity2MT(ctx, cityList)

    const res = deliveryActivities.map(activity => {
      if (dpRpList.includes(activity.resourcePositionId)) {
        activity.deliveryItems = activity.deliveryItems.map(item => {
          const mtCityIds: number[] = item.cityIds.map(city => cityMap[city])
          return Object.assign(
            {}, item, { cityIds: mtCityIds, mtCityIds, dpCityIds: item.cityIds },
          )
        })
      } else {
        activity.deliveryItems = activity.deliveryItems.map(item => {
          return Object.assign(
            {}, item, { mtCityIds: item.cityIds, dpCityIds: [] },
          )
        })
      }
      return activity
    })

    return res
  }

  async mt2dpCity(ctx: Moa.Context, cityList: number[]) {
    if (!cityList?.length) return []
    const cityMap = await this.commonService.mtCity2DPByPigeon(ctx, cityList)
    return cityList.map(city => {
      if (!cityMap[city]) {
        throw Error(`美团城市ID：${city} 无法转换为点评城市 ID`)
      }
      return cityMap[city]
    })
  }

  /**
   * 校验投放活动记录
   * @param deliveryItems
   */
  verifyDelivery(deliveryItems: DeliveryItemDTO[]) {
    deliveryItems.forEach(item => {
      isNecessaryParamsReq(['cityIds', 'startTime', 'endTime'], item)
      if (item.endTime <= item.startTime) {
        throw new Error(`投放结束时间应大于开始时间`)
      }
    })
  }


  /**
   * 排期表数据处理
   * type === 1
   * 同一城市冲突的时间在同一行展示
   */
  async formatScheduleOneLine(ctx, deliveryActivities: DeliveryActivityDTO[], resourceItem: ResourceEntity) {
    const { timeInterval, platform } = resourceItem
    const scheduleCityItems: ScheduleCityItem[] = []
    let citys: number[] = []
    deliveryActivities?.forEach((delivery) => {
      const { extra } = delivery
      delivery.deliveryItems?.forEach((item) => {
        const { startTime, endTime, activityId, cityIds, status, id } = item
        let name = ''
        try {
          name = JSON.parse(extra || '{}')?.liveName || ''
        } catch (error) {
          name = ''
        }

        const key =
          format(startTime, 'yyyy-MM-dd HH:mm:SS') +
          '-' +
          format(endTime, 'yyyy-MM-dd HH:mm:SS')
        cityIds.forEach((city) => {
          citys.push(city)
          let cityItem = getDataBykeyFromArray(
            scheduleCityItems,
            'cityId',
            city
          )
          if (!cityItem) {
            cityItem = {
              name: '',
              cityId: city,
              timeInterval,
              children: [
                {
                  key,
                  startTime,
                  endTime,
                  hasConflict: false,
                  contained: false,
                  children: [
                    {
                      id,
                      name,
                      extra,
                      activityId,
                      status,
                    },
                  ],
                },
              ],
            }
            scheduleCityItems.push(cityItem)
          } else {
            const timeList = cityItem.children as ScheduleTimeItem[]
            const timeItem = getDataBykeyFromArray(timeList, 'key', key)
            if (!timeItem) {
              (cityItem.children as ScheduleTimeItem[]).push({
                key,
                startTime,
                endTime,
                hasConflict: false,
                contained: false,
                children: [
                  {
                    id,
                    name,
                    extra,
                    activityId,
                    status,
                  },
                ],
              })
            } else {
              timeItem.children.push({
                id,
                name,
                extra,
                activityId,
                status,
              })
            }
          }
        })
      })
    })

    citys = uniqueArr(citys)
    const cityMap = await this.commonService.getCityMapByIds(ctx, platform, citys)

    /**
     * 计算冲突
     */
    scheduleCityItems.forEach((cityItem) => {
      cityItem.name = cityMap[cityItem.cityId]?.name
      cityItem.children.sort((a, b) => a.startTime - b.startTime)
      const timeList = cityItem.children
      for (let i = 0; i < timeList.length; i++) {
        const ele1 = timeList[i] as ScheduleTimeItem
        for (let j = i + 1; j < timeList.length; j++) {
          const ele2 = timeList[j] as ScheduleTimeItem

          // startTime 时间相同，必包含
          if (ele1.startTime === ele2.startTime) {
            if (ele1.endTime > ele2.endTime) {
              (timeList[j] as ScheduleTimeItem).contained = true;
              (timeList[i] as ScheduleTimeItem).hasConflict = true
            } else {
              (timeList[i] as ScheduleTimeItem).contained = true;
              (timeList[j] as ScheduleTimeItem).hasConflict = true
            }
          } else if (ele1.endTime > ele2.startTime) {
            // 必有交集
            // ele2 被 ele1 包含
            if (ele1.endTime >= ele2.endTime) {
              (timeList[j] as ScheduleTimeItem).contained = true;
              (timeList[i] as ScheduleTimeItem).hasConflict = true
            }
            // 有重叠区域
            if (ele1.endTime < ele2.endTime) {
              (timeList[i] as ScheduleTimeItem).hasConflict = true;
              (timeList[j] as ScheduleTimeItem).hasConflict = true
            }
          }
        }
      }
    })

    return scheduleCityItems
  }

  /**
 * 排期表数据处理
 * type === 2
 * 同一城市冲突的时间在分行展示
 */
  async formatScheduleMoreLine(ctx, deliveryActivities: DeliveryActivityDTO[], resourceItem: ResourceEntity) {
    const { timeInterval, platform } = resourceItem
    const scheduleCityItems: ScheduleCityItem[] = []
    let citys: number[] = []

    //
    deliveryActivities?.forEach((delivery) => {
      const { extra } = delivery
      delivery.deliveryItems?.forEach((item) => {
        const { startTime, endTime, activityId, cityIds, status, id } = item
        let name = ''
        try {
          name = JSON.parse(extra || '{}')?.liveName || ''
        } catch (error) {
          name = ''
        }

        const key =
          format(startTime, 'yyyy-MM-dd HH:mm:SS') +
          '-' +
          format(endTime, 'yyyy-MM-dd HH:mm:SS')

        const curScheduleTimeItem: ScheduleItem = {
          key,
          name,
          activityId,
          id,
          status,
          startTime,
          endTime,
          extra
        }

        cityIds.forEach((city) => {
          citys.push(city)
          const cityItem = getDataBykeyFromArray(
            scheduleCityItems,
            'cityId',
            city
          )
          if (!cityItem) {
            scheduleCityItems.push({
              name: '',
              cityId: city,
              timeInterval,
              children: [curScheduleTimeItem]
            })
          } else {
            (cityItem.children as ScheduleItem[]).push(curScheduleTimeItem)
          }
        })
      })
    })

    citys = uniqueArr(citys)
    const cityMap = await this.commonService.getCityMapByIds(ctx, platform, citys)

    scheduleCityItems.forEach((cityItem) => {
      cityItem.name = cityMap[cityItem.cityId]?.name
      cityItem.children.sort((a, b) => a.startTime - b.startTime)
      const children = cityItem.children as ScheduleItem[]
      const scheduleGroups = [[]] as ScheduleItem[][]
      for (let index = 0; index < children.length; index++) {
        const scheduleItem = children[index]
        let flag = false
        for (let j = 0; j < scheduleGroups.length; j++) {
          const element = scheduleGroups[j]
          if (Array.isArray(element) && element.every(i => checkDeliveryRulesConflict(i, scheduleItem))) {
            element.push(scheduleItem)
            flag = true
            break
          }
        }
        if (!flag) {
          scheduleGroups.push([scheduleItem])
        }
      }

      // 标记第一行冲突
      if (scheduleGroups.length > 1) {
        for (const rule of scheduleGroups[0]) {
          for (let index = 1; index < scheduleGroups.length; index++) {
            const rulesPendingCheck = scheduleGroups[index]
            for (const otherRule of rulesPendingCheck) {
              if (!checkDeliveryRulesConflict(rule, otherRule)) {
                rule.conflict = true
                break
              }
            }
            if (rule.conflict) {
              break
            }
          }
        }

      }

      cityItem.children = scheduleGroups
    })

    return scheduleCityItems
  }

  /**
   * 根据资源位的permStruct和权限关系组装审批流
   */
  async getDeliveryApprovalFlow(resourceItem: ResourceEntity, controlKey: string) {
    const permStruct = resourceItem.extra.permission.permStruct
    const template = resourceItem.extra.permission.auditFlowTemplate[controlKey]
    const approvalFlow: Array<Array<string>> = []
    const managerList = (await this.permissionDBS.getPermissionsByBiz(resourceItem.id, PermissionBizType.Resource, undefined, [PermissionType.Manage])).map(perm => perm.mis).filter(Boolean)
    const owners = resourceItem.owners.split(',').filter(Boolean)
    const finalManagerList = [...new Set(managerList.concat(owners))]
    for (const nodeTempalte of template) {
      const nodeApprovers = await this.getApprovalFlowNodeByPermStructAndTemplate(permStruct, nodeTempalte, resourceItem.id)
      approvalFlow.push([...new Set(nodeApprovers.concat(finalManagerList))])
    }

    return {
      candidateApprover: approvalFlow,
      levelCnt: approvalFlow.length
    } as SubmitApprovalFlowConfigModel

  }

  private async getApprovalFlowNodeByPermStructAndTemplate(permStruct: PermStructure, auditFlowNodeTemplate: AuditFlowNodeModel, resourceId: number) {
    if (auditFlowNodeTemplate.type.toString() === AuditFlowNodeType.PermStruct.toString()) {
      return await this.processApprovalFlowNodePermStructType(permStruct, auditFlowNodeTemplate, resourceId)
    } else if (auditFlowNodeTemplate.type.toString() === AuditFlowNodeType.XN.toString()) {
      // 本期先不做处理
      throw new Error(`审批层级类型(${auditFlowNodeTemplate.type})未定义，请联系管理员进行处理。`)
    }

    throw new Error(`审批层级类型(${auditFlowNodeTemplate.type})未定义，请联系管理员进行处理。`)

  }

  private async processApprovalFlowNodePermStructType(permStruct: PermStructure, auditFlowNodeTemplate: AuditFlowNodeModel, resourceId: number) {
    const permDetail = permStruct[auditFlowNodeTemplate.userRole]
    if (!permDetail) {
      throw new Error(`权限${auditFlowNodeTemplate.userRole}查找出错，请联系管理员进行处理。`)
    }
    const permKey = permDetail.permKey
    const permType = permDetail.permType
    const permissionList = await this.permissionDBS.getPermissionsByBiz(resourceId, PermissionBizType.Resource, permKey, [permType])

    return permissionList.map(perm => perm.mis).filter(Boolean)

  }

}
