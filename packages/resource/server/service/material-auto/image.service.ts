import Moa, { BaseService, Service } from '@gfe/moa'
import { toJava } from '@dp/pigeon-util'
import pigeon from '@dp/pigeon-client'
import ImgGenerateRequestJava from '../../pegion/material-auto/ImgGenerateRequest'
import { MUSHROOM_BIZ_CODE } from '../../config/constant'
import { ImgGenerateReq, ImgGenerateRes } from '../../interface/material'

@Service()
export class ImageAutoService extends BaseService {
  constructor() {
    super()
  }
  /**
   * 通过蘑菇平台自动生成图片
   * @param req
   * @returns
   */
  async getImageFromMushroom<T>(req?: ImgGenerateReq<T>) {
    if (!req?.templateId) {
      throw new Error('缺少templateId')
    }
    if (!req.imageDpi) {
      req.imageDpi = 32
    }
    if (!req.imageType) {
      req.imageType = 'jpeg'
    }
    const params = {
      bizCode: MUSHROOM_BIZ_CODE,
      ...req,
    }
    const imgGenerateRequest = new ImgGenerateRequestJava(params)
    try {
      const res = (await pigeon.services.MushroomService.invoke(
        'imgGenerate',
        toJava(imgGenerateRequest)
      )) as ImgGenerateRes
      if (res?.code !== 200) {
        throw new Error(res?.message || '生成图片失败')
      }
      return res?.content
    } catch (err) {
      throw err
    }
  }
}
