import Moa, { BaseService, Service } from '@gfe/moa'
import { IS_PROD } from "../config/env"

import axios from 'axios'

@Service()
export class MaterialService extends BaseService {
  constructor() {
    super()
  }
  async getLiveInfo(id: number) {
    const domain = IS_PROD ? 'https://m.dianping.com' : 'https://m.51ping.com'
    const res = await axios.get(domain + '/carnation/campaign/extLive/activityWithBigCard/queryById', { params: { deliveryId: id } })
    return res.data
  }

}
