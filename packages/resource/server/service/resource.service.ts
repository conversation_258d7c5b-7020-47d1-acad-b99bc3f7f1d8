import { BaseService, Service } from '@gfe/moa'
import { ResourceRes, ResourceItemCreateReqDTO, Boolean2NumEnum, DeliveryActivityDTO, ResourceItemDTO, ResourceItemDetailDTO, PermStructure } from "../interface"
import { ResourceDBService, WhereParamExperssion } from './DB/ResourceDB.service'
import { ResourceEntity } from '../entities/resource.entity'
import { DEFAULT_TIME_INTERVAL } from '../config/constant'
import { uniqueArr, getAvatar, string2Ary } from '../utils'
import { PermissionDBService } from './DB/PermissionDB.service'
import { PermissionBizType, PermissionType, PermissionStructureModel } from '../interface/Permission'
import { In, Like } from 'typeorm'
import { ChangeLogTypeEnum, PermissionStructureDTO } from '../interface/scene'
import { diffPermissionStructure } from '../utils/permission'
import { CommonService } from './common.service'

@Service()
export class ResourceService extends BaseService {
  constructor(
    protected resourceDBS: ResourceDBService,
    protected permissionDBS: PermissionDBService,
    protected commonService: CommonService
  ) {
    super()
  }

  async getResourceList(query?: any, skip = 0, take = 10) {
    const resList = await this.resourceDBS.findResourceListRecord({
      where: query,
      skip,
      take,
      order: { createTime: 'DESC' }
    })
    const allList = await this.resourceDBS.findResourceListRecord({
      where: query,
    })
    const list = resList.map(i => Object.assign(i))
    const res: ResourceRes = {
      list,
      total: allList.length,
    }
    return res
  }

  async getResourceListWithPermission(ssomis: string, query?: any, skip = 0, take = 10) {
    // 获取具有Access及以上权限的资源位列表
    const permissionList = await this.permissionDBS.getPermissionsByUser(ssomis, PermissionBizType.Resource, [PermissionType.Access, PermissionType.Approve, PermissionType.Manage])
    // 获取具有资源位owner权限的列表
    const ownerActivityList = await this.resourceDBS.findResourceListRecord({
      where: `find_in_set('${ssomis}',owners) > 0`
    })
    const ownerActivityIdList = ownerActivityList.map(item => item.id)
    // 合并并去重两个资源位id列表，得到所有有权限的资源位
    const ids = [...new Set(permissionList.map(perm => perm.bizId).concat(ownerActivityIdList))]
    const baseWhere = {
      id: In(ids)
    }
    let finalWhere = {}
    if (query?.where && Array.isArray(query.where)) {
      finalWhere = query.where.map(w => {
        return {
          ...w,
          ...baseWhere
        }
      })
    } else if (query?.where && typeof query.where === 'object') {
      finalWhere = {
        ...query.where,
        ...baseWhere
      }
    }

    const resList = await this.resourceDBS.findResourceListRecord({
      where: finalWhere,
      skip,
      take,
      order: { createTime: 'DESC' }
    })
    const allList = await this.resourceDBS.findResourceListRecord({
      where: finalWhere,
    })
    const list = resList.map(i => Object.assign(i, {
      owners: i.owners.split(',').filter(i => i).map(mis => {
        return {
          mis,
          avatar: getAvatar(mis)
        }
      })
    }))
    const res: ResourceRes = {
      list,
      total: allList.length,
    }
    return res
  }

  async getResourceListWithTemplate(orWhere: WhereParamExperssion[] = [], andWhere: WhereParamExperssion[] = [], skip = 0, take = 10) {
    const resList = await this.resourceDBS.findResourceRecordWithTemplate("MANY", {
      andWhere,
      orWhere,
      skip,
      take
    }) as ResourceEntity[]
    const total = await this.resourceDBS.getResourceRecordWithTemplateListCount({
      orWhere,
      skip,
      take
    })
    const list: ResourceItemDTO[] = resList.map(i => {
      const item: ResourceItemDTO = Object.assign(i, {
        templateInfo: i?.template?.template_info || "{}",
        owners: i.owners.split(',').filter(i => i).map(mis => {
          return {
            mis,
            avatar: getAvatar(mis)
          }
        }),
        template: undefined
      })
      return item

    })
    const res: ResourceRes = {
      list,
      total,
    }
    return res
  }

  async getResourceListByIds(ids: string[]) {
    const sqlQuery = ids.map(id => {
      return {
        resourcePositionId: id
      }
    })
    const allList = await this.resourceDBS.findResourceListRecord({
      where: sqlQuery,
    })

    return allList
  }

  async createReourcePosition(query: ResourceItemCreateReqDTO, mis: string) {
    const resourceRecord = new ResourceEntity()
    resourceRecord.resourcePositionId = query.resourcePositionId
    resourceRecord.creator = mis
    resourceRecord.name = query.name
    resourceRecord.owners = mis + ',' + (query.owners ??= '')
    resourceRecord.platform = query.platform
    resourceRecord.system = query.system
    resourceRecord.business = query.business
    resourceRecord.page = query.page
    resourceRecord.imgUrl = query.imgUrl
    resourceRecord.multiDelivery = query.multiDelivery ??= Boolean2NumEnum.FALSE
    resourceRecord.deleted = Boolean2NumEnum.FALSE
    resourceRecord.materialModel = query.materialModel
    resourceRecord.dslInfo = query.dslInfo
    resourceRecord.extra = JSON.parse(query.extra)
    resourceRecord.timeInterval = query.timeInterval || DEFAULT_TIME_INTERVAL
    const res = this.resourceDBS.addResourceRecord(resourceRecord)
    return res
  }
  async getResourceByRPIdWithTemplate(resourcePositionId: string) {

    const res = await this.resourceDBS.findResourceRecordWithTemplate("ONE", {
      orWhere: [{ query: 'res.resourcePositionId = :resourcePositionId', params: { resourcePositionId } }]
    }) as ResourceEntity
    return {
      ...res,
      templateInfo: res.template?.template_info || '{}',
      template: undefined,
      owners: res.owners.split(',').filter(i => i).map(mis => {
        return {
          mis,
          avatar: getAvatar(mis)
        }
      }),
    } as ResourceItemDetailDTO
  }
  async getResourceByRPId(resourcePositionId: string) {
    const resourceItem = await this.resourceDBS.findResourceRecord({
      resourcePositionId
    })
    return resourceItem
  }

  async addAuth(resourcePositionId: string, owners: string) {
    const resourceItem = await this.resourceDBS.updateResourceRecord({
      resourcePositionId
    }, {
      owners
    })
    return resourceItem
  }

  async getRPMapByActivities(list: DeliveryActivityDTO[]) {
    if (!list.length) return {}
    const ids = uniqueArr(list.map(i => i.resourcePositionId))
    const allList = await this.getResourceListByIds(ids)
    const map = {}
    allList.forEach(i => {
      map[i.resourcePositionId] = i
    })

    return map
  }

  async isRPAdmin(resourcePositionId: string, mis: string) {
    const resource = await this.getResourceByRPId(resourcePositionId)
    if (!resource) {
      throw Error(`${resourcePositionId}资源位不存在`)
    }
    if (!string2Ary(resource.owners).includes(mis) && resource.creator !== mis) {
      throw Error(`${mis} 无 ${resourcePositionId} 资源位权限`)
    }
    return true
  }

  // 获取当前资源位的权限结构
  async getPermissionStructure(rpId: number, permStruct: PermStructure) {
    const permissions = await this.permissionDBS.getPermissionsByBiz(rpId, PermissionBizType.Resource)
    const permKeys = Object.keys(permStruct || {})
    if (!permKeys.length) {
      throw new Error("当前资源位未定义权限结构")
    }
    const permissionStructure: PermissionStructureModel = {}
    permKeys.forEach((key: string) => {
      const curPermission = permissions.filter(perm => perm.permKey === key)
      permissionStructure[key] = curPermission

    })
    return permissionStructure
  }

  async updateResourcePermission(resourceItem: ResourceEntity, perms: PermissionStructureDTO) {
    const nowPermStru = await this.getPermissionStructure(resourceItem.id, resourceItem.extra?.permission?.permStruct)
    const permissionActions = diffPermissionStructure(perms, nowPermStru, resourceItem.extra.permission.permStruct)
    await this.permissionDBS.flushPermissionByBiz(resourceItem.id, PermissionBizType.Resource, permissionActions)
    return permissionActions
  }

  async addResourceChangeLog(type: ChangeLogTypeEnum, rpId: number, activityId: number, desc: string, operator: string, beforeStatus: string, afterStatus: string) {
    return await this.commonService.addCommonChangeLog(type, beforeStatus, afterStatus, operator, JSON.stringify({
      rpId,
      activityId,
      desc
    }))
  }
}
