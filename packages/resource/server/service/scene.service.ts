import { toJava } from "@dp/pigeon-util"
import Moa, { BaseService, Service } from "@gfe/moa"
import { SubmitSceneDeliveryRequest, SubmitSceneDeliveryResponse, QuerySceneDeliveryRequest, QuerySceneDeliveryResponse, LiveInfoExportRequest, LiveInfoExportResponse, DeliveryRuleItem, DeliveryItemStatusEnum, DeliverySceneActivityDTO, PermStructure } from "../interface"
import { SceneDBService } from "./DB/SceneDB.service"
import { ListQuery } from "./DB/BaseDB.service"
import { PermissionDBService } from "./DB/PermissionDB.service"
import { PermissionBizType, PermissionStructureModel, PermissionType } from "../interface/Permission"
import { ChangeLogTypeEnum, PermissionStructureDTO } from "../interface/scene"
import { OrgService } from "./authorize/Org.service"
import { In } from "typeorm"
import { BasePermissionService } from "./authorize/BasePermission.service"
import { CommonService } from "./common.service"
import { diffPermissionStructure } from "../utils/permission"

const SubmitSceneDeliveryRequestToJava = require('../pegion/SubmitSceneDeliveryRequest')
const QuerySceneDeliveryRequestToJava = require('../pegion/QuerySceneDeliveryRequest')
const SubmitActivityDTO = require('../pegion/SubmitActivityDTO')
const UpdateDeliveryRequestJava = require('../pegion/UpdateDeliveryRequest')
const SubmitItemDTO = require('../pegion/SubmitItemDTO')
const ExportLiveInfoRequestToJava = require('../pegion/ExportLiveInfoRequest')
const SubmitApprovalFlowConfigDTO = require('../pegion/SubmitApprovalFlowConfigDTO')
@Service()
export class SceneService extends BaseService {
    constructor(
        protected sceneDBS: SceneDBService,
        protected permissionDBS: PermissionDBService,
        protected orgService: OrgService,
        protected basePermissionService: BasePermissionService,
        protected commonService: CommonService,
    ) {
        super()
    }
    async createSceneDelivery(ctx: Moa.Context, query?: SubmitSceneDeliveryRequest): Promise<boolean> {
        const ssomis: string =
            ctx.state.initialData.userInfo &&
            ctx.state.initialData.userInfo.username
        query.activities.forEach(act => {
            act.items = act.items.map(item => new SubmitItemDTO(item))

        })
        query.activities = query.activities.map(i => {
            const obj: any = Object.assign({ creator: ssomis }, i)
            if (i.approvalFlowConfig) {
                obj.approvalFlowConfig = new SubmitApprovalFlowConfigDTO({ ...i.approvalFlowConfig })
            }
            return new SubmitActivityDTO(obj)
        })
        const params = new SubmitSceneDeliveryRequestToJava(query)
        const res: SubmitSceneDeliveryResponse = await ctx.pigeon.services.DeliveryManageService.invoke('submitSceneDelivery', toJava(params))
        if (res.code !== 0) {
            throw new Error(`pigeon.DeliveryManageService.submitSceneDelivery 请求失败:${res.msg || JSON.stringify(res)}`)
        }
        console.log(`调用外部 Pegion 接口：submitSceneDelivery =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(query)};__res__:${JSON.stringify(res)}`)
        return true
    }
    async querySceneDelivery(ctx: Moa.Context, query?: QuerySceneDeliveryRequest) {
        const params = new QuerySceneDeliveryRequestToJava(query)
        const res: QuerySceneDeliveryResponse = await ctx.pigeon.services.DeliveryManageService.invoke('querySceneDelivery', toJava(params))
        if (res.code !== 0) {
            throw new Error(`pigeon.DeliveryManageService.querySceneDelivery 请求失败:${res.msg || JSON.stringify(res)}`)
        }
        console.log(`调用外部 Pegion 接口：querySceneDelivery =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(query)};__res__:${JSON.stringify(res)}`)
        const ssomis: string =
            ctx.state.initialData.userInfo &&
            ctx.state.initialData.userInfo.username

        let hasManagePermission = false
        if (res.data.list.length) {
            hasManagePermission = await this.basePermissionService.judgPermission(ssomis, PermissionBizType.Scene, res.data.list[0].sceneId, [PermissionType.Manage])

        }
        res.data.list.forEach(delivery => {
            delivery.operation = {
                canEdit: hasManagePermission
            }
            delivery.deliveryActivities.forEach(act => {
                const deliveryItems = act.deliveryItems as DeliveryRuleItem[]
                const statusList = [0, 0, 0, 0] // 待审核、通过、驳回、删除

                deliveryItems.forEach(deliveryItem => {
                    switch (deliveryItem.status) {
                        case DeliveryItemStatusEnum.PendingAudit:
                            statusList[0]++
                            break
                        case DeliveryItemStatusEnum.Audited:
                            statusList[1]++
                            break
                        case DeliveryItemStatusEnum.Rejected:
                            statusList[2]++
                            break
                        case DeliveryItemStatusEnum.Offline:
                            statusList[3]++
                            break
                    }

                })

                act.statusInfo = `${statusList[0] ? statusList[0] + '条待审核;' : ''}${statusList[1] ? statusList[1] + '条审核通过;' : ''}${statusList[2] ? statusList[2] + '条被驳回;' : ''}${statusList[3] ? statusList[3] + '条已下线;' : ''}`
            })
        })

        return {
            pageInfo: res.data.pageInfo,
            list: await this.transDPCity2MT(ctx, res.data.list)
        }

    }

    async liveInfoExport(ctx: Moa.Context, query?: LiveInfoExportRequest) {
        const params = new ExportLiveInfoRequestToJava(query)
        const res: LiveInfoExportResponse = await ctx.pigeon.services.DeliveryManageService.invoke('exportLiveInfo', toJava(params))
        if (res.code !== 0) {
            throw new Error(`pigeon.DeliveryManageService.liveInfoExport 请求失败:${res.msg || JSON.stringify(res)}`)
        }
        console.log(`调用外部 Pegion 接口：liveInfoExport =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(query)};__res__:${JSON.stringify(res)}`)
        return res.url
    }

    // 带权限控制
    async getScenesListWithPermission(ssomis: string, query: ListQuery) {
        // 权限控制
        const permissionList = await this.permissionDBS.getPermissionsByUser(ssomis, PermissionBizType.Scene, [PermissionType.Access, PermissionType.Manage])
        const ids = [...new Set(permissionList.map(perm => perm.bizId))]
        const scenes = await this.sceneDBS.getScenesList({
            ...query,
            where: {
                ...query.where,
                id: In(ids)
            }
        })
        const totalCount = await this.sceneDBS.getSceneListCount({
            ...query,
            where: {
                ...query.where,
                id: In(ids)
            }
        })
        return { list: scenes, total: totalCount }
    }

    async getSceneById(sceneId: number) {
        return await this.sceneDBS.getSceneById(sceneId)
    }

    // 获取当前场景的权限结构
    async getPermissionStructure(sceneId: number, permStruct: PermStructure) {
        const permissions = await this.permissionDBS.getPermissionsByBiz(sceneId, PermissionBizType.Scene)
        const permKeys = Object.keys(permStruct || {})
        if (!permKeys.length) {
            throw new Error("当前资源位未定义权限结构")
        }
        const permissionStructure: PermissionStructureModel = {}
        permKeys.forEach((key: string) => {
            const curPermission = permissions.filter(perm => perm.permKey === key)
            permissionStructure[key] = curPermission

        })
        return permissionStructure
    }

    async updateScenePermission(sceneId: number, perms: PermissionStructureDTO) {
        const scene = await this.getSceneById(sceneId)
        if (!scene) {
            throw new Error(`场景(id: ${sceneId})不存在`)
        }
        const nowPermStru = await this.getPermissionStructure(scene.id, scene.extra?.permission?.permStruct)
        const permissionActions = diffPermissionStructure(perms, nowPermStru, scene.extra.permission.permStruct)

        await this.permissionDBS.flushPermissionByBiz(scene.id, PermissionBizType.Scene, permissionActions)

        return permissionActions
    }

    async updateDelivery(ctx: Moa.Context, query: UpdateDeliveryRequest) {
        const params = new UpdateDeliveryRequestJava(query)
        const res = await ctx.pigeon.services.DeliveryManageService.invoke('updateDelivery', toJava(params))
        console.log(`调用外部 Pegion 接口: updateDelivery =>__traceid__:${ctx?.header?.['m-traceid']}; __req__:${JSON.stringify(params)};__res__:${JSON.stringify(res)}`)
        if (res.code !== 0) {
            throw new Error(`pigeon.DeliveryManageService.updateDelivery 请求失败:${res.msg || JSON.stringify(res)}`)
        }
        return true

    }
    async transDPCity2MT(ctx: Moa.Context, arr: DeliverySceneActivityDTO[]) {
        let citiesNeedToChange = []
        arr.forEach(item => {
            item.deliveryActivities.forEach(act => {
                if (act.resourcePositionId.startsWith('dp')) {
                    act.deliveryItems.forEach(delivery => {
                        citiesNeedToChange = citiesNeedToChange.concat(delivery.cityIds)
                    })
                }
            })
        })
        const transRes = await this.commonService.dpCity2MT(ctx, citiesNeedToChange)

        arr.forEach(item => {
            item.deliveryActivities.forEach(act => {
                if (act.resourcePositionId.startsWith('dp')) {
                    act.deliveryItems.forEach(delivery => {
                        delivery.cityIds = delivery.cityIds.map(cityId => transRes[cityId] || cityId)
                    })
                }
            })
        })
        return arr
    }
    async addSceneChangeLog(type: ChangeLogTypeEnum, sceneId: number, activityId: number, desc: string, operator: string, beforeStatus: string, afterStatus: string) {
        return await this.commonService.addCommonChangeLog(type, beforeStatus, afterStatus, operator, JSON.stringify({
            sceneId,
            activityId,
            desc
        }))
    }
}