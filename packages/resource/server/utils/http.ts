import { HttpException } from '@gfe/moa'

export interface HTTPResObj<T> {
  code: number
  msg: string
  data: T
}

export function setHttpResult<T>(
  data: T,
  code = 200,
  msg = 'success',
): HTTPResObj<T> {
  return {
    data,
    code,
    msg,
  }
}

export function setHttpError(err: Error | HttpException) {
  if (!err) {
    return new HttpException('Internal Server Error', 500)
  }
  return new HttpException(err.message, 500)
}
