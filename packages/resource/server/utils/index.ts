import Moa from '@gfe/moa'
import { ScheduleItem, ScheduleTimeItem } from '../interface'

export * from './http'
/**
 * 删除对象的空值key
 * @param obj 简单对象
 */
export function deleteEmptyValueKey(obj: object) {
  const objCopy = JSON.parse(JSON.stringify(obj))
  Object.keys(objCopy).forEach((key) => {
    if (!objCopy[key]) {
      delete objCopy[key]
    }
  })

  return objCopy
}

export function uniqueArr(arr: Array<any>) {
  return Array.from(new Set(arr))
}

export function getAvatar(mis: string) {
  return `https://serverless.sankuai.com/dx-avatar/?type=img&mis=${mis.trim()}`
}
export function isEmpty(obj) {
  if (typeof obj === 'undefined' || obj == null || obj === '') {
    return true
  } else {
    return false
  }
}

export function isNecessaryParamsReq(paramsArr: Array<string>, query: object) {
  return paramsArr
    .map((param) => {
      if (isEmpty(query[param])) {
        throw new Error(`未填写${param}`)
      } else {
        return false
      }
    })
    .every((isEmpty) => isEmpty === false)
}

/**
 *
 * @param arr 要查询的数组
 * @param key key 名
 * @param value value 值
 */
export function getDataBykeyFromArray<T>(
  arr: Array<T>,
  key: string,
  value: any,
) {
  let res: T
  Array.isArray(arr) &&
    arr.forEach((item) => {
      if (item[key] == value) {
        res = item
      }
    })
  return res
}

export function string2Ary(str: string) {
  return str.split(',').filter(i => i)
}

/**
 * 计算两个时间段是否有冲突
 * @param time1
 * @param time2
 * @returns
 */
export function hasConflictForTimes(
  time1: ScheduleTimeItem | ScheduleItem,
  time2: ScheduleTimeItem | ScheduleItem,
) {
  // 1. time1 包含 time2
  if (time1.startTime >= time2.startTime && time1.startTime <= time2.endTime) {
    return true
  }
  // 2. time2 包含 time1
  if (time1.endTime >= time2.startTime && time1.endTime <= time2.endTime) {
    return true
  }
  //  3. time1 和 time2 有交集
  if (time2.startTime >= time1.startTime && time2.startTime <= time1.endTime) {
    return true
  }
  //  4. time1 和 time2 有交集
  if (time2.endTime >= time1.startTime && time2.endTime <= time1.endTime) {
    return true
  }
  return false
}
interface ITimeRange {
  startTime?: number
  endTime?: number
}
export function checkDeliveryRulesConflict(ruleA: ITimeRange, ruleB: ITimeRange) {
  // 保证earlierRule的开始时间早于laterRule
  let earlierRule: DeliveryItemDTO
  let laterRule: DeliveryItemDTO
  if (ruleA.startTime <= ruleB.startTime) {
    earlierRule = ruleA
    laterRule = ruleB
  } else {
    earlierRule = ruleB
    laterRule = ruleA
  }
  // 只有一种没有冲突的情况，就是早开始的规则在下一个规则开始前就结束，其他情况一定有冲突
  if (earlierRule.endTime <= laterRule.startTime) {
    return true
  }
  return false
}

export function logError(ctx: Moa.Context, key: string, error: unknown) {
  const errMsg = error instanceof Error ? error.message : typeof error === 'object' ? JSON.stringify(error) : error
  const mTraceId = ctx?.header?.['m-traceid']
  const query = typeof ctx.query === 'object' ? JSON.stringify(ctx.query) : ctx.query
  const err = new Error(`m_trace_id: ${mTraceId}, msg: ${errMsg}, query: ${query}`)

  console.log(err)
  ctx.cat.logError(key, err)
}