/*
 * @Author: guowei
 * @Date: 2021-12-07 15:36:16
 * @LastEditors: guowei
 * @LastEditTime: 2023-10-11 11:10:30
 * @Description:
 */
import axois from 'axios'
const CryptoJS = require('crypto-js')

import { CLIENT_ID, CLIENT_SECRET } from '../config/OrgApi'

// https://km.sankuai.com/page/121665947
function getBAInfo(uri: string, method: string) {
  const timespan = new Date().toUTCString()
  const string_to_sign = method + ' ' + uri + '\n' + timespan
  const hash = CryptoJS.HmacSHA1(string_to_sign, CLIENT_SECRET)
  const signature = CryptoJS.enc.Base64.stringify(hash)
  const Authorization = 'MWS' + ' ' + CLIENT_ID + ':' + signature
  return {
    'data-scope': 'tenantId=1;source=MT',
    Date: timespan,
    Authorization,
  }
}

export async function orgHttp(
  requestUrl: string,
  params: any,
  type = 'get',
): Promise<any> {
  let _requestUrl = ''
  const host = 'https://org2.vip.sankuai.com'
  _requestUrl = `${host}${requestUrl}`
  if (type === 'get') {
    const BAInfo = await getBAInfo(requestUrl, 'GET')
    return axois
      .get(_requestUrl, {
        headers: BAInfo,
        params: params,
      })
      .then((res) => {
        if (res && res.data) {
          return res.data
        }
      })
      .catch((e) => {
        throw new Error(`调用ORG Get接口失败: ${e}`)
      })
  }
  if (type === 'post') {
    const BAInfo = await getBAInfo(requestUrl, 'POST')
    return axois
      .post(_requestUrl, params, {
        headers: BAInfo,
      })
      .then((res) => {
        if (res && res.data) {
          return res.data
        }
      })
      .catch((e) => {
        throw new Error(`调用Org Post接口失败: ${e}`)
      })
  }
}
