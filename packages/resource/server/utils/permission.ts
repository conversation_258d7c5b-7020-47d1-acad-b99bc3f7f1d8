import { PermissionStructureDTO } from "../interface/scene"
import { PermissionAction, PermissionStructureModel, PermissionType } from "../interface/Permission"
import { PermStructure, PermStructureType } from "../interface"

export function diffPermissionStructure(nxtPerm: PermissionStructureDTO, nowPerm: PermissionStructureModel, permStru: PermStructure) {
    const permissionActions: PermissionAction[] = []
    const permKeys = Object.keys(permStru)
    permKeys.forEach((key: string) => {
        const nextPerms = nxtPerm[key] || []
        const nowPerms = nowPerm[key] || []
        if (permStru[key].permType === PermissionType.Manage) {
            if (nextPerms.length === 0) {
                throw new Error('修改管理员权限时，不能将所有管理员的权限都删除')
            }
        }
        const config = permStru[key]
        if (!config) {
            return
        }
        const checkedMap = new Set()
        nextPerms.forEach((nxt) => {
            if (config.type === PermStructureType.ORG) {
                const findPerm = nowPerms.find(perm => perm.orgId && perm.orgId === (nxt as { id: number }).id)
                if (findPerm) {
                    checkedMap.add(findPerm.orgId)
                } else {
                    permissionActions.push({
                        action: 'add',
                        perm_type: config.permType,
                        payload: nxt,
                        permKey: key,
                    })
                }
            } else if (config.type === PermStructureType.MIS) {
                const findPerm = nowPerms.find(perm => perm.mis && perm.mis === nxt)
                if (findPerm) {
                    checkedMap.add(findPerm.mis)
                } else {
                    permissionActions.push({
                        action: 'add',
                        perm_type: config.permType,
                        payload: nxt,
                        permKey: key
                    })
                }
            }
        })
        nowPerms.forEach(cur => {
            if (config.type === PermStructureType.ORG) {
                if (!checkedMap.has(cur.orgId)) {
                    permissionActions.push({
                        action: 'delete',
                        payload: cur.id,
                    })
                }
            } else if (config.type === PermStructureType.MIS) {
                if (!checkedMap.has(cur.mis)) {
                    permissionActions.push({
                        action: 'delete',
                        payload: cur.id,
                    })
                }
            }
        })
    })
    return permissionActions
}