import { resolveService } from '@gfe/moa'
import { OrgService } from '../../server/service/authorize/Org.service'

async function main() {
  const orgService = resolveService<OrgService>(OrgService)
  // 美团/到店事业群/平台技术部/到店综合研发部/休闲娱乐研发组/前端研发组
  // 美团/到店事业群/平台技术部/到店综合研发部/Life Event研发组/前端研发组
  const orgInfo = await orgService.queryOrgByOrgPathName(
    '美团/到店事业群/平台技术部/到店综合研发部'
  )
  console.log(`queryOrgByOrgPathName`, orgInfo)

  const empOrgInfo = await orgService.queryEmploysOrgByMis('chenkaiming03')
  console.log('queryEmploysOrgByMis', empOrgInfo)

  const orgPath = await orgService.queryOrgPathByMis('chenkaiming03')
  console.log('queryOrgPathIdByMis', orgPath)
}

main()
