import { resolveService } from '@gfe/moa'
import { initCommonService } from '@gfe/moa/lib/core/service'
import { ResourcePermissionService } from '../../server/service/authorize/ResourcePermission.service'
import { getAppConfig } from '../../server/config/TestDbConfig'

async function main() {
  const moaConfig = await getAppConfig()
  await initCommonService(moaConfig as any)
  const resourcePermissionService = resolveService(ResourcePermissionService)
  const resourcePositionId = 'dp.app.medic.channel.livecard'
  // // 访问权限
  // const access = await resourcePermissionService.canAccessPermission(
  //   'tangjiaming',
  //   resourcePositionId
  // )
  // console.log('access', access)

  // 审批权限
  // const aprroveAndReject = await resourcePermissionService.canAprrovePermission(
  //   'tangjiaming',
  //   resourcePositionId
  // )
  // console.log('aprroveAndReject', aprroveAndReject)

  const canUpdateAndOffline =
    await resourcePermissionService.canUpdateAndOfflinePermission(
      'tangjiaming',
      'tangjiaming',
      resourcePositionId
    )
  console.log('canUpdateAndOffline', canUpdateAndOffline)

  const canManage = await resourcePermissionService.canManagePermission(
    'chenkaiming03',
    resourcePositionId
  )
  console.log('canManage', canManage)
}

main()
