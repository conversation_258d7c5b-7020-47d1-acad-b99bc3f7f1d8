import { resolveService } from '@gfe/moa'
import { initCommonService } from '@gfe/moa/lib/core/service'
import { ScenePermissionService } from '../../server/service/authorize/ScenePermission.service'
import { getAppConfig } from '../../server/config/TestDbConfig'

async function main() {
  const moaConfig = await getAppConfig()
  await initCommonService(moaConfig as any)
  const s = resolveService(ScenePermissionService)
  const orgAcessRes = await s.canAccessPermission('chenkaiming03', 1)
  const misAcessRes = await s.canAccessPermission('tangjiaming', 1)
  const misNotAcessRes = await s.canAccessPermission('pengyiting', 1)
  const misNoExistAcessRes = await s.canAccessPermission('fafaf', 1)
  console.log(
    'canAccessPermission',
    orgAcessRes,
    misAcessRes,
    misNotAcessRes,
    misNoExistAcessRes
  )
}

main()
