{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2017",
    "removeComments": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "outDir": "./build",
    "sourceMap": true,
    "noImplicitAny": false,
    "allowJs": true,
    "baseUrl": "./",
    "incremental": true,
    "lib": [
      "ESNext",
      "DOM"
    ]
  },
  "include": [
    "./*.ts",
    "./**/*.ts",
    "thrift"
  ],
  "exclude": [
    "node_modules",
    "test"
  ],
  "baseUrl": "./",
  "paths": {
    "entities/*": ["entities/*"],
    "server/*": ["server/*"],
  }
}
